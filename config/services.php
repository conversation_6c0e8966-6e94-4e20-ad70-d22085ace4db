<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'keycloak' => [
        'client_id' => env('KEYCLOAK_CLIENT_ID'),
        'client_secret' => env('KEYCLOAK_CLIENT_SECRET'),
        'redirect' => env('KEYCLOAK_REDIRECT_URI'),
        'base_url' => env('KEYCLOAK_BASE_URL'),
        'realms' => env('KEYCLOAK_REALM'),
        'admin_username' => env('KEYCLOAK_ADMIN_USERNAME'),
        'admin_password' => env('KEYCLOAK_ADMIN_PASSWORD'),

        'company_group_prefix' => env('KEYCLOAK_COMPANY_GROUP_PREFIX'),
        'app_prefix' => env('KEYCLOAK_CLIENT_PREFIX'),
        'max_concurrent_requests' => env('MAX_CONCURRENT_REQUESTS', 50),
    ],

    'micro-services' => [
        'subscription_url'  => env('SUBSCRIPTION_URL'),
        'saas_url'          => env('SAAS_BASE_URL'),
    ],
];
