#!/bin/bash

# Implement JWT Middleware for All Services
# This script implements the JWT middleware for all requested services

# Configuration
SERVICES=("customer-service-v12" "catalogue-service-v12" "payment-service-v12")
BASE_DIR="services"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        "PROGRESS") echo -e "${CYAN}[PROGRESS]${NC} $message" ;;
    esac
}

# Create controller for a service
create_controller() {
    local service=$1
    local service_name=$(echo $service | sed 's/-service-v12//' | sed 's/-/_/g')
    local controller_name="${service_name^}Controller"
    
    print_status "PROGRESS" "Creating controller for $service..."
    
    mkdir -p "$BASE_DIR/$service/app/Http/Controllers/Api/V1"
    
    cat > "$BASE_DIR/$service/app/Http/Controllers/Api/V1/${controller_name}.php" << EOF
<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\JwtAuthHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * ${controller_name} with JWT Authentication
 * 
 * Example controller demonstrating JWT middleware usage in $service
 */
class ${controller_name} extends Controller
{
    use JwtAuthHelper;

    /**
     * Get user profile information
     *
     * @param Request \$request
     * @return JsonResponse
     */
    public function profile(Request \$request): JsonResponse
    {
        \$userInfo = \$this->getAuthUser(\$request);
        \$oldSsoUserId = \$this->getOldSsoUserId(\$request);
        \$roles = \$this->getUserRoles(\$request);
        \$isAdmin = \$this->isAdmin(\$request);
        \$tokenExpiration = \$this->getTokenExpiration(\$request);

        return response()->json([
            'success' => true,
            'data' => [
                'user' => \$userInfo,
                'old_sso_user_id' => \$oldSsoUserId,
                'roles' => \$roles,
                'is_admin' => \$isAdmin,
                'token_expiration' => \$tokenExpiration,
                'service' => '$service'
            ]
        ]);
    }

    /**
     * Get token information
     *
     * @param Request \$request
     * @return JsonResponse
     */
    public function tokenInfo(Request \$request): JsonResponse
    {
        \$userId = \$this->getCurrentUserId(\$request);
        \$username = \$this->getCurrentUsername(\$request);
        \$email = \$this->getCurrentUserEmail(\$request);
        \$oldSsoUserId = \$this->getOldSsoUserId(\$request);
        \$expiresInMinutes = \$this->getTokenExpiresInMinutes(\$request);
        \$isExpired = \$this->isTokenExpired(\$request);

        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => \$userId,
                'username' => \$username,
                'email' => \$email,
                'old_sso_user_id' => \$oldSsoUserId,
                'expires_in_minutes' => \$expiresInMinutes,
                'is_expired' => \$isExpired,
                'service' => '$service'
            ]
        ]);
    }

    /**
     * Get Old SSO mapping information
     *
     * @param Request \$request
     * @return JsonResponse
     */
    public function oldSsoMapping(Request \$request): JsonResponse
    {
        \$keycloakUserId = \$this->getCurrentUserId(\$request);
        \$oldSsoUserId = \$this->getOldSsoUserId(\$request);
        \$username = \$this->getCurrentUsername(\$request);

        return response()->json([
            'success' => true,
            'data' => [
                'keycloak_user_id' => \$keycloakUserId,
                'old_sso_user_id' => \$oldSsoUserId,
                'username' => \$username,
                'mapping_status' => \$oldSsoUserId ? 'mapped' : 'not_mapped',
                'service' => '$service'
            ]
        ]);
    }

    /**
     * Admin dashboard (requires admin role)
     *
     * @param Request \$request
     * @return JsonResponse
     */
    public function dashboard(Request \$request): JsonResponse
    {
        \$userProfile = \$this->getUserProfile(\$request);
        \$auditContext = \$this->createAuditContext(\$request, 'admin_dashboard_access');

        return response()->json([
            'success' => true,
            'data' => [
                'message' => 'Welcome to ${service_name^} Admin Dashboard',
                'user_profile' => \$userProfile,
                'audit_context' => \$auditContext,
                'service' => '$service'
            ]
        ]);
    }

    /**
     * Health check with authentication
     *
     * @param Request \$request
     * @return JsonResponse
     */
    public function healthCheck(Request \$request): JsonResponse
    {
        \$userId = \$this->getCurrentUserId(\$request);
        \$oldSsoUserId = \$this->getOldSsoUserId(\$request);

        return response()->json([
            'success' => true,
            'data' => [
                'status' => 'healthy',
                'service' => '$service',
                'authenticated_user' => \$userId,
                'old_sso_user_id' => \$oldSsoUserId,
                'timestamp' => now()->toIso8601String()
            ]
        ]);
    }
}
EOF

    print_status "SUCCESS" "Controller created for $service"
}

# Add routes to a service
add_routes() {
    local service=$1
    local service_name=$(echo $service | sed 's/-service-v12//' | sed 's/-/_/g')
    local controller_name="${service_name^}Controller"
    
    print_status "PROGRESS" "Adding JWT routes for $service..."
    
    # Add routes to api.php
    cat >> "$BASE_DIR/$service/routes/api.php" << EOF

// JWT Authentication Examples for $service
use App\Http\Controllers\Api\V1\\${controller_name};

// Routes requiring JWT authentication (no specific roles)
Route::middleware(['jwt.auth'])->group(function () {
    Route::get('/v1/${service_name}/profile', [${controller_name}::class, 'profile']);
    Route::get('/v1/${service_name}/token-info', [${controller_name}::class, 'tokenInfo']);
    Route::get('/v1/${service_name}/old-sso-mapping', [${controller_name}::class, 'oldSsoMapping']);
    Route::get('/v1/${service_name}/health', [${controller_name}::class, 'healthCheck']);
});

// Routes requiring admin role
Route::middleware(['jwt.auth:admin'])->group(function () {
    Route::get('/v1/${service_name}/dashboard', [${controller_name}::class, 'dashboard']);
});
EOF

    print_status "SUCCESS" "Routes added for $service"
}

# Main implementation function
implement_jwt_for_service() {
    local service=$1
    
    print_status "INFO" "🚀 Implementing JWT middleware for $service"
    echo "=================================================="
    
    # Check if service directory exists
    if [ ! -d "$BASE_DIR/$service" ]; then
        print_status "ERROR" "Service directory $BASE_DIR/$service does not exist"
        return 1
    fi
    
    # Create controller
    create_controller "$service"
    
    # Add routes
    add_routes "$service"
    
    print_status "SUCCESS" "✅ JWT middleware implementation completed for $service"
    echo ""
}

# Main execution
main() {
    print_status "INFO" "🛠️  JWT Middleware Implementation for Multiple Services"
    echo "========================================================"
    echo ""
    
    print_status "INFO" "Services to implement:"
    for service in "${SERVICES[@]}"; do
        echo "  - $service"
    done
    echo ""
    
    # Implement for each service
    for service in "${SERVICES[@]}"; do
        implement_jwt_for_service "$service"
    done
    
    echo "========================================================"
    print_status "INFO" "📋 Implementation Summary"
    echo ""
    print_status "SUCCESS" "✅ Completed Services:"
    for service in "${SERVICES[@]}"; do
        echo "  - $service"
        echo "    • JwtDecoder utility: ✅"
        echo "    • KeycloakJwtAuth middleware: ✅"
        echo "    • JwtAuthHelper trait: ✅"
        echo "    • Example controller: ✅"
        echo "    • JWT routes: ✅"
        echo ""
    done
    
    print_status "INFO" "🎯 Next Steps:"
    echo "  1. Test the JWT middleware with valid tokens"
    echo "  2. Update bootstrap/app.php for remaining services"
    echo "  3. Add service-specific business logic to controllers"
    echo "  4. Configure rate limiting and security headers"
    echo ""
    
    print_status "INFO" "📚 Documentation:"
    echo "  - JWT Middleware Guide: services/admin-service-v12/docs/JWT_MIDDLEWARE_GUIDE.md"
    echo "  - Token Refresh API Guide: services/auth-service-v12/docs/TOKEN_REFRESH_API_GUIDE.md"
    echo ""
    
    print_status "SUCCESS" "🎉 JWT Middleware Implementation Complete!"
}

# Run the implementation
main
