<?php

namespace App\Jobs;

use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\{Log,DB};

class MigrateCompanyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $company;
    protected $baseUrl;
    protected $realm;
    protected $clientId;
    protected $clientSecret;
    protected $companyPrefix;
    protected $token;
    protected $tokenExpiresAt;
    protected $clientPrefix;

    /**
     * Create a new job instance.
     */
    public function __construct($company)
    {
        $this->company = $company; // Plain data object, should be serializable
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realms');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
        $this->companyPrefix = config('services.keycloak.company_group_prefix');
        $this->clientPrefix = config('services.keycloak.app_prefix');
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        ini_set('max_execution_time', '300');
        $client = new Client(); // Create the HTTP client instance within the handle method

        $this->checkAndRefreshToken($client);
        try {
            // check if company already exists
            $check = $this->checkCompany($client, $this->company->company_id);
            if (!empty($check)) {
                Log::info("Company ID: {$this->company->company_id} already exists in Keycloak");
                return;
            }else{
                $response = $this->createParentGroup($client, $this->token);
                // $organizationResponse = $this->createOrganization($client, $this->token);
                // $group_id = $this->getKeycloakCompanyId($client, $this->company->company_id);
                // $subGroup = $this->createSubGroup($client, $this->token, $group_id);
                // $this->assignRolesToGroup($client, $this->token, $group_id);

                if ($response !== true) {
                    Log::error('Company Migration failed', ['company_id' => $this->company->company_id, 'error' => $response]);
                } else {
                    Log::info("Migrated company ID: {$this->company->company_id} as group");
                }

                // if ($organizationResponse !== true) {
                //     Log::error('Organization Migration failed', ['company_id' => $this->company->company_id, 'error' => $organizationResponse]);
                // } else {
                //     Log::info("Migrated organization ID: {$this->company->company_id} as organization");
                // }
            }
        } catch (\Exception $e) {
            // Handle any exceptions that occur during the migration
            Log::error('Failed to migrate company', ['company_id' => $this->company->company_id, 'error' => $e->getMessage()]);

            // Throw the exception to let Laravel's queue system retry the job
            throw $e;
        }
    }

    private function checkAndRefreshToken(Client $client)
    {
        if (!$this->token || time() >= $this->tokenExpiresAt) {
            $response = $client->post($this->baseUrl . '/realms/' . $this->realm . '/protocol/openid-connect/token', [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $this->token = $data['access_token'];
            $this->tokenExpiresAt = time() + $data['expires_in'];
        }
    }

    // check if company already exists
    private function checkCompany(Client $client, $companyId)
    {
        $response = $client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/groups?max=1&search=' . $this->companyPrefix . $companyId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = json_decode($response->getBody()->getContents(), true);
        if (empty($body) || !isset($body[0]['id'])) {
            return null;
        }
        return $body[0]['id'];
    }

    private function createParentGroup(Client $client, $token)
    {
        $response = retry(3, function () use ($client, $token) {
            return $client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/groups', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'name' => $this->companyPrefix . $this->company->company_id,
                    "attributes" => [
                        "company_name" => [$this->company->company_name],
                        "type" => [$this->company->type]
                    ]
                ]
            ]);
        }, 200);

        $statusCode = $response->getStatusCode();
        $responseBody = $response->getBody()->getContents();

        if ($statusCode == 201) {
            return true;
        } else {
            return $responseBody; // Return the error message for logging
        }
    }

    private function createOrganization(Client $client, $token)
    {
        $response = retry(3, function () use ($client, $token) {
            // get organization
            $response = $client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/organizations?exact=true&max=1&search=' . $this->company->company_id, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json',
                ]
            ]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (empty($body)) {
                return $client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/organizations', [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $token,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'name' => $this->company->company_id,
                        'alias' => $this->company->company_id,
                        'attributes' => [
                            'company_id' => [$this->company->company_id],
                            'company_name' => [$this->company->company_name],
                            'type' => [$this->company->type],
                            'address_line_1' => [$this->company->address_line_1],
                            'address_line_2' => [$this->company->address_line_2],
                            'city' => [$this->company->city],
                            'state' => [$this->company->state],
                            'zip' => [$this->company->zip_code],
                            'country' => [$this->company->country],

                        ],
                        'domains' => [
                            ['name' => $this->company->domain ? $this->company->domain : $this->company->company_id.'auth.futurescapetech.com'],
                        ],
                        'enabled' => true
                    ]
                ]);
            }
        }, 200);

        $statusCode = $response->getStatusCode();
        $responseBody = $response->getBody()->getContents();

        if ($statusCode == 201) {
            return true;
        } else {
            return $responseBody; // Return the error message for logging
        }
    }

    private function getKeycloakCompanyId(Client $client,  $companyId)
    {
        $response = $client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/groups?max=1&search=' . $this->companyPrefix . $companyId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = json_decode($response->getBody()->getContents(), true);
        if (empty($body) || !isset($body[0]['id'])) {
            return null;
        }
        return $body[0]['id'];
    }

    private function assignRolesToGroup(Client $client, $token, $group_id)
    {
        // admin/realms/FSTech/groups/dc7c619e-3d35-44ce-8cc2-40a9abff46c2/role-mappings/clients/990dd100-ac0d-4d7e-86b4-398b9ab9c718
        $response = $client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/groups/' . $group_id . '/role-mappings/realm-management', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    private function getApplications()
    {
        return DB::connection('old_sso')->table('applications')->get();
    }

    private function generateClientId($application)
    {
        // Generate a unique client ID based on the company and application details
        $companyId = $application->company_id;
        $appId = $application->app_id;
        return $this->companyPrefix . $companyId . "_" . $this->clientPrefix . $appId;
    }

    private function createSubGroup(Client $client, $token, $groupId)
    {
        $applications = $this->getApplications();

        foreach ($applications as $application) {
            $subGroupName = $this->generateClientId($application);
            $subGroupId = $this->postSubGroup($client, $token, $subGroupName, $groupId);
            if ($subGroupId == null) {
                Log::error('Failed to create sub group', ['group_id' => $groupId, 'error' => $response]);
            } else {

                $roles = [];
                if($application->app_id == 2){
                    $roles = ['member','staff','admin','manager','auditor','support','seabreeze_staff','View Only','editor'];
                }
                elseif($application->app_id == 5){
                    $roles = ['master','member','staff','gatekeeper','editor'];
                }else{
                    $roles = ['editor'];
                }

                foreach ($roles as $role) {
                    $innerGroupId = $this->postSubGroup($client, $token, $role, $subGroupId);
                    if ($innerGroupId == null) {
                        Log::error('Failed to create inner group', ['group_id' => $subGroupId, 'error' => $response]);
                    }
                }
            }
        }
    }

    private function postSubGroup($client, $token, $subGroupName, $groupId){
        $response = retry(3, function () use ($client, $token, $groupId, $subGroupName) {
            return $client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/groups/' . $groupId . '/children', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'name' => $subGroupName
                ]
            ]);
        }, 200);

        $statusCode = $response->getStatusCode();
        $responseBody = json_decode($response->getBody()->getContents(), true);

        if (empty($responseBody) || !isset($responseBody['id'])) {
            return null;
        }
        return $responseBody['id'];
    }
}
