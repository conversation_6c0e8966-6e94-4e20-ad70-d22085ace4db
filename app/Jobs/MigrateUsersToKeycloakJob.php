<?php

namespace App\Jobs;

use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\{Log,DB};
use App\Models\ChsoneSocietiesUser;

class MigrateUsersToKeycloakJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    protected $user;
    protected $company;
    protected $baseUrl;
    protected $realm;
    protected $clientId;
    protected $clientSecret;
    protected $companyPrefix;
    protected $token;
    protected $tokenExpiresAt;
    
    public function __construct($user)
    {
        $this->user = $user; 
        $this->company = null;
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realms');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
        $this->companyPrefix = config('services.keycloak.company_group_prefix');
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $client = new Client([
            'http_version' => 2.0,
            'headers' => [
                'Accept-Encoding' => 'gzip',
                'Connection' => 'keep-alive'
            ]
        ]);
        $this->checkAndRefreshToken($client); // Get or refresh token before starting
        $this->migrateUserToKeycloak($client);
    }

    private function checkAndRefreshToken(Client $client)
    {
        if (!$this->token || time() >= $this->tokenExpiresAt) {
            $response = $client->post($this->baseUrl . '/realms/' . $this->realm . '/protocol/openid-connect/token', [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $this->token = $data['access_token'];
            $this->tokenExpiresAt = time() + $data['expires_in'];
        }
    }

    private function migrateUserToKeycloak(Client $client)
    {
        echo "Migrating user ID: {$this->user->user_id}\n";
        // User data preparation
        try{
            $mobile = $this->user->mobile;
            if ($mobile) {
                $mobile = ltrim($mobile, '+');
                
                // Validate length
                $mobileLength = strlen($mobile);
                if ($mobileLength < 10 || $mobileLength > 12) {
                    Log::warning("Invalid mobile number length for user ID: {$this->user->user_id}. Mobile: {$mobile}");
                    $mobile = null;
                }
            }
            $userData = [
                'username' => $this->user->username,
                'email' => str_replace(' ', '', $this->user->email),
                'firstName' => $this->user->first_name,
                'lastName' => $this->user->last_name,
                'enabled' => true,
                "emailVerified" => $this->user->email_verified,
                "attributes" => [
                    "mobile" => [$mobile],
                    "mobileVerified" => [$this->user->mobile_verified ? 'yes' : 'no'],
                    "oldSsoUserId" => [$this->user->user_id]
                ],
                'credentials' => [
                    [
                        'type' => 'password',
                        'value' => $mobile ?: "1234567890",
                        'temporary' => true
                    ]
                ]
            ];
        }catch (\Exception $e) {
            Log::error('Failed to prepare user data for user_id'.$this->user->user_id, [$e->getMessage()]);
        }

        // Migrate the user
        try {
            $response = $client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/users', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => $userData
            ]);

            if ($response->getStatusCode() == 201) {
                Log::info("Migrated user ID: {$this->user->user_id}");

                // Assign user to groups concurrently
                $this->assignUserToGroupsConcurrently($client);
            } else {
                $error = $response->getBody()->getContents();
                Log::error('Failed to migrate user', ['user_creation' => $this->user->user_id, 'error' => $error]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to migrate user', ['user_exception' => $this->user->user_id, 'exception' => $e->getMessage()]);
        }
    }

    private function assignUserToGroupsConcurrently(Client $client)
    {
        $user_id = $this->user->user_id;

        $query1 = "SELECT UA.user_id, USR.first_name, USR.last_name, USR.username, USR.mobile, USR.email,  CA.app_id, CA.company_id, COM.company_name, COM.type, APP.app_name, APP.product_code  FROM  user_app_access  as  UA 
        RIGHT JOIN  com_app_access as CA on UA.com_app_acc_id = CA.com_app_acc_id
        LEFT JOIN  companies AS COM on CA.company_id = COM.company_id
        LEFT JOIN  applications AS APP on CA.app_id = APP.app_id
        LEFT JOIN  users AS USR on UA.user_id = USR.user_id
        where UA.user_id =  " . $user_id;

        $query2 = "SELECT USR.user_id,USR.first_name,USR.last_name,USR.username,USR.mobile,USR.email,COM.company_id,COM.company_name,COM.type,AA.app_id,AA.app_name,AA.product_code
            FROM
                users AS USR
                    LEFT JOIN
                user_com_access AS UC ON USR.user_id = UC.user_id
                    LEFT JOIN 
                companies AS COM ON UC.company_id = COM.company_id
                    LEFT JOIN
                com_app_access AS CA ON UC.company_id = CA.company_id
                    LEFT JOIN
                user_app_access AS UA ON CA.com_app_acc_id = UA.com_app_acc_id
                    LEFT JOIN
                applications AS AA ON AA.app_id = CA.app_id
            WHERE
                USR.user_id = " . $user_id . " group by USR.user_id,USR.first_name,USR.last_name,USR.username,USR.mobile,USR.email,COM.company_id,COM.company_name,COM.type,AA.app_id,AA.app_name,AA.product_code";                    

        $results1 = DB::connection('old_sso')->select($query1);
        $results2 = DB::connection('old_sso')->select($query2);

        $results = array_merge($results1, $results2);

        $user['companies'] = [];
        if (count($results) > 0) {
            foreach ($results as $result) {
                if($result->company_id != null){
                    $company = [];
                    $company['company_id'] = $result->company_id;
                    if (empty($user['companies'][$result->company_id])) {
                        $user['companies'][$result->company_id] = $company;
                    }
    
                    if (!empty($result->app_id)) {
                        $app = [];
                        $app['app_id'] = $result->app_id;
                        // if (empty($user['companies'][$result->company_id]['apps'][$result->app_id])) {
                        //     $user['companies'][$result->company_id]['access_to'][] = $app['app_id'];
                        // }
                        $app['app_id'] = $result->app_id;
                        $app['type'] = $result->type;
                        $app['app_name'] = $result->app_name;
                        $app['product_code'] = $result->product_code;
                        if (empty($user['companies'][$result->company_id]['apps'][$result->app_id])) {
                            $user['companies'][$result->company_id]['apps'][] = $app;
                        }
                    }
                }
            }
        }

        if(!empty($user['companies'])) {
            foreach($user['companies'] as $key => $row) {
                if(isset($row['apps'])){
                    foreach($row['apps'] as $iKey => $appRow) {
                        $appRow['roles'] = [];
                        if($appRow['app_id'] == 2){

                            $roles = [];
                            if(!empty($user_id)) {
                                $societyUserRoles = ChsoneSocietiesUser::where(['user_id' => $user_id, 'user_status' => '1'])->get();
                                if(!empty($societyUserRoles)) {
                                    foreach($societyUserRoles->toArray() as $ele) {
                                        $roles[$ele['soc_id']] = strtolower($ele['user_role']);
                                    }
                                }
                            }

                            if(!empty($roles[$key])) {
                                $user_roles = explode(",",$roles[$key]);
                                $user['companies'][$key]['apps'][$iKey]['roles']  = array_unique($user_roles);
                            } else {
                                $user['companies'][$key]['apps'][$iKey]['roles'] = ['editor'];
                            }

                        }
                        elseif($appRow['app_id']  == 5){
                            $company_id = $row['company_id'];

                            $gateUser = "SELECT * FROM live_vizlog_master.users where auth_id = " . $user_id;
                            $gateResult = DB::connection('gate_master')->select($gateUser);
                            
                            // check if $gateResult is empty or not
                            if(!empty($gateResult)){
                                $gateUserId = $gateResult[0]->user_id;
                                $this->user->gateUserId = $gateUserId;

                                $query = "SELECT r.name FROM live_vizlog_master.role_user ru left join live_vizlog_master.roles r on ru.fk_role_id=r.role_id where ru.fk_user_id = $gateUserId and ru.company_id = $company_id";
                                $roles = DB::connection('gate_master')->select($query);
                                $user_roles = [];
                                foreach($roles as $role){
                                    $user_roles[] = $role->name;
                                }
                                $user['companies'][$key]['apps'][$iKey]['roles'] = array_unique($user_roles);
                            }
                            else{
                                $user['companies'][$key]['apps'][$iKey]['roles'] = ['editor'];
                            }
                        }
                        else{
                            $user['companies'][$key]['apps'][$iKey]['roles'] = ['editor'];
                        }
                    }
                }
            }
        }
        $companyAppAccess = $user['companies'];

        if(!empty($user['companies'])){
            $requests = function ($companyAppAccess) use ($client) {
                $userId = $this->getKeycloakUserId($client);
                foreach ($companyAppAccess as $key =>  $companyUser) {
                    $this->company = $companyUser['company_id'];
                    yield function() use ($client, $companyUser, $key, $userId) {
                        $masterGroupId = $this->getKeycloakCompanyId($client, $companyUser['company_id']);
                        if(!empty($userId) && !empty($masterGroupId)){
                            $this->assignGroupId($client, $userId, $masterGroupId);
                            if(isset($companyUser['apps']) && count($companyUser['apps']) > 0){
                                foreach($companyUser['apps'] as $app){
                                    $clientId = $this->getClientId($client, $app['app_name']);
                                    if(count($app['roles']) > 0){
                                        foreach($app['roles'] as $role){
                                            $this->assignRoletoUser($client, $userId, $clientId, $role);
                                        }
                                    }
                                }
                            }
                        }
                    };
                }
                $this->updateProfile($client, $companyAppAccess, $userId);
            };

            $pool = new Pool($client, $requests($companyAppAccess), [
                'concurrency' => config('services.keycloak.max_concurrent_requests'), // Adjust based on server capacity
                'fulfilled' => function ($response, $index) {
                    Log::info("Successfully assigned group $index to user {$this->user->user_id}."); 
                },
                'rejected' => function ($reason, $index) {
                    Log::error("Failed to assign group {$this->company} to user {$this->user->user_id}: " . $reason->getMessage()); 
                },
            ]);

            $promise = $pool->promise();
            $promise->wait();
        }
    }

    private function getKeycloakUserId(Client $client)
    {
        $response = $client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/users?username=' . $this->user->username, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = json_decode($response->getBody()->getContents(), true);
        return $body[0]['id'];
    }

    private function getKeycloakCompanyId(Client $client, $companyId)
    {
        $response = $client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/groups?max=1&search=' . $this->companyPrefix . $companyId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = json_decode($response->getBody()->getContents(), true);
        if (empty($body) || !isset($body[0]['id'])) {
            return null;
        }
        return $body[0]['id'];
    }

    private function getClientId(Client $client, $appName)
    {
        $response = $client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/clients?clientId=' . str_replace(' ','_',strtolower($appName)), [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = json_decode($response->getBody()->getContents(), true);
        if (empty($body) || !isset($body[0]['id'])) {
            Log::error("Failed to get client ID for app $appName");
        }
        return $body[0]['id'];
    }

    private function assignGroupId($client, $userId, $groupId){
        return $client->put($this->baseUrl . '/admin/realms/' . $this->realm . '/users/' . $userId . '/groups/' . $groupId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    private function assignRoletoUser(Client $client, $userId, $clientId, $roleName){
        $role = [];
        try{
            $response = $client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/clients/'.$clientId.'/roles?search='.$roleName, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
            ]);
            $body = json_decode($response->getBody()->getContents(), true);

            if (empty($body) || !isset($body[0]['id'])) {
                Log::error("Failed to get client ID for app $appName");
            }

            $roleId = '';

            foreach($body as $role){
                if($role['name'] == $roleName){
                    $roleId = $role['id'];
                    break;
                }
            }

            $role = [
                'id' => $roleId,
                'name' => $roleName,
            ];

            $response = $client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/users/'.$userId.'/role-mappings/clients/'.$clientId, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [ $role ]
            ]);

            if ($response->getStatusCode() == 204) {
                Log::info("Assigned role $roleName to user $userId for client $clientId");
            } else {
                $error = $response->getBody()->getContents();
                Log::error('Failed to assign role to user1', ['user_id' =>$userId, 'error' => $error, "roleName" => $roleName]);
            }
        }
        catch (\Exception $e) {
            Log::error('Failed to assign role to user2', ['user_id' =>$userId, 'exception' => $e->getMessage(), "roleName" => $roleName, "role" => $role]);
        }
    }

    private function updateProfile(Client $client, $companyAppAccess, $userId)
    {
        try{
            $group_access = [];
            foreach($companyAppAccess as $company){
                $group_access["C_".$company['company_id']] = [];
                if(isset($company['apps'])){
                    foreach($company['apps'] as $app){
                        $group_access["C_".$company['company_id']] = [
                            str_replace(' ','_',strtolower($app['app_name'])) => implode(',',$app['roles'])
                        ];
                    }
                }
            }

            $mobile = $this->user->mobile;
            if ($mobile) {
                $mobile = ltrim($mobile, '+');
                
                $mobileLength = strlen($mobile);
                if ($mobileLength < 10 || $mobileLength > 12) {
                    Log::warning("Invalid mobile number length for user ID: {$this->user->user_id}. Mobile: {$mobile}");
                    $mobile = null;
                }
            }
            $userId = $this->getKeycloakUserId($client);
            $response = $client->put($this->baseUrl . '/admin/realms/' . $this->realm . '/users/' . $userId, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'email' => str_replace(' ', '', $this->user->email),
                    'firstName' => $this->user->first_name,
                    'lastName' => $this->user->last_name,
                    "attributes"=> [
                        "groupAccess"=> json_encode($group_access),
                        "mobile" => [$mobile],
                        "mobileVerified" => [$this->user->mobile_verified ? 'yes' : 'no'],
                        "oldSsoUserId" => [$this->user->user_id],
                        "oldGateUserId" => [$this->user->gateUserId ?? null]
                    ]
                ]
            ]);

            if ($response->getStatusCode() == 204) {
                Log::info("Updated user profile for user ID: {$this->user->user_id}");
            } else {
                $error = $response->getBody()->getContents();
                Log::error('Failed to update user profile1', ['user_id' => $this->user->user_id, 'error' => $error]);
            }
        }catch (\Exception $e) {
            Log::error('Failed to update user profile2', ['user_id' => $this->user->user_id, 'exception' => $e->getMessage()]);
        }
    }
}
