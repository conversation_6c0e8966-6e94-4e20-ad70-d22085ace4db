<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class DeleteKeycloakUsers extends Command
{
    protected $baseUrl;
    protected $realm;
    protected $clientId;
    protected $clientSecret;
    protected $token;
    protected $tokenExpiresAt;
    protected $client;

    public function __construct()
    {
        parent::__construct();
        $this->client = new Client();
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realms');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-keycloak-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete users from Keycloak';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = new Client();
        $this->info('Deleting Keycloak users...');
        $this->checkAndRefreshToken($client); 
        $this->deleteUsers();
        $this->info('Keycloak users deleted successfully.');
    }

    private function checkAndRefreshToken(Client $client)
    {
        if (!$this->token || time() >= $this->tokenExpiresAt) {
            $response = $client->post($this->baseUrl . '/realms/' . $this->realm . '/protocol/openid-connect/token', [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $this->token = $data['access_token'];
            $this->tokenExpiresAt = time() + $data['expires_in'];
        }
    }

    private function deleteUsers()
    {
        $response = $this->client->get("$this->baseUrl/admin/realms/$this->realm/users?max=1000", [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ]
        ]);

        $users = json_decode($response->getBody()->getContents(), true);
        $requests = function ($users) {
            foreach ($users as $user) {
                $this->checkAndRefreshToken( $this->client );
                $userId = $user['id'];
                $this->info("User id :"  .$userId);
                yield function () use ($userId) {
                    return $this->client->deleteAsync("$this->baseUrl/admin/realms/$this->realm/users/$userId", [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $this->token,
                        ]
                    ]);
                    
                };
            }
        };

        $pool = new Pool($this->client, $requests($users), [
            'concurrency' => 20, // Adjust the concurrency level based on server capacity
            'fulfilled' => function ($response, $index) {
                Log::info("Successfully deleted user $index\n");
            },
            'rejected' => function (RequestException $reason, $index) {
                Log::error("Failed to delete user $index: " . $reason->getMessage() . "\n");
            },
        ]);
        
        $promise = $pool->promise();
        $promise->wait();
    }
}
