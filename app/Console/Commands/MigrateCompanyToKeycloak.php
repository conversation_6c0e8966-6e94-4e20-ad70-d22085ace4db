<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\MigrateCompanyJob;
use Illuminate\Support\Facades\DB;

class MigrateCompanyToKeycloak extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-company-to-keycloak';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate company data to Keycloak SSO';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        ini_set('memory_limit', '512M');
        DB::connection('old_sso')->table('companies')
            ->orderBy('company_id')
            ->chunk(config('services.keycloak.max_concurrent_requests'), function ($companies) {
                foreach ($companies as $company) {
                    // Dispatch the job for each company
                    MigrateCompanyJob::dispatch($company);
                }
            });

        $this->info('Company migration to Keycloak started successfully.');
    }
}
