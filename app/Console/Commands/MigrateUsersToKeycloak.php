<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Jobs\MigrateUsersToKeycloakJob;

class MigrateUsersToKeycloak extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-users-to-keycloak {start_user_id} {end_user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate Users to Keycloak';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // 1066 = chsonesupport
        // 42330 = mahesh 1
        // 2894 = poonam 
        // 3729 = neeraj 
        // 85134 = lotus 
        // 85137 = cyber one 
        // 73553 = Shweta 
        // 85358 = lotus one gatekeeper
        // 85359 = lotus two gatekeeper
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 600); 
        DB::connection('old_sso')->table('users')
            ->whereBetween('user_id', [$this->argument('start_user_id'), $this->argument('end_user_id')])
            ->whereNotIn('user_id',[1066,42330,2894,3729,85134,85137,73553,85358,85359])
            ->orderBy('user_id')
            ->chunk(config('services.keycloak.max_concurrent_requests'), function ($users) {
                foreach ($users as $user) {
                    // Dispatch the job for each company
                    MigrateUsersToKeycloakJob::dispatch($user);
                    $this->info("Dispatched migration job for user ID: {$user->user_id}");
                }
            });

        $this->info('User migration to Keycloak started successfully.');
    }
}
