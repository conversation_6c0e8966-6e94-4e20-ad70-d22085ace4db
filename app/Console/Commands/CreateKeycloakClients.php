<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Traits\KeycloakApiTrait;

class CreateKeycloakClients extends Command
{
    use KeycloakApiTrait;
    protected $companyPrefix;
    protected $clientPrefix;

    public function __construct()
    {
        parent::__construct();
        $this->initializeKeycloakApi();
        $this->companyPrefix = config('services.keycloak.company_group_prefix');
        $this->clientPrefix = config('services.keycloak.app_prefix');
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:migrate-application-to-keycloak';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate applications to Keycloak as a Clients';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $applications = $this->getApplications();

        foreach ($applications as $application) {
            $clientId = str_replace(' ','_',strtolower($application->app_name));
            $this->createClient($clientId, $application);
            $keycloakClient = $this->getClient($clientId);
            if(!empty($keycloakClient)){
                $this->createClientRoles($keycloakClient[0]->id, $application);
            }else{
                $this->logError('Failed to get client', ['client_id' => $clientId, 'error' => 'Client not found']);
            }
        }
    }

    private function getApplications()
    {
        return DB::connection('old_sso')->table('applications')->get();
    }

    private function createClient($clientId, $application)
    {
        try{
            $response = $this->sendKeycloakRequest('POST','/clients', [
                'clientId' => $clientId,
                'name' => $application->app_name,
                'enabled' => true,
                'protocol' => 'openid-connect',
                'redirectUris' => ['*'],
                'publicClient' => true,
                'attributes' => [
                    'product_code' => $application->product_code,
                ],
            ]);

            if ($response->getStatusCode() == 201) {
                $this->info("Created client ID: {$clientId}");
                return json_decode($response->getBody()->getContents());
            } else {
                $error = $response->getBody()->getContents();
                Log::error('Failed to create client', ['client_id' => $clientId, 'error' => $error]);
            }
        } catch (\Exception $e) {
            $this->logError('Exception occurred while creating client', ['client_id' => $clientId, 'exception' => $e->getMessage()]);
        }
    }

    // get client by id
    private function getClient($clientId)
    {
        try{
            $response = $this->sendKeycloakRequest('GET', '/clients?clientId=' . $clientId);
            if ($response->getStatusCode() == 200) {
                return json_decode($response->getBody()->getContents());
            } else {
                $this->logError('Failed to get client', ['client_id' => $clientId, 'error' => $response->getBody()->getContents()]);
            }
        } catch (\Exception $e) {
            $this->logError('Exception occurred while getting client', ['client_id' => $clientId, 'exception' => $e->getMessage()]);
        }
    }

    private function createClientRoles($keycloakClientId, $application)
    {
        try{
            $roles = [];
            if($application->app_id == 2){
                $roles = ['member','staff','admin','manager','auditor','support','seabreeze_staff','View Only','editor'];
            }
            elseif($application->app_id == 5){
                $roles = ['master','member','staff','gatekeeper','editor'];
            }else{
                $roles = ['editor'];
            }

            foreach ($roles as $role) {
                $response = $this->sendKeycloakRequest('POST', '/clients/' . $keycloakClientId . '/roles', [
                    'name' => $role,
                ]);
                if ($response->getStatusCode() == 201) {
                    $this->info("Created client role: {$keycloakClientId}");
                } else {
                    $this->logError('Failed to create client role', ['client_id' => $keycloakClientId, 'error' => $response->getBody()->getContents()]);
                }
            }
        } catch (\Exception $e) {
            $this->logError('Exception occurred while creating client roles', ['client_id' => $keycloakClientId, 'exception' => $e->getMessage()]);
        }
    }
}

