<?php

namespace App\Console\Commands;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

use Illuminate\Console\Command;

class DeleteKeycloakGroups extends Command
{
    protected $baseUrl;
    protected $realm;
    protected $clientId;
    protected $clientSecret;
    protected $companyPrefix;
    protected $token;
    protected $tokenExpiresAt;
    protected $client;

    public function __construct()
    {
        parent::__construct(); // Call the parent constructor
        $this->client = new Client();
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realms');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
        $this->companyPrefix = config('services.keycloak.company_prefix');
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-keycloak-groups';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete Keycloak groups';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = new Client();
        $this->info('Deleting Keycloak groups...');
        $this->checkAndRefreshToken($client); 
        $this->deleteGroups();
        $this->info('Keycloak groups deleted successfully.');
    }

    private function checkAndRefreshToken(Client $client)
    {
        if (!$this->token || time() >= $this->tokenExpiresAt) {
            $response = $client->post($this->baseUrl . '/realms/' . $this->realm . '/protocol/openid-connect/token', [
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $this->token = $data['access_token'];
            $this->tokenExpiresAt = time() + $data['expires_in'];
        }
    }

    private function deleteGroups()
    {
        
        $response = $this->client->get("$this->baseUrl/admin/realms/$this->realm/groups", [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ]
        ]);

        $groups = json_decode($response->getBody()->getContents(), true);
        $requests = function ($groups) {
            foreach ($groups as $group) {
                $this->checkAndRefreshToken( $this->client );
                $groupId = $group['id'];
                yield function () use ($groupId) {
                    return $this->client->deleteAsync("$this->baseUrl/admin/realms/$this->realm/groups/$groupId", [
                        'headers' => [
                            'Authorization' => 'Bearer ' . $this->token,
                        ]
                    ]);
                };
            }
        };

        $pool = new Pool($this->client, $requests($groups), [
            'concurrency' => 20, // Adjust the concurrency level based on server capacity
            'fulfilled' => function ($response, $index) {
                Log::info("Successfully deleted group $index\n");
            },
            'rejected' => function (RequestException $reason, $index) {
                Log::error("Failed to delete group $index: " . $reason->getMessage() . "\n");
            },
        ]);
        
        $promise = $pool->promise();
        $promise->wait();
    }
}
