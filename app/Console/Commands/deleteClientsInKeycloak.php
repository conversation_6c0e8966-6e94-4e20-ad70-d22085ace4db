<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Traits\KeycloakApiTrait;
use Illuminate\Support\Facades\DB;

class deleteClientsInKeycloak extends Command
{
    use KeycloakApiTrait;
    protected $companyPrefix;
    protected $clientPrefix;

    public function __construct()
    {
        parent::__construct();
        $this->initializeKeycloakApi();
        $this->companyPrefix = config('services.keycloak.company_group_prefix');
        $this->clientPrefix = config('services.keycloak.app_prefix');
    }
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:delete-clients-in-keycloak';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete clients in Keycloak';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $applications = $this->getApplications();

        foreach ($applications as $key => $application) {
            $clientId = $this->generateClientId($application);
            $keycloakClient = $this->getClient($clientId);
            if(!empty($keycloakClient)){
                $this->deleteClient($keycloakClient[0]->id);
            }else{
                $this->logError('Failed to get client', ['client_id' => $clientId, 'error' => 'Client not found']);
            }
        }
    }

    private function getApplications()
    {
        return DB::connection('old_sso')->table('applications')->get();
    }

    private function generateClientId($application)
    {
        // Generate a unique client ID based on the company and application details
        $companyId = $application->company_id;
        $appId = $application->app_id;
        return $this->companyPrefix . $companyId . "_" . $this->clientPrefix . $appId;
    }

    // get client by id
    private function getClient($clientId)
    {
        try{
            $response = $this->sendKeycloakRequest('GET', '/clients?clientId=' . $clientId);

            if ($response->getStatusCode() == 200) {
                return json_decode($response->getBody()->getContents());
            } else {
                $this->logError('Failed to get client', ['client_id' => $clientId, 'error' => $response->getBody()->getContents()]);
                return null;
            }
        } catch (\Exception $e) {
            $this->logError('Exception occurred while getting client', ['client_id' => $clientId, 'exception' => $e->getMessage()]);
        }
    }

    private function deleteClient($clientId)
    {
        try{
            $response = $this->sendKeycloakRequest('DELETE', '/clients/' . $clientId);

            if ($response->getStatusCode() == 204) {
                $this->info('Client deleted successfully', ['client_id' => $clientId]);
            } else {
                $this->logError('Failed to delete client', ['client_id' => $clientId, 'error' => $response->getBody()->getContents()]);
            }
        } catch (\Exception $e) {
            $this->logError('Exception occurred while deleting client', ['client_id' => $clientId, 'exception' => $e->getMessage()]);
        }
    }
}
