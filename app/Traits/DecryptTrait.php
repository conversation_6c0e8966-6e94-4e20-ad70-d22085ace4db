<?php

namespace App\Traits;

trait DecryptTrait
{
    private function decodeJWT($authorizationHeader)
    {
        try {
            $jwt = str_replace('Bearer ', '', $authorizationHeader);

            // Split the JWT into its three parts
            $parts = explode('.', $jwt);

            if (count($parts) !== 3) {
                return response()->json(['message' => 'Invalid JWT format'], 400);
            }

            // Decode the header
            $header = json_decode(base64_decode($parts[0]), true);

            // Decode the payload
            $payload = json_decode(base64_decode($parts[1]), true);

            if (!$payload) {
                return response()->json(['message' => 'Invalid JWT payload'], 400);
            }

            // Extract key information
            $decodedInfo = [
                'header' => $header,
                'payload' => $payload,
                'token_info' => [
                    'issuer' => $payload['iss'] ?? null,
                    'subject' => $payload['sub'] ?? null,
                    'audience' => $payload['aud'] ?? null,
                    'expiration' => $payload['exp'] ?? null,
                    'issued_at' => $payload['iat'] ?? null,
                    'client_id' => $payload['azp'] ?? $payload['client_id'] ?? null,
                    'username' => $payload['preferred_username'] ?? null,
                    'email_verified' => $payload['email_verified'] ?? null,
                ],
                'keycloak_access' => [
                    'realm_access' => $payload['realm_access'] ?? null,
                    'resource_access' => $payload['resource_access'] ?? null,
                ],
                'timestamps' => [
                    'issued_at_readable' => isset($payload['iat']) ? date('Y-m-d H:i:s', $payload['iat']) : null,
                    'expires_at_readable' => isset($payload['exp']) ? date('Y-m-d H:i:s', $payload['exp']) : null,
                    'is_expired' => isset($payload['exp']) ? time() > $payload['exp'] : null,
                ]
            ];

            // make sure token is not expired
            if ($decodedInfo['timestamps']['is_expired']) {
                return response()->json(['message' => 'Token expired'], 401);
            }

            // Analyze resource access permissions
            $resourceAnalysis = $this->analyzeResourceAccess($payload['resource_access'] ?? []);

            if(!$resourceAnalysis['has_user_management']){
                return response()->json(['message' => 'User management permission missing'], 403);
            }

            return response()->json([
                'message' => 'JWT decoded successfully',
                'data' => $decodedInfo
            ]);

        } catch (\Exception $e) {
            Log::error('JWT decode error', ['error' => $e->getMessage()]);
            return response()->json(['message' => 'Failed to decode JWT', 'error' => $e->getMessage()], 500);
        }
    }

    private function analyzeResourceAccess($resourceAccess)
    {
        $analysis = [
            'total_resources' => count($resourceAccess),
            'resources' => [],
            'admin_permissions' => [],
            'user_permissions' => [],
            'has_realm_admin' => false,
            'has_user_management' => false,
            'has_client_management' => false,
        ];

        foreach ($resourceAccess as $resource => $access) {
            $roles = $access['roles'] ?? [];

            $analysis['resources'][$resource] = [
                'roles_count' => count($roles),
                'roles' => $roles
            ];

            // Check for admin permissions
            foreach ($roles as $role) {

                if (in_array($role, ['manage-users'])) {
                    $analysis['has_user_management'] = true;
                }
            }
        }

        return $analysis;
    }
}
