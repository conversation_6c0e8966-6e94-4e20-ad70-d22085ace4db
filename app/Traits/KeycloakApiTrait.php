<?php

namespace App\Traits;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

trait KeycloakApiTrait
{
    protected $client;
    protected $baseUrl;
    protected $realm;
    protected $clientId;
    protected $clientSecret;
    protected $token;
    protected $tokenExpiresAt;

    public function initializeKeycloakApi()
    {
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realms');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
        $this->client = new Client();
        $this->token = null;
        $this->tokenExpiresAt = null;
    }

    private function getAccessToken()
    {
        $response = $this->client->post($this->baseUrl . '/realms/' . $this->realm . '/protocol/openid-connect/token', [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'client_credentials',
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret,
            ],
        ]);

        $data = json_decode($response->getBody()->getContents(), true);

        $this->token = $data['access_token'];
        $this->tokenExpiresAt = time() + $data['expires_in'];

        return $this->token;
    }

    private function checkAndRefreshToken()
    {
        if (!$this->token || time() >= $this->tokenExpiresAt) {
            $this->getAccessToken();
        }
    }

    protected function sendKeycloakRequest($method, $endpoint, $body = null)
    {
        $this->checkAndRefreshToken();

        $options = [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type'  => 'application/json',
            ],
        ];

        if ($body) {
            $options['json'] = $body;
        }

        $response = $this->client->request($method, $this->baseUrl . '/admin/realms/' . $this->realm . $endpoint, $options);

        return $response;
    }

    protected function logError($message, $context = [])
    {
        Log::error($message, $context);
    }

    protected function logInfo($message, $context = [])
    {
        Log::info($message, $context);
    }
}
