<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Auth;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        $requiredPlan = $request->route()->defaults['required_plan'] ?? 'Get Started';
        
        $response = Http::post(config('services.micro-services.subscription_url').'/subscription', [
            'keycloak_id' => $user->keycloak_id
        ]);

        if(!$response->json('status')){
            return redirect()->route('subscription.required')->with('error', 'You need a valid subscription to access this page.');
        }

        if ($response->json('data') !== null) {
            // Desired product_name to search
            $productNameToSearch = config('app.name');

            // Filter and get the plan_name
            $planName = collect($response->json('data'))
                ->first(fn($item) => $item['product_name'] === $productNameToSearch)['plan_name'] ?? null;

            if ($planName === null) {
                return redirect()->route('subscription.required')->with('error', 'You need a valid subscription to access this page.');
            }

            if (!$this->hasAccess($planName, $requiredPlan)) {
                return redirect()->route('subscription.required')->with('error', 'You need a valid subscription to access this page.');
            }

        }
        return $next($request);
    }

    protected function hasAccess($userPlan, $requiredPlan)
    {
        $plansHierarchy = [
            'Get Started'=> 1,
            'Unlimited' => 2,
        ];

        return $plansHierarchy[$userPlan] >= $plansHierarchy[$requiredPlan];
    }
}
