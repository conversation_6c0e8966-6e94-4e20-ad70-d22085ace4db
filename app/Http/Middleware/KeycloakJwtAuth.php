<?php

namespace App\Http\Middleware;

use App\Utils\JwtDecoder;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Keycloak JWT Authentication Middleware
 * 
 * Validates Keycloak JWT tokens and extracts user information
 * without requiring external JWT packages.
 */
class KeycloakJwtAuth
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @param string ...$requiredRoles
     * @return Response
     */
    public function handle(Request $request, Closure $next, ...$requiredRoles): Response
    {
        try {
            // Extract JWT token from request
            $token = $this->extractToken($request);
            
            if (!$token) {
                return $this->unauthorizedResponse('No authentication token provided');
            }

            // Validate JWT token
            $validation = JwtDecoder::validate($token);
            
            if (!$validation['valid']) {
                $errorMessage = implode(', ', $validation['errors']);
                Log::warning('JWT validation failed', [
                    'errors' => $validation['errors'],
                    'token_preview' => substr($token, 0, 50) . '...'
                ]);
                
                return $this->unauthorizedResponse('Invalid token: ' . $errorMessage);
            }

            $payload = $validation['payload'];
            $header = $validation['header'];

            // Extract user information
            $userInfo = JwtDecoder::extractUserInfo($payload);
            $roles = JwtDecoder::extractRoles($payload);
            $expirationInfo = JwtDecoder::getExpirationInfo($payload);

            // Check required roles if specified
            if (!empty($requiredRoles)) {
                $hasRequiredRole = false;
                
                foreach ($requiredRoles as $requiredRole) {
                    if (JwtDecoder::hasRole($payload, $requiredRole)) {
                        $hasRequiredRole = true;
                        break;
                    }
                }

                if (!$hasRequiredRole) {
                    Log::warning('Insufficient permissions', [
                        'user_id' => $userInfo['user_id'],
                        'username' => $userInfo['username'],
                        'required_roles' => $requiredRoles,
                        'user_roles' => $roles['all_roles']
                    ]);
                    
                    return $this->forbiddenResponse('Insufficient permissions. Required roles: ' . implode(', ', $requiredRoles));
                }
            }

            // Add authentication data to request
            $request->attributes->set('jwt_token', $token);
            $request->attributes->set('jwt_payload', $payload);
            $request->attributes->set('jwt_header', $header);
            $request->attributes->set('auth_user', $userInfo);
            $request->attributes->set('user_roles', $roles);
            $request->attributes->set('token_expiration', $expirationInfo);

            // Add convenience methods to request
            $request->macro('getAuthUser', function () use ($userInfo) {
                return $userInfo;
            });

            $request->macro('getUserRoles', function () use ($roles) {
                return $roles;
            });

            $request->macro('hasRole', function ($role, $resource = null) use ($payload) {
                return JwtDecoder::hasRole($payload, $role, $resource);
            });

            $request->macro('isAdmin', function () use ($payload) {
                return JwtDecoder::isAdmin($payload);
            });

            $request->macro('getOldSsoUserId', function () use ($userInfo) {
                return $userInfo['old_sso_user_id'];
            });

            $request->macro('getTokenExpiration', function () use ($expirationInfo) {
                return $expirationInfo;
            });

            // Log successful authentication
            Log::info('JWT authentication successful', [
                'user_id' => $userInfo['user_id'],
                'username' => $userInfo['username'],
                'client_id' => $userInfo['client_id'],
                'realm' => $userInfo['realm'],
                'expires_in_minutes' => $expirationInfo['expires_in_minutes'],
                'roles_count' => count($roles['all_roles']),
                'is_admin' => JwtDecoder::isAdmin($payload)
            ]);

            return $next($request);

        } catch (\Exception $e) {
            Log::error('JWT authentication error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_path' => $request->getPathInfo()
            ]);

            return $this->serverErrorResponse('Authentication service error');
        }
    }

    /**
     * Extract JWT token from request
     * 
     * @param Request $request
     * @return string|null
     */
    private function extractToken(Request $request): ?string
    {
        // Check Authorization header
        $authHeader = $request->header('Authorization');
        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            return substr($authHeader, 7);
        }

        // Check X-Auth-Token header
        $xAuthToken = $request->header('X-Auth-Token');
        if ($xAuthToken) {
            return $xAuthToken;
        }

        // Check query parameter (not recommended for production)
        $queryToken = $request->query('token');
        if ($queryToken) {
            return $queryToken;
        }

        return null;
    }

    /**
     * Return unauthorized response
     * 
     * @param string $message
     * @return JsonResponse
     */
    private function unauthorizedResponse(string $message): JsonResponse
    {
        return response()->json([
            'success' => false,
            'data' => [],
            'message' => $message,
            'error_code' => 'UNAUTHORIZED'
        ], 401);
    }

    /**
     * Return forbidden response
     * 
     * @param string $message
     * @return JsonResponse
     */
    private function forbiddenResponse(string $message): JsonResponse
    {
        return response()->json([
            'success' => false,
            'data' => [],
            'message' => $message,
            'error_code' => 'FORBIDDEN'
        ], 403);
    }

    /**
     * Return server error response
     * 
     * @param string $message
     * @return JsonResponse
     */
    private function serverErrorResponse(string $message): JsonResponse
    {
        return response()->json([
            'success' => false,
            'data' => [],
            'message' => $message,
            'error_code' => 'SERVER_ERROR'
        ], 500);
    }
}
