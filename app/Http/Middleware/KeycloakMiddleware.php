<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class KeycloakMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try{
            // Check if the user is authenticated
            if (Auth::check()) {
                return $next($request);  // Proceed if the user is already authenticated
            }
            // Redirect to Ke   ycloak login only if the user is not authenticated
            return redirect()->route('login.keycloak');
        } catch (\Exception $e) {
            dd($e->getMessage());
        }
    }
}
