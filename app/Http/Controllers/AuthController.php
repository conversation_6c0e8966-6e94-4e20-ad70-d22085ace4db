<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Socialite;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Config;
use App\Models\ChsoneSocietiesUser;

class AuthController extends Controller
{
    public function redirectToKeycloak()
    {
        try{
            return Socialite::driver('keycloak')->scopes(['openid'])->redirect();
        } catch (\Exception $e) {
            dd($e->getMessage());
        }
    }

    public function handleKeycloakCallback()
    {
        try {
            $socialiteUser = Socialite::driver('keycloak')->user();
            $idToken = $socialiteUser->accessTokenResponseBody['id_token'];
            session(['id_token' => $idToken]);
            try{
                $user = User::where('keycloak_id', $socialiteUser->id)->first();

                if(!$user){
                    $user = DB::table('users')->updateOrInsert([
                        'keycloak_id' => $socialiteUser->id,
                    ],[
                        'keycloak_id' => $socialiteUser->id,
                        'name' => $socialiteUser->name,
                        'email' => $socialiteUser->email,
                        'password' => Hash::make('test')
                    ]);
                }
                $user = User::where('keycloak_id', $socialiteUser->id)->first();
                Auth::login($user);
            } catch (\Exception $e) {
                dd($e->getMessage());
            }

            // Redirect to the intended page or dashboard
            return redirect()->intended(route('dashboard'));
        } catch (\Exception $e) {
            // Handle exceptions (e.g., token expired, invalid credentials)
            dd($e->getMessage());
            // return redirect()->route('login.keycloak')->with('error', 'Unable to authenticate.');
        }
    }

    public function dashboard()
    {
        $user = Auth::user();
        // Fetch user roles, companies, applications
        // Assuming $user->roles, $user->companies, etc.
        $roles = $user->roles; 
        $companies = $user->companies; 

        return view('dashboard', compact('user', 'roles', 'companies'));
    }

    public function logout()
    {
        Auth::logout();
        
        $idToken = session()->get('id_token');
        $keycloakBaseUrl = config('services.keycloak.base_url');
        $keycloakRealm = config('services.keycloak.realms');
        $redirectUri = config('app.url'); // Redirect back to your app after Keycloak logout

        $logoutUrl = $keycloakBaseUrl . '/realms/' . $keycloakRealm . '/protocol/openid-connect/logout?post_logout_redirect_uri='.urlencode($redirectUri).'&id_token_hint=' . $idToken;

        return redirect($logoutUrl);
    }

    public function test()
    {
        $user_id = 611;

        $query1 = "SELECT UA.user_id, USR.first_name, USR.last_name, USR.username, USR.mobile, USR.email,  CA.app_id, CA.company_id, COM.company_name, COM.type, APP.app_name, APP.product_code  FROM  user_app_access  as  UA 
        RIGHT JOIN  com_app_access as CA on UA.com_app_acc_id = CA.com_app_acc_id
        LEFT JOIN  companies AS COM on CA.company_id = COM.company_id
        LEFT JOIN  applications AS APP on CA.app_id = APP.app_id
        LEFT JOIN  users AS USR on UA.user_id = USR.user_id
        where UA.user_id =  " . $user_id;

        $query2 = "SELECT USR.user_id,USR.first_name,USR.last_name,USR.username,USR.mobile,USR.email,COM.company_id,COM.company_name,COM.type,AA.app_id,AA.app_name,AA.product_code
            FROM
                users AS USR
                    LEFT JOIN
                user_com_access AS UC ON USR.user_id = UC.user_id
                    LEFT JOIN 
                companies AS COM ON UC.company_id = COM.company_id
                    LEFT JOIN
                com_app_access AS CA ON UC.company_id = CA.company_id
                    LEFT JOIN
                user_app_access AS UA ON CA.com_app_acc_id = UA.com_app_acc_id
                    LEFT JOIN
                applications AS AA ON AA.app_id = CA.app_id
            WHERE
                USR.user_id = " . $user_id . " group by USR.user_id,USR.first_name,USR.last_name,USR.username,USR.mobile,USR.email,COM.company_id,COM.company_name,COM.type,AA.app_id,AA.app_name,AA.product_code";                    

        $results1 = DB::connection('old_sso')->select($query1);
        $results2 = DB::connection('old_sso')->select($query2);

        $results = array_merge($results1, $results2);

        // dd($results);

        $user['companies'] = [];
        if (count($results) > 0) {
            foreach ($results as $result) {
                if($result->company_id != null){
                    $company = [];
                    $company['company_id'] = $result->company_id;
                    if (empty($user['companies'][$result->company_id])) {
                        $user['companies'][$result->company_id] = $company;
                    }
    
                    if (!empty($result->app_id)) {
                        $app = [];
                        $app['app_id'] = $result->app_id;
                        // if (empty($user['companies'][$result->company_id]['apps'][$result->app_id])) {
                        //     $user['companies'][$result->company_id]['access_to'][] = $app['app_id'];
                        // }
                        $app['app_id'] = $result->app_id;
                        $app['type'] = $result->type;
                        $app['app_name'] = $result->app_name;
                        $app['product_code'] = $result->product_code;
                        if (empty($user['companies'][$result->company_id]['apps'][$result->app_id])) {
                            $user['companies'][$result->company_id]['apps'][] = $app;
                        }
                    }
                }
            }
        }
        // dd($user);

        if(!empty($user['companies'])) {
            foreach($user['companies'] as $key => $row) {
                // dd($row);
                if(isset($row['apps'])){
                    foreach($row['apps'] as $iKey => $appRow) {
                        $appRow['roles'] = [];
                        if($appRow['app_id'] == 2){

                            $roles = [];
                            if(!empty($user_id)) {
                                $societyUserRoles = ChsoneSocietiesUser::where(['user_id' => $user_id, 'user_status' => '1'])->get();
                                if(!empty($societyUserRoles)) {
                                    foreach($societyUserRoles->toArray() as $ele) {
                                        $roles[$ele['soc_id']] = strtolower($ele['user_role']);
                                    }
                                }
                            }

                            if(!empty($roles[$key])) {
                                $user_roles = explode(",",$roles[$key]);
                                $user['companies'][$key]['apps'][$iKey]['roles']  = array_unique($user_roles);
                            } else {
                                $user['companies'][$key]['apps'][$iKey]['roles'] = ['editor'];
                            }

                        }
                        elseif($appRow['app_id']  == 5){
                            $company_id = $row['company_id'];

                            $gateUser = "SELECT * FROM live_vizlog_master.users where auth_id = " . $user_id;
                            $gateResult = DB::connection('gate_master')->select($gateUser);

                            // check if $gateResult is empty or not
                            if(!empty($gateResult)){
                                $gateUserId = $gateResult[0]->user_id;

                                
                                $query = "SELECT r.name FROM live_vizlog_master.role_user ru left join live_vizlog_master.roles r on ru.fk_role_id=r.role_id where ru.fk_user_id = $gateUserId and ru.company_id = $company_id";
                                $roles = DB::connection('gate_master')->select($query);

                                $user_roles = [];
                                foreach($roles as $role){
                                    $user_roles[] = $role->name;
                                }
                                $user['companies'][$key]['apps'][$iKey]['roles'] = array_unique($user_roles);
                            }
                            else{
                                $user['companies'][$key]['apps'][$iKey]['roles'] = ['editor'];
                            }
                        }
                        else{
                            $user['companies'][$key]['apps'][$iKey]['roles'] = ['editor'];
                        }
                    }
                }
            }
        }
        $companyAppAccess = $user['companies'];
        dd($companyAppAccess);

        $group_access = [];
        foreach($companyAppAccess as $company){
            $group_access["C_".$company['company_id']] = [];
            foreach($company['apps'] as $app){
                $group_access["C_".$company['company_id']] = [
                    str_replace(' ','_',strtolower($app['app_name'])) => implode(',',$app['roles'])
                ];
            }
        }
    }
}
