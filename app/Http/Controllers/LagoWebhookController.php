<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\RequestOptions;

class LagoWebhookController extends Controller
{
    protected $token;
    protected $baseUrl;
    protected $realm;
    protected $clientId;
    protected $clientSecret;
    protected $client;
    protected $tokenExpiresAt;
    protected $companyPrefix;

    public function __construct()
    {
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realms');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
        $this->companyPrefix = config('services.keycloak.company_group_prefix');
        $this->client = new Client([
            'timeout' => 30,
            'connect_timeout' => 10,
            'http_errors' => false
        ]);
        $this->token = null;
        $this->tokenExpiresAt = null;
    }

    public function handleWebhook(Request $request)
    {
        // Validate webhook signature if <PERSON><PERSON> provides one
        // $this->validateSignature($request);
        
        $payload = $request->all();
        Log::info('Lago webhook received', ['payload' => $payload]);
        
        // Handle different webhook events
        $eventType = $payload['webhook_type'] ?? '';
        
        switch ($eventType) {
            case 'subscription.started':
                return $this->handleSubscriptionEvent($payload, 'add');

            case 'subscription.renewed':
                return $this->handleSubscriptionEvent($payload, 'add');
                
            case 'subscription.terminated':
                return $this->handleSubscriptionEvent($payload, 'revoke');

            case 'subscription.canceled':
                return $this->handleSubscriptionEvent($payload, 'revoke');
                
            default:
                return response()->json(['message' => 'Event not handled', 'description' => 'Event type not recognized by name '. $eventType], 200);
        }
    }
    
    private function handleSubscriptionEvent($payload, $accessType)
    {
        try {

            $subscription = $payload['subscription'] ?? [];
            if (empty($subscription)) {
                return response()->json(['message' => 'Missing subscription data'], 400);
            }

            // Get user ID (Keycloak ID) from external_customer_id
            $userId = $subscription['external_customer_id'] ?? null;
            if (!$userId) {
                return response()->json(['message' => 'Missing user ID'], 400);
            }

            // Get subscription name and parse app_name and access_level
            $subscriptionName = $subscription['plan_code'] ?? '';
            if (empty($subscriptionName)) {
                return response()->json(['message' => 'Missing subscription name'], 400);
            }

            $nameParts = explode('_', $subscriptionName);
            if (count($nameParts) < 2) {
                return response()->json(['message' => 'Invalid subscription name format'], 400);
            }

            $appName = trim($nameParts[0]);
            $accessLevel = trim($nameParts[1]);
            
            // Default company ID - you may need to fetch this from your database
            $companyId = 1;
            
            // Call updateAccess directly instead of making an HTTP request
            $migrationController = new MigrationController();
            $updateRequest = new Request([
                'company_id' => $companyId,
                'user_id' => $userId,
                'access_level' => $accessLevel,
                'type' => $accessType,
                'app_name' => $appName,
            ]);

            Log::info('Update access request', ['request' => $updateRequest->all()]);
            
            $response = $migrationController->updateAccess($updateRequest);
            $responseData = json_decode($response->getContent(), true);
            
            return response()->json([
                'message' => $responseData['message'] ?? 'Webhook processed successfully'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error processing Lago webhook', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'message' => 'Error processing webhook',
                'description' => $e->getMessage()
            ], 500);
        }
    }
    
    private function getAccessToken()
    {
        $response = $this->client->post($this->baseUrl . '/realms/' . $this->realm . '/protocol/openid-connect/token', [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'client_credentials',
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret
            ]
        ]);

        $data = json_decode($response->getBody()->getContents(), true);

        $this->token = $data['access_token'];
        $this->tokenExpiresAt = time() + $data['expires_in']; // Store the expiration time
        return $this->token;
    }

    private function checkAndRefreshToken()
    {
        if (!$this->token || time() >= $this->tokenExpiresAt) {
            $this->getAccessToken();
        }
    }
}
