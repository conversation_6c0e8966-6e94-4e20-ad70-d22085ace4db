<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Traits\DecryptTrait;

class MigrationController extends Controller
{
    use DecryptTrait;
    protected $token;
    protected $baseUrl;
    protected $realm;
    protected $clientId;
    protected $clientSecret;
    protected $client;
    protected $tokenExpiresAt;

    public function __construct()
    {
        $this->baseUrl = config('services.keycloak.base_url');
        $this->realm = config('services.keycloak.realms');
        $this->clientId = config('services.keycloak.client_id');
        $this->clientSecret = config('services.keycloak.client_secret');
        $this->companyPrefix = config('services.keycloak.company_group_prefix');
        $this->client = new Client();
        $this->token = null;
        $this->tokenExpiresAt = null;
        $this->clientName = null;
    }

    public function migrate()
    {
        try{
            $this->checkAndRefreshToken();

            $companies = DB::connection('old_sso')->table('companies')->get();
            foreach($companies as $company){

                $response = $this->createParentGroup($company);
                if($response != true){
                    return response()->json(['message' => 'Company Migration failed', 'error' => $response], 500);
                }
            }
            return response()->json(['message' => 'Company Migration successful']);
        }
        catch(\Exception $e){
            return response()->json(['message' => 'Migration failed', 'error' => $e->getMessage()]);
        }
    }

    private function getAccessToken()
    {
        $response = $this->client->post($this->baseUrl . '/realms/' . $this->realm . '/protocol/openid-connect/token', [
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'grant_type' => 'client_credentials',
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret
            ]
        ]);

        $data = json_decode($response->getBody()->getContents(), true);

        $this->token = $data['access_token'];
        $this->tokenExpiresAt = time() + $data['expires_in']; // Store the expiration time
        return $this->token;
    }

    // Check if the token is expired and refresh if necessary
    private function checkAndRefreshToken()
    {
        if (!$this->token || time() >= $this->tokenExpiresAt) {
            $this->getAccessToken();
        }
    }

    // Create a new parent group
    private function createParentGroup($company)
    {
        // Check and refresh the token before making the API request
        $this->checkAndRefreshToken();

        $response = $this->client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/groups', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type'  => 'application/json',
            ],
            'json' => [
                'name' => "c_". $company->company_id,
                "attributes" => [
                        "company_name" => [$company->company_name],
                        "type" => [$company->type]
                    ]
            ]
        ]);

        $statusCode = $response->getStatusCode();
        $responseBody = $response->getBody()->getContents();

        if($statusCode == 201){
            return true;
        }
        else{
            return response()->json(['message' => 'Failed to create parent group', 'error' => $responseBody], $statusCode);
        }
    }

    public function updateAccess(Request $request)
    {
        ini_set('max_execution_time', 600);
        
        $validator = Validator::make($request->all(), [
            'company_id' => 'required|integer',
            'user_id' => 'required|string',
            'access_level' => 'required|string',
            'type' => 'required|string',
            'app_name' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 400);
        }

        $this->checkAndRefreshToken();

        $userProfile = $this->getKeycloakUser($request->user_id);
        $companyId = $this->getKeycloakCompanyId($request->company_id);
        $clientID = $this->getClientId($request->app_name);
        
        if(empty($userProfile) || empty($companyId) || empty($clientID)){
            Log::error('User/ company/ client not found', ['user_id' => $request->user_id, 'company_id' => $request->company_id, 'app_name' => $request->app_name]);
            return response()->json(['message' => 'User/ company/ client not found'], 404);
        }

        // Determine if we're adding or revoking access
        $isRevoking = strtolower($request->type) === 'revoke';

        // For adding access, always assign the user to the company group
        if (!$isRevoking) {
            $check = $this->assignGroupId($request->user_id, $companyId);
            if($check->getStatusCode() != 204){
                Log::error('Failed to assign group', ['user_id' => $request->user_id, 'error' => $check->getBody()->getContents()]);
            }
        }

        // Handle role assignment or revocation based on type
        if ($isRevoking) {
            $roleRevoked = $this->revokeRoleFromUser($request->user_id, $clientID, $request->access_level);
            if (!$roleRevoked) {
                Log::warning('Role revocation failed or role not found', [
                    'user_id' => $request->user_id,
                    'client_id' => $clientID,
                    'role' => $request->access_level
                ]);
            }
        } else {
            $this->assignRoletoUser($request->user_id, $clientID, $request->access_level);
        }

        // Update groupAccess attribute
        $groupAccess = $this->generateGroupAccess($userProfile, $request, $isRevoking, $companyId);
        $attributes = $this->prepareAttributes($groupAccess, $userProfile);

        // Update user profile in Keycloak
        $response = $this->client->put($this->baseUrl . '/admin/realms/' . $this->realm . '/users/' . $request->user_id, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type'  => 'application/json',
            ],
            'json' => [
                "username"  => $userProfile['username'] ?? null,
                'email'     => $userProfile['email'] ?? null,
                'firstName' => $userProfile['firstName'] ?? null,
                'lastName'  => $userProfile['lastName'] ?? null,
                "attributes" => $attributes
            ]
        ]);

        $statusCode = $response->getStatusCode();
        $responseBody = $response->getBody()->getContents();

        if($statusCode == 204){
            $actionType = $isRevoking ? 'revoked' : 'updated';
            Log::info("Access {$actionType} successfully", ['user_id' => $request->user_id, 'company_id' => $request->company_id, 'app_name' => $request->app_name, 'access_level' => $request->access_level]);
            return response()->json(['message' => "Access {$actionType} successfully"]);
        }
        else{
            $actionType = $isRevoking ? 'revoke' : 'update';
            Log::error("Failed to {$actionType} access", ['user_id' => $request->user_id, 'company_id' => $request->company_id, 'app_name' => $request->app_name, 'access_level' => $request->access_level, 'error' => $responseBody]);
            return response()->json(['message' => "Failed to {$actionType} access", 'error' => $responseBody], $statusCode);
        }
    }

    private function getKeycloakUser($userId)
    {
        $response = $this->client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/users/'. $userId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json'
                ]
            ]);
        if($response->getStatusCode() != 200){
            Log::error('Failed to get user', ['user_id' => $userId, 'error' => $response->getBody()->getContents()]);
            return null;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    private function getKeycloakCompanyId($companyId)
    {
        $response = $this->client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/groups?max=1&search=' . $this->companyPrefix . $companyId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = json_decode($response->getBody()->getContents(), true);
        if (empty($body) || !isset($body[0]['id'])) {
            Log::error('Failed to get company group', ['company_id' => $companyId, 'error' => $response->getBody()->getContents()]);    
            return null;
        }
        return $body[0]['id'];
    }

    private function assignGroupId($userId, $groupId){
        return $this->client->put($this->baseUrl . '/admin/realms/' . $this->realm . '/users/' . $userId . '/groups/' . $groupId, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
    }

    private function removeUserFromGroup($userId, $groupId){
        try {
            $response = $this->client->delete($this->baseUrl . '/admin/realms/' . $this->realm . '/users/' . $userId . '/groups/' . $groupId, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            if ($response->getStatusCode() !== 204) {
                Log::error('Failed to remove user from group', [
                    'user_id' => $userId,
                    'group_id' => $groupId,
                    'error' => $response->getBody()->getContents()
                ]);
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Exception while removing user from group', [
                'user_id' => $userId,
                'group_id' => $groupId,
                'exception' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function getClientId($appName)
    {
        $appName = str_replace(' ','_',strtolower($appName));
        // if appname starts with one then remove it
        if (strpos($appName, 'one') === 0) {
            $appName = substr($appName, 3);
        }
        // if appname ends with _trial then remove it
        // if (strpos($appName, '_trial') === (strlen($appName) - 6)) {
        //     $appName = substr($appName, 0, -6);
        // }
        $appName = explode('_', $appName);
        $response = $this->client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/clients?clientId=' .$appName[0].'&search=true', [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type' => 'application/json',
            ],
        ]);
        $body = json_decode($response->getBody()->getContents(), true);
        if (empty($body) || !isset($body[0]['id'])) {
            return null;
        }
        $this->clientName = $body[0]['clientId'];
        Log::info('Client ID found', ['app_name' => $appName, 'client_details' => $body]);
        return $body[0]['id'];
    }

    private function assignRoletoUser($userId, $clientId, $roleName){
        $role = [];
        try{
            $response = $this->client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/clients/'.$clientId.'/roles?search='.$roleName, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
            ]);
            $body = json_decode($response->getBody()->getContents(), true);

            // find in array list search with name for exact name
            $checkExactRole = array_filter($body, function($item) use ($roleName) {
                return $item['name'] == $roleName;
            });

            if (empty($body) || !isset($body[0]['id']) || empty($checkExactRole)) {
                $response = $this->client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/clients/'.$clientId.'/roles', [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $this->token,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'name' => $roleName
                    ]
                ]);
                if($response->getStatusCode() !== 201){
                    log::error('Failed to create role', ['role' => $roleName, 'error' => $response->getBody()->getContents()]);
                }
            }

            $response = $this->client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/clients/'.$clientId.'/roles?search='.$roleName, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
            ]);
            $body = json_decode($response->getBody()->getContents(), true);

            $roleId = '';

            foreach($body as $role){
                if($role['name'] == $roleName){
                    $roleId = $role['id'];
                    break;
                }
            }

            $role = [
                'id' => $roleId,
                'name' => $roleName,
            ];

            $response = $this->client->post($this->baseUrl . '/admin/realms/' . $this->realm . '/users/'.$userId.'/role-mappings/clients/'.$clientId, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [ $role ]
            ]);

            if ($response->getStatusCode() !== 204) {
                $error = $response->getBody()->getContents();
                Log::error('Failed to assign role to user1', ['user_id' =>$userId, 'error' => $error, "roleName" => $roleName]);
            }else{
                return true;
            }
        }
        catch (\Exception $e) {
            Log::error('Failed to assign role to user2', ['user_id' =>$userId, 'exception' => $e->getMessage(), "roleName" => $roleName, "role" => $role]);
        }
    }

    private function revokeRoleFromUser($userId, $clientId, $roleName){
        try {
            // First, get the role details
            $response = $this->client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/clients/'.$clientId.'/roles?search='.$roleName, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
            ]);
            $body = json_decode($response->getBody()->getContents(), true);

            // Find the specific role
            $roleId = '';
            $role = null;

            foreach($body as $r){
                if($r['name'] == $roleName){
                    $roleId = $r['id'];
                    $role = $r;
                    break;
                }
            }

            // If role doesn't exist, nothing to revoke
            if (!$role) {
                Log::info('Role not found, nothing to revoke', ['role_name' => $roleName, 'client_id' => $clientId]);
                return true;
            }

            // Check if user has this role
            $response = $this->client->get($this->baseUrl . '/admin/realms/' . $this->realm . '/users/'.$userId.'/role-mappings/clients/'.$clientId, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
            ]);

            $userRoles = json_decode($response->getBody()->getContents(), true);
            $hasRole = false;

            foreach ($userRoles as $userRole) {
                if ($userRole['name'] === $roleName) {
                    $hasRole = true;
                    break;
                }
            }

            // If user doesn't have the role, nothing to do
            if (!$hasRole) {
                Log::info('User does not have the role, nothing to revoke', ['user_id' => $userId, 'role_name' => $roleName]);
                return true;
            }

            // Revoke the role
            $roleToRevoke = [
                'id' => $roleId,
                'name' => $roleName,
            ];

            $response = $this->client->delete($this->baseUrl . '/admin/realms/' . $this->realm . '/users/'.$userId.'/role-mappings/clients/'.$clientId, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [ $roleToRevoke ]
            ]);

            if ($response->getStatusCode() !== 204) {
                $error = $response->getBody()->getContents();
                Log::error('Failed to revoke role from user', ['user_id' => $userId, 'error' => $error, 'role_name' => $roleName]);
                return false;
            }

            return true;
        }
        catch (\Exception $e) {
            Log::error('Exception while revoking role from user', ['user_id' => $userId, 'exception' => $e->getMessage(), 'role_name' => $roleName]);
            return false;
        }
    }

    private function generateGroupAccess($userProfile, $request, $isRevoking, $companyId){
        $groupAccess = [];
        if(isset($userProfile['attributes']['groupAccess'][0])){
            $existingGroupAccess = json_decode($userProfile['attributes']['groupAccess'][0], true);
            if($existingGroupAccess) {
                $groupAccess = $existingGroupAccess;

                // Check if company exists
                if(isset($groupAccess[$this->companyPrefix.$request->company_id])) {
                    // Check if app exists in company
                    $appKey = $this->clientName;
                    if(isset($groupAccess[$this->companyPrefix.$request->company_id][$appKey])) {
                        // App exists
                        $existingRoles = explode(',', $groupAccess[$this->companyPrefix.$request->company_id][$appKey]);

                        if ($isRevoking) {
                            // Remove the role if it exists
                            if(in_array($request->access_level, $existingRoles)) {
                                $existingRoles = array_diff($existingRoles, [$request->access_level]);

                                // If no roles left, remove the app entry
                                if (empty($existingRoles)) {
                                    unset($groupAccess[$this->companyPrefix.$request->company_id][$appKey]);

                                    // If no apps left, remove the company entry and remove user from the company group
                                    if (empty($groupAccess[$this->companyPrefix.$request->company_id])) {
                                        unset($groupAccess[$this->companyPrefix.$request->company_id]);

                                        // Remove user from the company group since there are no more roles
                                        $this->removeUserFromGroup($request->user_id, $companyId);
                                        Log::info('Removed user from company group due to no remaining roles', [
                                            'user_id' => $request->user_id,
                                            'company_id' => $request->company_id
                                        ]);
                                    }
                                } else {
                                    $groupAccess[$this->companyPrefix.$request->company_id][$appKey] = implode(',', $existingRoles);
                                }
                            }
                        } else {
                            // Add role if it doesn't exist (for 'add' type)
                            if(!in_array($request->access_level, $existingRoles)) {
                                $existingRoles[] = $request->access_level;
                                $groupAccess[$this->companyPrefix.$request->company_id][$appKey] = implode(',', $existingRoles);
                            }
                        }
                    } elseif (!$isRevoking) {
                        // App doesn't exist and we're adding access, create it
                        $groupAccess[$this->companyPrefix.$request->company_id][$appKey] = $request->access_level;
                    }
                } elseif (!$isRevoking) {
                    // Company doesn't exist and we're adding access, create it with the app
                    $groupAccess[$this->companyPrefix.$request->company_id] = [
                        $this->clientName => $request->access_level
                    ];
                }
            } elseif (!$isRevoking) {
                // Invalid JSON in groupAccess and we're adding access, create fresh
                $groupAccess[$this->companyPrefix.$request->company_id] = [
                    $this->clientName => $request->access_level
                ];
            }
        } elseif (!$isRevoking) {
            // No groupAccess and we're adding access, create fresh
            $groupAccess[$this->companyPrefix.$request->company_id] = [
                $this->clientName => $request->access_level
            ];
        }

        return $groupAccess;
    }

    private function prepareAttributes($groupAccess, $userProfile){
        $attributes = [
            "groupAccess" => [json_encode($groupAccess)]
        ];

        if (isset($userProfile['attributes'])) {
            if (isset($userProfile['attributes']['oldSsoUserId'][0])) {
                $attributes["oldSsoUserId"] = [$userProfile['attributes']['oldSsoUserId'][0]];
            }

            if (isset($userProfile['attributes']['oldGateUserId'][0])) {
                $attributes["oldGateUserId"] = [$userProfile['attributes']['oldGateUserId'][0]];
            }

            if (isset($userProfile['attributes']['mobileVerified'][0])) {
                $attributes["mobileVerified"] = [$userProfile['attributes']['mobileVerified'][0]];
            }

            if (isset($userProfile['attributes']['mobile'][0])) {
                $attributes["mobile"] = [$userProfile['attributes']['mobile'][0]];
            }
        }

        return $attributes;
    }

    public function createOrUpdateUserAddress(Request $request)
    {
        ini_set('max_execution_time', 600);
        // get header autorization
        $authorizationHeader = $request->header('Authorization');
        if (!$authorizationHeader) {
            return response()->json(['message' => 'Authorization header is missing'], 401);
        }
        $decodedInfo = $this->decodeJWT($authorizationHeader);
        if($decodedInfo->getStatusCode() != 200){
            return $decodedInfo;
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|string',
            'addresses' => 'required|array|min:1',
            'addresses.*.type' => 'required|string|max:50',
            'addresses.*.method' => 'nullable|string|in:update,delete',
            'addresses.*.line1' => 'nullable|string|max:255',
            'addresses.*.line2' => 'nullable|string|max:255',
            'addresses.*.landmark' => 'nullable|string|max:255',
            'addresses.*.city' => 'nullable|string|max:100',
            'addresses.*.state' => 'nullable|string|max:100',
            'addresses.*.country' => 'nullable|string|max:100',
            'addresses.*.pincode' => 'nullable|string|regex:/^[1-9][0-9]{5}$/',
            'addresses.*.default' => 'nullable|boolean',
            'gstin' => 'nullable|string|regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Validation failed', 'errors' => $validator->errors()], 400);
        }

        // Additional custom validations
        $addresses = $request->addresses;

        // Convert types to lowercase and check for unique address types
        $types = array_map('strtolower', array_column($addresses, 'type'));
        if (count($types) !== count(array_unique($types))) {
            return response()->json(['message' => 'Address types must be unique. Use type1, type2 for multiple addresses of same type.'], 400);
        }

        // Validate addresses based on method
        $updateAddresses = [];
        $deleteAddresses = [];

        foreach ($addresses as $index => $address) {
            $method = strtolower($address['method'] ?? 'update');

            if ($method === 'delete') {
                $deleteAddresses[] = $address;
            } else {
                $updateAddresses[] = $address;
            }
        }

        // Check for exactly one default address among update operations
        if (!empty($updateAddresses)) {
            $defaultCount = array_sum(array_column($updateAddresses, 'default'));
            if ($defaultCount > 1) {
                return response()->json(['message' => 'Only one address can be marked as default in update operations.'], 400);
            }
        }

        $this->token = str_replace('Bearer ', '', $authorizationHeader);

        $userProfile = $this->getKeycloakUser($request->user_id);

        if(empty($userProfile)){
            Log::error('User not found', ['user_id' => $request->user_id]);
            return response()->json(['message' => 'User not found'], 404);
        }

        // Get existing addresses and merge with new ones
        $existingAddresses = $this->getExistingAddresses($userProfile);
        $updatedAddresses = $this->mergeAddresses($existingAddresses, $addresses);

        // Prepare attributes with addresses and GSTIN
        $attributes = $this->prepareAddressAttributes($updatedAddresses, $request->gstin, $userProfile);

        // Update user profile in Keycloak
        $response = $this->client->put($this->baseUrl . '/admin/realms/' . $this->realm . '/users/' . $request->user_id, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->token,
                'Content-Type'  => 'application/json',
            ],
            'json' => [
                "username"  => $userProfile['username'] ?? null,
                'email'     => $userProfile['email'] ?? null,
                'firstName' => $userProfile['firstName'] ?? null,
                'lastName'  => $userProfile['lastName'] ?? null,
                "attributes" => $attributes
            ]
        ]);

        $statusCode = $response->getStatusCode();
        $responseBody = $response->getBody()->getContents();

        if($statusCode == 204){
            Log::info("User address and GSTIN updated successfully", ['user_id' => $request->user_id]);
            return response()->json(['message' => "User address and GSTIN updated successfully"]);
        }
        else{
            Log::error("Failed to update user address and GSTIN", ['user_id' => $request->user_id, 'error' => $responseBody]);
            return response()->json(['message' => "Failed to update user address and GSTIN", 'error' => $responseBody], $statusCode);
        }
    }

    private function getExistingAddresses($userProfile)
    {
        $existingAddresses = [];

        if (isset($userProfile['attributes']['addresses'][0])) {
            $addressesJson = $userProfile['attributes']['addresses'][0];
            $decoded = json_decode($addressesJson, true);

            if (is_array($decoded)) {
                $existingAddresses = $decoded;
            }
        }

        return $existingAddresses;
    }

    private function mergeAddresses($existingAddresses, $newAddresses)
    {
        // Convert existing addresses to associative array with lowercase type as key
        $addressesByType = [];
        foreach ($existingAddresses as $address) {
            $type = strtolower($address['type']);
            $addressesByType[$type] = $address;
        }

        // Process new addresses
        $hasNewDefault = false;
        $updateAddressTypes = [];

        foreach ($newAddresses as $newAddress) {
            $type = strtolower($newAddress['type']);
            $method = strtolower($newAddress['method'] ?? 'update');

            if ($method === 'delete') {
                // Remove the address type if it exists
                if (isset($addressesByType[$type])) {
                    unset($addressesByType[$type]);
                }
            } else {
                // Update or add the address
                $updateAddressTypes[] = $type;

                // Format the new address
                $formattedAddress = [
                    'type' => $type, // Store as lowercase
                    'line1' => $newAddress['line1'],
                    'city' => $newAddress['city'],
                    'state' => $newAddress['state'],
                    'pincode' => $newAddress['pincode'],
                    'default' => $newAddress['default']
                ];

                // Add optional fields only if they exist and are not empty
                if (!empty($newAddress['line2'])) {
                    $formattedAddress['line2'] = $newAddress['line2'];
                }

                if (!empty($newAddress['landmark'])) {
                    $formattedAddress['landmark'] = $newAddress['landmark'];
                }

                if (!empty($newAddress['country'])) {
                    $formattedAddress['country'] = $newAddress['country'];
                }

                // Update or add the address
                $addressesByType[$type] = $formattedAddress;

                if ($newAddress['default']) {
                    $hasNewDefault = true;
                }
            }
        }

        // If a new default address is being set, remove default from other addresses
        if ($hasNewDefault) {
            foreach ($addressesByType as $type => $address) {
                if (!in_array($type, $updateAddressTypes)) {
                    $addressesByType[$type]['default'] = false;
                }
            }
        }

        // Convert back to indexed array
        return array_values($addressesByType);
    }

    private function prepareAddressAttributes($addresses, $gstin, $userProfile)
    {
        $attributes = [];

        // Add addresses as JSON string
        $attributes["addresses"] = [json_encode($addresses)];

        // Add GSTIN if provided
        if (!empty($gstin)) {
            $attributes["gstin"] = [$gstin];
        }

        // Preserve existing attributes from user profile
        if (isset($userProfile['attributes'])) {
            // Preserve groupAccess
            if (isset($userProfile['attributes']['groupAccess'][0])) {
                $attributes["groupAccess"] = [$userProfile['attributes']['groupAccess'][0]];
            }

            // Preserve other existing attributes
            if (isset($userProfile['attributes']['oldSsoUserId'][0])) {
                $attributes["oldSsoUserId"] = [$userProfile['attributes']['oldSsoUserId'][0]];
            }

            if (isset($userProfile['attributes']['oldGateUserId'][0])) {
                $attributes["oldGateUserId"] = [$userProfile['attributes']['oldGateUserId'][0]];
            }

            if (isset($userProfile['attributes']['mobileVerified'][0])) {
                $attributes["mobileVerified"] = [$userProfile['attributes']['mobileVerified'][0]];
            }

            if (isset($userProfile['attributes']['mobile'][0])) {
                $attributes["mobile"] = [$userProfile['attributes']['mobile'][0]];
            }
        }

        return $attributes;
    }
}
