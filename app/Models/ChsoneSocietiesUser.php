<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ChsoneSocietiesUser extends Model
{
    use HasFactory;

    protected $connection= 'society_master';
    protected $table = 'chsone_societies_users';
    public $timestamps = false;

    protected $casts = [
        'soc_id' => 'int',
        'user_id' => 'int',
        'user_status' => 'int'
    ];

    protected $fillable = [
        'soc_id',
        'user_id',
        'user_role',
        'user_status'
    ];
}
