<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\JwtAuthHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Quickserve Controller with JWT Authentication
 * 
 * Example controller demonstrating JWT middleware usage in quickserve-service-v12
 */
class QuickserveController extends Controller
{
    use JwtAuthHelper;

    /**
     * Get user profile information
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function profile(Request $request): JsonResponse
    {
        $userInfo = $this->getAuthUser($request);
        $oldSsoUserId = $this->getOldSsoUserId($request);
        $roles = $this->getUserRoles($request);
        $isAdmin = $this->isAdmin($request);
        $tokenExpiration = $this->getTokenExpiration($request);

        return response()->json([
            'success' => true,
            'data' => [
                'user' => $userInfo,
                'old_sso_user_id' => $oldSsoUserId,
                'roles' => $roles,
                'is_admin' => $isAdmin,
                'token_expiration' => $tokenExpiration,
                'service' => 'quickserve-service-v12'
            ]
        ]);
    }

    /**
     * Get token information
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function tokenInfo(Request $request): JsonResponse
    {
        $userId = $this->getCurrentUserId($request);
        $username = $this->getCurrentUsername($request);
        $email = $this->getCurrentUserEmail($request);
        $oldSsoUserId = $this->getOldSsoUserId($request);
        $expiresInMinutes = $this->getTokenExpiresInMinutes($request);
        $isExpired = $this->isTokenExpired($request);
        $clientId = $this->getClientId($request);
        $realm = $this->getRealm($request);

        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $userId,
                'username' => $username,
                'email' => $email,
                'old_sso_user_id' => $oldSsoUserId,
                'expires_in_minutes' => $expiresInMinutes,
                'is_expired' => $isExpired,
                'client_id' => $clientId,
                'realm' => $realm,
                'service' => 'quickserve-service-v12'
            ]
        ]);
    }

    /**
     * Get Old SSO mapping information
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function oldSsoMapping(Request $request): JsonResponse
    {
        $keycloakUserId = $this->getCurrentUserId($request);
        $oldSsoUserId = $this->getOldSsoUserId($request);
        $username = $this->getCurrentUsername($request);

        return response()->json([
            'success' => true,
            'data' => [
                'keycloak_user_id' => $keycloakUserId,
                'old_sso_user_id' => $oldSsoUserId,
                'username' => $username,
                'mapping_status' => $oldSsoUserId ? 'mapped' : 'not_mapped',
                'service' => 'quickserve-service-v12'
            ]
        ]);
    }

    /**
     * Admin dashboard (requires admin role)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function dashboard(Request $request): JsonResponse
    {
        $userProfile = $this->getUserProfile($request);
        $auditContext = $this->createAuditContext($request, 'admin_dashboard_access');

        return response()->json([
            'success' => true,
            'data' => [
                'message' => 'Welcome to Quickserve Admin Dashboard',
                'user_profile' => $userProfile,
                'audit_context' => $auditContext,
                'service' => 'quickserve-service-v12'
            ]
        ]);
    }

    /**
     * Manage orders (requires specific role)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function manageOrders(Request $request): JsonResponse
    {
        $hasOrderManagement = $this->hasRole($request, 'manage-orders');
        $hasAdmin = $this->isAdmin($request);
        $userRoles = $this->getUserRoles($request);

        return response()->json([
            'success' => true,
            'data' => [
                'message' => 'Order management access granted',
                'permissions' => [
                    'has_order_management' => $hasOrderManagement,
                    'is_admin' => $hasAdmin,
                    'user_roles' => $userRoles
                ],
                'service' => 'quickserve-service-v12'
            ]
        ]);
    }

    /**
     * Health check with authentication
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function healthCheck(Request $request): JsonResponse
    {
        $userId = $this->getCurrentUserId($request);
        $oldSsoUserId = $this->getOldSsoUserId($request);

        return response()->json([
            'success' => true,
            'data' => [
                'status' => 'healthy',
                'service' => 'quickserve-service-v12',
                'authenticated_user' => $userId,
                'old_sso_user_id' => $oldSsoUserId,
                'timestamp' => now()->toIso8601String()
            ]
        ]);
    }
}
