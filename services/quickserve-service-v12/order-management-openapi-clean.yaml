openapi: 3.0.3
info:
  title: Order Management API
  description: |
    Comprehensive order management system for QuickServe food delivery platform with advanced cancellation policies and order swap functionality.

    ## Complete Order Journey
    1. **Create Pre-Order** - Creates temp_pre_orders, temp_order_payment, payment_transaction (initiated)
    2. **Payment Processing** - Mobile app uses Payment Service v12 APIs
    3. **Payment Success** - Updates payment_transaction, creates payment_transfered (Razorpay), updates temp_order_payment
    4. **Order Creation** - Creates multiple orders (15 days = 12 weekday orders) and order_details (3 items × 12 orders = 36 records)
    5. **Wallet Locking** - Locks wallet amount for each order for cancellation tracking
    6. **Order Fulfillment** - All orders ready for delivery with status "New" (not "Confirmed")

    ## Advanced Cancellation System
    ### Time-Based Refund Policies:
    1. **Before Cutoff Time** → 100% refund + unlock wallet amount
    2. **Partial Refund Window (00:01:00-08:00:00)** → Breakfast: 0%, Lunch: 50% + unlock wallet
    3. **After 08:00:00** → No cancellation allowed

    ## Order Swap System
    - Allows customers to swap meals within the same category
    - Automatic price difference calculation and tax recalculation
    - Comprehensive validation and audit logging

  version: 2.1.0
  contact:
    name: QuickServe API Support
    email: <EMAIL>

servers:
  - url: http://************:8000/api/v2
    description: Development server
  - url: https://api.quickserve.com/v2
    description: Production server

paths:
  /order-management/create:
    post:
      tags:
        - Order Management
      summary: Create new order with payment integration
      description: |
        Creates a new order with temporary tables and payment integration.
        Returns payment_service_transaction_id for mobile app payment processing.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '200':
          description: Order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /order-management/cancel/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Cancel order with time-based refund processing
      description: |
        Advanced order cancellation with time-based refund policies and wallet management.
        Includes cancellation tracking with user details and timestamps.
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to cancel
          example: "QA93250725"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelOrderRequest'
      responses:
        '200':
          description: Order cancelled successfully with refund processed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelOrderResponse'
        '400':
          description: Invalid request - orders cannot be cancelled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: No eligible orders found for cancellation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/swap/{orderNo}:
    post:
      tags:
        - Order Management
      summary: Swap order product with another product from the same category
      description: |
        Allows customers to swap their meal with another product from the same category.
        Updates both orders and order_details tables while maintaining data integrity.

        **Key Features:**
        - Product category validation - Only same category swaps allowed
        - Order status validation - Only swappable orders (not delivered/cancelled)
        - Price difference calculation - Automatic price adjustment
        - Swap charges support - Additional charges for premium swaps
        - Tax recalculation - Updated tax based on new amount
        - Audit logging - Complete swap history tracking

        **Price Calculation:**
        ```
        New Amount = Old Amount + Price Difference + Swap Charges
        ```
      parameters:
        - name: orderNo
          in: path
          required: true
          schema:
            type: string
          description: Order number to swap
          example: "QA93250725"
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwapOrderRequest'
      responses:
        '200':
          description: Order swapped successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwapOrderResponse'
        '400':
          description: Invalid request - order not swappable or products not compatible
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order or product not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /order-management/customer/{customerCode}:
    get:
      tags:
        - Order Management
      summary: Get customer orders with cancellation details
      description: |
        Retrieves all orders for a specific customer including cancellation details
        such as cancelled by whom and cancellation date.
      parameters:
        - name: customerCode
          in: path
          required: true
          schema:
            type: integer
          description: Customer code
          example: 1
        - name: include_cancelled
          in: query
          schema:
            type: boolean
            default: true
          description: Include cancelled orders in response
        - name: order_status
          in: query
          schema:
            type: string
            enum: [New, Confirmed, Processing, Prepared, Dispatched, Delivered, Cancelled]
          description: Filter by order status
      responses:
        '200':
          description: Customer orders retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOrdersResponse'

components:
  schemas:
    CreateOrderRequest:
      type: object
      required:
        - customer_id
        - customer_address
        - location_code
        - meals
        - start_date
        - end_date
      properties:
        customer_id:
          type: integer
          example: 1
        customer_address:
          type: string
          example: "123 Main Street, Apartment 4B"
        location_code:
          type: integer
          example: 101
        meals:
          type: array
          items:
            type: object
            properties:
              meal_type:
                type: string
                enum: [breakfast, lunch, dinner]
              product_codes:
                type: array
                items:
                  type: integer
        start_date:
          type: string
          format: date
          example: "2025-09-01"
        end_date:
          type: string
          format: date
          example: "2025-09-15"

    CreateOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order created successfully"
        data:
          type: object
          properties:
            primary_order_no:
              type: string
              example: "QA93250725"
            payment_service_transaction_id:
              type: string
              example: "txn_abc123def456"
            temp_pre_order_id:
              type: integer
              example: 12345

    CancelOrderRequest:
      type: object
      required:
        - reason
        - cancel_dates
      properties:
        reason:
          type: string
          maxLength: 500
          description: "Reason for order cancellation"
          example: "Customer requested cancellation due to change in plans"
        cancel_dates:
          type: array
          minItems: 1
          items:
            type: string
            format: date
          description: "Specific dates to cancel"
          example: ["2025-09-03", "2025-09-10"]
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional: Filter cancellation by specific meal type"
          example: "lunch"
        cancelled_by:
          type: string
          description: "Who initiated the cancellation"
          example: "customer"
          enum: [customer, admin, system]

    CancelOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Orders cancelled successfully with time-based refund policy"
        data:
          type: object
          properties:
            cancelled_orders:
              type: integer
              description: "Number of orders cancelled"
              example: 3
            cancelled_order_ids:
              type: array
              items:
                type: integer
              description: "List of cancelled order IDs"
              example: [127733, 127734, 127735]
            total_refund_amount:
              type: number
              format: decimal
              description: "Total refund amount credited to wallet"
              example: 125.50
            cancellation_details:
              type: object
              properties:
                cancelled_by:
                  type: string
                  example: "customer"
                cancelled_on:
                  type: string
                  format: date-time
                  example: "2025-07-28T10:30:00Z"
                cancellation_reason:
                  type: string
                  example: "Customer requested cancellation due to change in plans"

    SwapOrderRequest:
      type: object
      required:
        - order_date
        - new_product_code
      properties:
        order_date:
          type: string
          format: date
          description: "Date of the order to swap (YYYY-MM-DD)"
          example: "2025-09-03"
        new_product_code:
          type: integer
          description: "Product code of the new product to swap to"
          example: 342
        reason:
          type: string
          maxLength: 255
          description: "Optional reason for the swap"
          example: "Customer wants to change from Poha to Upma"
        meal_type:
          type: string
          enum: [breakfast, lunch, dinner]
          description: "Optional filter by meal type"
          example: "breakfast"

    SwapOrderResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Order swapped successfully"
        data:
          type: object
          properties:
            order_id:
              type: integer
              description: "Internal order ID"
              example: 127810
            order_no:
              type: string
              description: "Order number that was swapped"
              example: "QA93250725"
            order_date:
              type: string
              format: date
              description: "Date of the swapped order"
              example: "2025-09-03"
            swap_details:
              type: object
              description: "Detailed information about the swap"
              properties:
                old_product:
                  type: object
                  properties:
                    code:
                      type: integer
                      example: 341
                    name:
                      type: string
                      example: "Poha"
                    price:
                      type: number
                      format: decimal
                      example: 150.00
                new_product:
                  type: object
                  properties:
                    code:
                      type: integer
                      example: 342
                    name:
                      type: string
                      example: "Upma"
                    price:
                      type: number
                      format: decimal
                      example: 200.00
                price_difference:
                  type: number
                  format: decimal
                  description: "Price difference between old and new product"
                  example: 50.00
                swap_charges:
                  type: number
                  format: decimal
                  description: "Additional charges for the swap"
                  example: 25.00
                total_amount_change:
                  type: number
                  format: decimal
                  description: "Total amount change (price difference + swap charges)"
                  example: 75.00
                new_order_amount:
                  type: number
                  format: decimal
                  description: "New total order amount after swap"
                  example: 225.00
            reason:
              type: string
              description: "Reason provided for the swap"
              example: "Customer wants to change from Poha to Upma"

    CustomerOrdersResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Customer orders retrieved successfully"
        data:
          type: object
          properties:
            customer_code:
              type: integer
              example: 1
            total_orders:
              type: integer
              example: 25
            orders:
              type: array
              items:
                type: object
                properties:
                  order_id:
                    type: integer
                    example: 127810
                  order_no:
                    type: string
                    example: "QA93250725"
                  order_date:
                    type: string
                    format: date
                    example: "2025-09-03"
                  order_status:
                    type: string
                    enum: [New, Confirmed, Processing, Prepared, Dispatched, Delivered, Cancelled]
                    example: "New"
                  delivery_status:
                    type: string
                    example: "Pending"
                  meal_type:
                    type: string
                    enum: [breakfast, lunch, dinner]
                    example: "breakfast"
                  product_name:
                    type: string
                    example: "Poha"
                  image_path:
                    type: string
                    nullable: true
                    description: Full S3 URL of the product image
                    example: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/breakfast-meal.jpg"
                  amount:
                    type: number
                    format: decimal
                    example: 150.00
                  order_created_on:
                    type: string
                    format: date-time
                    example: "2025-07-25T10:30:00Z"
                  cancellation_details:
                    type: object
                    nullable: true
                    description: "Present only if order is cancelled"
                    properties:
                      cancelled_by:
                        type: string
                        enum: [customer, admin, system]
                        example: "customer"
                        description: "Who initiated the cancellation"
                      cancelled_on:
                        type: string
                        format: date-time
                        example: "2025-07-28T10:30:00Z"
                        description: "When the order was cancelled"
                      cancellation_reason:
                        type: string
                        example: "Customer requested cancellation due to change in plans"
                        description: "Reason for cancellation"
                      refund_amount:
                        type: number
                        format: decimal
                        example: 75.00
                        description: "Amount refunded to customer"
                      refund_percentage:
                        type: integer
                        example: 50
                        description: "Percentage of refund based on timing"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "An error occurred"
        data:
          type: object
          nullable: true

    ValidationErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            order_date: ["The order date field is required."]
            new_product_code: ["The new product code field is required."]

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "JWT token for authenticated endpoints"

tags:
  - name: Order Management
    description: "Core order management operations including creation, cancellation, and swapping"
