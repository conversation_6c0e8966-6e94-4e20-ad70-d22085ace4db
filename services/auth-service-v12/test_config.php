<?php

/**
 * Test script to verify Old SSO configuration
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load Laravel app
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Old SSO Configuration Test ===\n\n";

$config = config('old_sso');

echo "Domain 1: " . $config['domain_1'] . "\n";
echo "Domain 2: " . $config['domain_2'] . "\n";
echo "API Key: " . substr($config['api_key'], 0, 10) . "...\n";
echo "Source: " . $config['source'] . "\n";
echo "Ref URL Domain: " . $config['ref_url_domain'] . "\n";

echo "\nEndpoints:\n";
foreach ($config['endpoints'] as $name => $endpoint) {
    echo "  {$name}: {$endpoint}\n";
}

echo "\nHeaders:\n";
foreach ($config['headers'] as $name => $value) {
    echo "  {$name}: {$value}\n";
}

echo "\nHTTP Config:\n";
echo "  Timeout: " . $config['http']['timeout'] . "s\n";
echo "  Connect Timeout: " . $config['http']['connect_timeout'] . "s\n";

// Test URL construction
$otpUrl = $config['domain_2'] . $config['endpoints']['user_otp'];
$verifyUrl = $config['domain_2'] . $config['endpoints']['user_verify_otp'];

echo "\nConstructed URLs:\n";
echo "  OTP Request URL: {$otpUrl}\n";
echo "  OTP Verify URL: {$verifyUrl}\n";

echo "\n=== Configuration Test Complete ===\n";
