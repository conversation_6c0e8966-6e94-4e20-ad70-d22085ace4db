<?php

/**
 * Test Password Mismatch Error Handling
 * Tests how the API handles "Old password did not match" errors
 */

// Test configuration
$baseUrl = 'http://************:8000/api/v2/hybrid-auth';
$accessToken = 'x6HreRlkU3I84Sa86KHkr6m7j1Ipixz4aAXy1d1a'; // Replace with actual access token
$refreshToken = 'RTeh18baI3sKFbBOtyLBJHixT1huBv8INwCJ01Gj'; // Replace with actual refresh token
$wrongOldPassword = 'wrongPassword123'; // Intentionally wrong password
$newPassword = 'newPassword123!'; // New password to set

echo "=== Password Mismatch Error Handling Test ===\n\n";

// Helper function to make HTTP requests
function makeRequest($url, $data) {
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test: Change password with wrong old password
echo "Testing: Change password with intentionally wrong old password\n";
echo "URL: {$baseUrl}/change-password\n";

$changePasswordData = [
    'access_token' => $accessToken,
    'refresh_token' => $refreshToken,
    'old_password' => $wrongOldPassword,
    'new_password' => $newPassword
];

echo "Request: " . json_encode([
    'access_token' => substr($accessToken, 0, 10) . '...',
    'refresh_token' => substr($refreshToken, 0, 10) . '...',
    'old_password' => '***WRONG_PASSWORD***',
    'new_password' => '***HIDDEN***'
], JSON_PRETTY_PRINT) . "\n";

$result = makeRequest($baseUrl . '/change-password', $changePasswordData);
echo "HTTP Code: {$result['http_code']}\n";
echo "Response: " . $result['response'] . "\n\n";

// Analyze the response
if ($result['http_code'] === 422) {
    echo "✅ CORRECT: HTTP 422 status code for validation error\n";
} else {
    echo "❌ UNEXPECTED: Expected HTTP 422, got {$result['http_code']}\n";
}

if (!$result['data']['success']) {
    echo "✅ CORRECT: success = false\n";
} else {
    echo "❌ UNEXPECTED: success should be false\n";
}

$message = $result['data']['message'] ?? '';
if (strpos(strtolower($message), 'password') !== false) {
    echo "✅ CORRECT: Error message mentions password issue\n";
    echo "Message: {$message}\n";
} else {
    echo "❌ UNEXPECTED: Error message doesn't mention password\n";
    echo "Message: {$message}\n";
}

if (isset($result['data']['data']['errors'])) {
    echo "✅ CORRECT: Validation errors included in response\n";
    echo "Errors: " . json_encode($result['data']['data']['errors'], JSON_PRETTY_PRINT) . "\n";
} else {
    echo "❌ MISSING: No validation errors in response\n";
}

echo "\n=== Expected Error Response Format ===\n";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"message\": \"Old password is incorrect. Please verify your current password.\",\n";
echo "  \"data\": {\n";
echo "    \"errors\": {\n";
echo "      \"old_password\": [\n";
echo "        \"Old password did not match\"\n";
echo "      ]\n";
echo "    }\n";
echo "  }\n";
echo "}\n";

echo "\n=== Error Handling Logic ===\n";
echo "1. Try to change password with provided access_token\n";
echo "2. Detect password mismatch error (422 status, old_password errors)\n";
echo "3. Return user-friendly error message immediately\n";
echo "4. Do NOT attempt token refresh for password mismatch errors\n";
echo "5. Include validation errors in response for client handling\n";

echo "\n=== Test Complete ===\n";
