openapi: 3.1.0
info:
  title: OneFoodDialer 2025 - Auth Service API
  description: |
    Authentication and authorization service for OneFoodDialer 2025 platform.

    ## Features
    - Multi-provider authentication (Keycloak, legacy, social)
    - JWT token management with refresh capabilities
    - Role-based access control (RBAC)
    - Multi-factor authentication (MFA)
    - Session management and security
    - Password policies and reset functionality
    - OAuth 2.0 and OpenID Connect support
    - Single Sign-On (SSO) integration

    ## Authentication Methods
    - **Keycloak SSO** - Primary authentication provider
    - **Legacy Authentication** - Backward compatibility
    - **Social Login** - Google, Facebook, Apple
    - **API Keys** - Service-to-service authentication
    - **Multi-Factor Authentication** - SMS, Email, TOTP

    ## Security Features
    - JWT tokens with RS256 signing
    - Token rotation and blacklisting
    - Rate limiting and brute force protection
    - Session timeout and concurrent session limits
    - Audit logging for all authentication events
    - GDPR compliant user data handling

    ## Integration
    - Kong API Gateway integration for token validation
    - Microservice authentication middleware
    - Real-time session management
    - Centralized user profile management

    ## Rate Limiting
    Authentication endpoints are rate limited to prevent abuse.
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.onefooddialer.com/v2/auth-service-v12
    description: Production server
  - url: https://staging-api.onefooddialer.com/v2/auth-service-v12
    description: Staging server
  - url: http://localhost:8000/v2/auth-service-v12
    description: Development server

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: Core authentication operations
  - name: Authorization
    description: Role and permission management
  - name: Token Management
    description: JWT token operations
  - name: Password Management
    description: Password reset and policy management
  - name: Multi-Factor Authentication
    description: MFA setup and verification
  - name: Social Authentication
    description: Social login providers
  - name: Session Management
    description: User session operations
  - name: User Profile
    description: User profile and preferences
  - name: Hybrid Authentication
    description: Old SSO + Keycloak integrated authentication

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
          description: Unique identifier for the user
        first_name:
          type: string
          description: First name of the user
        last_name:
          type: string
          description: Last name of the user
        email:
          type: string
          format: email
          description: Email address of the user
        role_id:
          type: integer
          description: Role ID of the user
        auth_type:
          type: string
          description: Authentication type (legacy, keycloak, etc.)
        full_name:
          type: string
          description: Full name of the user (first_name + last_name)

    LoginRequest:
      type: object
      properties:
        username:
          type: string
          description: Username or email address
        password:
          type: string
          format: password
          description: Password
        rememberMe:
          type: boolean
          description: Whether to remember the user
      required:
        - username
        - password

    ForgotPasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Email address
      required:
        - email

    ResetPasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: Email address
        token:
          type: string
          description: Reset token
        password:
          type: string
          format: password
          description: New password
        password_confirmation:
          type: string
          format: password
          description: Password confirmation
      required:
        - email
        - token
        - password
        - password_confirmation

    Error:
      type: object
      properties:
        success:
          type: boolean
          description: Success status
          example: false
        message:
          type: string
          description: Error message
          example: Invalid credentials

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

paths:
  /auth/login:
    post:
      summary: Login
      description: Authenticates a user and returns a token
      operationId: login
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
                        description: Authentication token (Laravel Sanctum for Admin, Keycloak JWT for Customer)
                      token_type:
                        type: string
                        example: Bearer
                      refresh_token:
                        oneOf:
                          - type: string
                          - type: "null"
                        description: Keycloak refresh token (only for Customer users)
                      auth_method:
                        type: string
                        enum: [sanctum, keycloak]
                        description: Authentication method used based on user role
                      role_based_storage:
                        type: object
                        properties:
                          table_used:
                            type: string
                            enum: [users, customers]
                            description: "Admin users stored in 'users' table, Customer users stored in 'customers' table"
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/logout:
    post:
      summary: Logout User
      description: Logout user from both Laravel session and Keycloak session. Automatically detects token type (Sanctum for Admin, Keycloak for Customer) and applies appropriate logout method.
      operationId: logout
      tags:
        - Auth
      security:
        - bearerAuth: []
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token:
                  type: string
                  description: Keycloak refresh token (required for Customer users to properly terminate Keycloak sessions)
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Logged out successfully from both Laravel and Keycloak"
                  data:
                    type: object
                    properties:
                      logout_methods:
                        type: array
                        items:
                          type: string
                        description: "Methods used for logout"
                        example: ["keycloak_refresh_token", "admin_api"]
                      session_terminated:
                        type: boolean
                        description: "Whether the session was properly terminated"
                        example: true
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/refresh-token:
    post:
      summary: Refresh Token
      description: Refresh an authentication token
      operationId: refreshToken
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token:
                  type: string
                  description: Refresh token
              required:
                - refresh_token
      responses:
        '200':
          description: Token refreshed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
                        description: Authentication token
                      token_type:
                        type: string
                        example: Bearer
        '401':
          description: Invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/forgot-password:
    post:
      summary: Forgot Password
      description: Requests a password reset
      operationId: forgotPassword
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForgotPasswordRequest'
      responses:
        '200':
          description: Password reset requested
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Password reset link has been sent to your email
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        description: Reset token (only included in development)
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/reset-password:
    post:
      summary: Reset Password
      description: Resets a user's password
      operationId: resetPassword
      tags:
        - Auth
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetPasswordRequest'
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Password has been reset successfully
        '401':
          description: Invalid token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/user:
    get:
      summary: Get User
      description: Gets the authenticated user
      operationId: getUser
      tags:
        - Auth
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/validate-token:
    post:
      summary: Validate Token
      description: Validate an authentication token
      operationId: validateToken
      tags:
        - Auth
      security:
        - bearerAuth: []
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: Authentication token
      responses:
        '200':
          description: Token validation result
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      valid:
                        type: boolean
                        example: true
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/keycloak/login:
    get:
      summary: Keycloak Login
      description: Get the Keycloak login URL
      operationId: keycloakLogin
      tags:
        - Auth
      responses:
        '200':
          description: Keycloak login URL
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      auth_url:
                        type: string
                        example: http://localhost:8080/auth/realms/master/protocol/openid-connect/auth?client_id=laravel&redirect_uri=http://localhost:8000/auth/callback&response_type=code&scope=openid+profile+email&state=abcdefghijklmnopqrstuvwxyz
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /auth/keycloak/callback:
    get:
      summary: Keycloak Callback
      description: Handle the Keycloak callback
      operationId: keycloakCallback
      tags:
        - Auth
      parameters:
        - name: code
          in: query
          description: Authorization code
          required: true
          schema:
            type: string
        - name: state
          in: query
          description: State parameter
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Keycloak authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
                        description: Authentication token
                      token_type:
                        type: string
                        example: Bearer
                      keycloak_tokens:
                        type: object
                        properties:
                          access_token:
                            type: string
                            description: Keycloak access token
                          refresh_token:
                            type: string
                            description: Keycloak refresh token
                          expires_in:
                            type: integer
                            description: Token expiration time in seconds
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  # Hybrid Authentication Endpoints (Old SSO + Keycloak)
  /hybrid-auth/search-user:
    post:
      summary: Search User in Old SSO
      description: Check if a user exists in the old SSO system
      operationId: hybridSearchUser
      tags:
        - Hybrid Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "<EMAIL>"
      responses:
        '200':
          description: User search completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user_exists:
                        type: boolean
                        description: Whether user exists in either system
                      user_data:
                        type: object
                        nullable: true
                        description: User data from Old SSO (if available)
                      old_sso_exists:
                        type: boolean
                        description: Whether user exists in Old SSO
                      keycloak_exists:
                        type: boolean
                        description: Whether user exists in Keycloak
                      sync_status:
                        type: string
                        enum: [synced, needs_keycloak_registration, keycloak_only, not_found]
                        description: Synchronization status between systems
                      recommendations:
                        type: object
                        properties:
                          can_login_password:
                            type: boolean
                          can_login_otp:
                            type: boolean
                          needs_keycloak_registration:
                            type: boolean
                          auto_register_on_login:
                            type: boolean
                          suggest_keycloak_login:
                            type: boolean
                          can_register:
                            type: boolean
                      # Legacy fields for backward compatibility
                      can_login:
                        type: boolean
                        description: Legacy field - whether user can login
                      can_register:
                        type: boolean
                        description: Legacy field - whether user can register
                      login_methods:
                        type: array
                        items:
                          type: string
                        description: Available login methods
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /hybrid-auth/request-otp:
    post:
      summary: Request OTP for Login
      description: Request an OTP for user authentication
      operationId: hybridRequestOtp
      tags:
        - Hybrid Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "919988877766"
      responses:
        '200':
          description: OTP sent successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      otp_sent:
                        type: boolean
                      message:
                        type: string
                      expires_in:
                        type: integer
                        description: OTP expiration time in seconds
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /hybrid-auth/login:
    post:
      summary: Hybrid Login
      description: Login using old SSO with Keycloak synchronization (password or OTP)
      operationId: hybridLogin
      tags:
        - Hybrid Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "<EMAIL>"
                password:
                  type: string
                  description: User password (required if login_otp not provided)
                  example: "MySecurePassword123!"
                login_otp:
                  type: string
                  description: 6-digit OTP (required if password not provided)
                  example: "123456"
                remember_me:
                  type: boolean
                  description: Whether to remember the user
                  default: false
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      token:
                        type: string
                        description: Bearer token for API access
                      token_type:
                        type: string
                        example: "Bearer"
                      auth_type:
                        type: string
                        enum: [hybrid, hybrid_otp]
                      keycloak_synced:
                        type: boolean
                        description: Whether user was synced with Keycloak
                      expires_in:
                        type: integer
                        description: Token expiration time in seconds
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /hybrid-auth/register:
    post:
      summary: Hybrid Registration
      description: Register a new user in both old SSO and Keycloak
      operationId: hybridRegister
      tags:
        - Hybrid Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - first_name
                - last_name
                - email
                - mobile
                - password
                - password_confirmation
              properties:
                first_name:
                  type: string
                  description: User's first name
                  example: "John"
                  maxLength: 100
                last_name:
                  type: string
                  description: User's last name
                  example: "Doe"
                  maxLength: 100
                email:
                  type: string
                  format: email
                  description: User's email address
                  example: "<EMAIL>"
                  maxLength: 255
                mobile:
                  type: string
                  description: User's mobile number with country code
                  example: "919988877766"
                  pattern: "^91\\d{10}$"
                password:
                  type: string
                  description: User's password (min 8 chars, must contain uppercase, lowercase, number, special char)
                  example: "MySecurePassword123!"
                  minLength: 8
                  maxLength: 255
                password_confirmation:
                  type: string
                  description: Password confirmation
                  example: "MySecurePassword123!"
      responses:
        '201':
          description: Registration successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
                      auth_type:
                        type: string
                        example: "hybrid"
                      keycloak_registered:
                        type: boolean
                        description: Whether user was registered in Keycloak
                      message:
                        type: string
                        example: "Registration completed successfully. You can now login with your credentials."
        '400':
          description: Registration failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'


