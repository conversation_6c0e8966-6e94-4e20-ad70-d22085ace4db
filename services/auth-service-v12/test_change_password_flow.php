<?php

/**
 * Test Change Password Flow with Access Token + Refresh Token Fallback
 * Tests the new logic: Try access token first, if expired then refresh and retry
 */

// Test configuration
$baseUrl = 'http://************:8000/api/v2/hybrid-auth';
$accessToken = 'x6HreRlkU3I84Sa86KHkr6m7j1Ipixz4aAXy1d1a'; // Replace with actual access token
$refreshToken = 'RTeh18baI3sKFbBOtyLBJHixT1huBv8INwCJ01Gj'; // Replace with actual refresh token
$oldPassword = 'dinesh@321'; // Replace with actual old password
$newPassword = 'newPassword123!'; // New password to set

echo "=== Change Password Flow Test (Access Token + Refresh Token Fallback) ===\n\n";

// Helper function to make HTTP requests
function makeRequest($url, $data) {
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test: Change password with access token and refresh token fallback
echo "Testing: Change password with access token and refresh token fallback\n";
echo "URL: {$baseUrl}/change-password\n";

$changePasswordData = [
    'access_token' => $accessToken,
    'refresh_token' => $refreshToken,
    'old_password' => $oldPassword,
    'new_password' => $newPassword
];

echo "Request: " . json_encode([
    'access_token' => substr($accessToken, 0, 10) . '...',
    'refresh_token' => substr($refreshToken, 0, 10) . '...',
    'old_password' => '***HIDDEN***',
    'new_password' => '***HIDDEN***'
], JSON_PRETTY_PRINT) . "\n";

$result = makeRequest($baseUrl . '/change-password', $changePasswordData);
echo "HTTP Code: {$result['http_code']}\n";
echo "Response: " . $result['response'] . "\n\n";

if ($result['http_code'] === 200 && $result['data']['success']) {
    echo "✅ SUCCESS: Password changed successfully\n";
    echo "Old SSO Updated: " . ($result['data']['data']['old_sso_updated'] ? 'Yes' : 'No') . "\n";
    echo "Keycloak Updated: " . ($result['data']['data']['keycloak_updated'] ? 'Yes' : 'No') . "\n";
    
    if (isset($result['data']['data']['token_refreshed'])) {
        echo "Token Refreshed: " . ($result['data']['data']['token_refreshed'] ? 'Yes' : 'No') . "\n";
    }
} else {
    echo "❌ FAILED: Could not change password\n";
    echo "Error: " . ($result['data']['message'] ?? 'Unknown error') . "\n";
}

echo "\n=== Flow Logic ===\n";
echo "1. Try to change password with provided access_token\n";
echo "2. If access_token expired, refresh it using refresh_token\n";
echo "3. Retry password change with new access_token\n";
echo "4. Update password in both Old SSO and Keycloak\n";

echo "\n=== API Endpoint ===\n";
echo "POST {$baseUrl}/change-password\n";
echo "Body: {\n";
echo "  \"access_token\": \"current_access_token\",\n";
echo "  \"refresh_token\": \"refresh_token_for_fallback\",\n";
echo "  \"old_password\": \"current_password\",\n";
echo "  \"new_password\": \"new_password\"\n";
echo "}\n";

echo "\n=== Test Complete ===\n";
