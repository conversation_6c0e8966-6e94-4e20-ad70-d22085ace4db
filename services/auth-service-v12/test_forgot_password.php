<?php

/**
 * Test script for forgot password functionality
 * Tests the OTP request and verification with type='forget_password'
 */

// Test data
$baseUrl = 'http://localhost:8000/api/v2/hybrid-auth';
$username = '918793644824'; // Replace with actual test username
$testOtp = '1234'; // Replace with actual OTP received

echo "=== Testing Forgot Password Flow ===\n\n";

// Test 1: Request OTP for forgot password (dedicated endpoint)
echo "1. Testing Forgot Password OTP Request (Dedicated Endpoint)\n";
echo "URL: {$baseUrl}/forgot-password/request-otp\n";

$requestData = [
    'username' => $username
];

echo "Request Data: " . json_encode($requestData, JSON_PRETTY_PRINT) . "\n";

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $baseUrl . '/forgot-password/request-otp',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($requestData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json'
    ],
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

echo "HTTP Code: {$httpCode}\n";
echo "Response: " . $response . "\n\n";

// Test 2: Verify OTP for forgot password (dedicated endpoint)
echo "2. Testing Forgot Password OTP Verification (Dedicated Endpoint)\n";
echo "URL: {$baseUrl}/forgot-password/verify-otp\n";

$verifyData = [
    'username' => $username,
    'otp' => $testOtp
];

echo "Request Data: " . json_encode($verifyData, JSON_PRETTY_PRINT) . "\n";

$curl = curl_init();
curl_setopt_array($curl, [
    CURLOPT_URL => $baseUrl . '/forgot-password/verify-otp',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($verifyData),
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json',
        'Accept: application/json'
    ],
]);

$response = curl_exec($curl);
$httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
curl_close($curl);

echo "HTTP Code: {$httpCode}\n";
echo "Response: " . $response . "\n\n";

// Parse response to check for fk_auth_code
$responseData = json_decode($response, true);
if (isset($responseData['data']['fp_auth_code'])) {
    echo "✅ SUCCESS: fk_auth_code found in response!\n";
    echo "fk_auth_code: " . $responseData['data']['fp_auth_code'] . "\n";
} else {
    echo "❌ ISSUE: fk_auth_code not found in response\n";
    echo "Available data keys: " . implode(', ', array_keys($responseData['data'] ?? [])) . "\n";
}

echo "\n=== Test Complete ===\n";
