<?php

namespace App\Services\Auth;

use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class KeycloakAuthenticationService implements AuthenticationServiceInterface
{
    /**
     * Constructor
     */
    public function __construct(protected array $config) {}

    /**
     * Authenticate a user
     */
    public function authenticate(string $username, string $password): array
    {
        try {
            // Get tokens from Keycloak
            $response = Http::asForm()->post($this->getTokenUrl(), [
                'grant_type' => 'password',
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['client_secret'],
                'username' => $username,
                'password' => $password,
                'scope' => 'openid profile email',
            ]);
            dd($response->json());

            if ($response->failed()) {
                Log::error('Keycloak authentication failed', [
                    'username' => $username,
                    'status' => $response->status(),
                    'response' => $response->json(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Keycloak authentication failed: '.($response->json()['error_description'] ?? 'Unknown error'),
                ];
            }

            $tokens = $response->json();

            // Get user info
            // $userInfo = $this->getUserInfo($tokens['access_token']);

            // Find or create user
            // $user = $this->findOrCreateUser($userInfo);

            // Create Sanctum token
            // $token = $user->createToken('auth_token');

            // Update auth_token in user record
            // $user->auth_token = $token->plainTextToken;
            // $user->auth_type = 'keycloak';
            // $user->save();

            // Log::info('Keycloak authentication successful', [
            //     'username' => $username,
            //     'user_id' => $user->id,
            //     'keycloak_id' => $userInfo['sub'] ?? null,
            // ]);

            return [
                'success' => true,
                // 'user' => $user,
                // 'token' => $token->plainTextToken,
                'token_type' => 'Bearer',
                'keycloak_tokens' => $tokens,
            ];
        } catch (\Exception $e) {
            Log::error('Keycloak authentication exception', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak authentication failed: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Refresh the authentication token
     */
    public function refreshToken(string $refreshToken): array
    {
        try {
            // Refresh tokens from Keycloak
            $response = Http::asForm()->post($this->getTokenUrl(), [
                'grant_type' => 'refresh_token',
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['client_secret'],
                'refresh_token' => $refreshToken,
            ]);

            if ($response->failed()) {
                Log::error('Keycloak token refresh failed', [
                    'status' => $response->status(),
                    'response' => $response->json(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Keycloak token refresh failed: '.($response->json()['error_description'] ?? 'Unknown error'),
                ];
            }

            $tokens = $response->json();

            // Get user info
            $userInfo = $this->getUserInfo($tokens['access_token']);

            // Find user
            $user = User::where('email', $userInfo['email'])->first();
            if (! $user) {
                return [
                    'success' => false,
                    'message' => 'User not found',
                ];
            }

            // Create Sanctum token
            $token = $user->createToken('auth_token');

            // Update auth_token in user record
            $user->auth_token = $token->plainTextToken;
            $user->save();

            Log::info('Keycloak token refreshed', [
                'user_id' => $user->id,
                'keycloak_id' => $userInfo['sub'] ?? null,
            ]);

            return [
                'success' => true,
                'user' => $user,
                'token' => $token->plainTextToken,
                'token_type' => 'Bearer',
                'keycloak_tokens' => $tokens,
            ];
        } catch (\Exception $e) {
            Log::error('Keycloak token refresh exception', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak token refresh failed: '.$e->getMessage(),
            ];
        }
    }

    /**
     * Validate the authentication token
     */
    public function validateToken(string $token): bool
    {
        try {
            // Validate token with Keycloak
            $response = Http::asForm()->post($this->getIntrospectUrl(), [
                'token' => $token,
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['client_secret'],
            ]);

            if ($response->failed()) {
                return false;
            }

            $result = $response->json();

            // Check if token is active
            return $result['active'] ?? false;
        } catch (\Exception $e) {
            Log::error('Keycloak token validation exception', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Revoke the authentication token
     */
    public function revokeToken(string $token): bool
    {
        try {
            // Revoke token with Keycloak
            $response = Http::asForm()->post($this->getLogoutUrl(), [
                'token' => $token,
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['client_secret'],
            ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Keycloak token revocation exception', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get the authentication method
     */
    public function getAuthMethod(): string
    {
        return 'keycloak';
    }

    /**
     * Get user info from Keycloak
     */
    protected function getUserInfo(string $accessToken): array
    {
        $response = Http::withToken($accessToken)
            ->get($this->getUserInfoUrl());

        if ($response->failed()) {
            throw new \Exception('Failed to get user info: '.$response->body());
        }

        return $response->json();
    }

    /**
     * Find or create user
     */
    protected function findOrCreateUser(array $userInfo): User
    {
        // Find user by email
        $user = User::where('email', $userInfo['email'])->first();

        if (! $user) {
            // Create new user
            $user = new User;
            $user->email = $userInfo['email'];
            $user->username = $userInfo['preferred_username'] ?? $userInfo['email'];
            $user->first_name = $userInfo['given_name'] ?? '';
            $user->last_name = $userInfo['family_name'] ?? '';
            $user->password = bcrypt(uniqid()); // Random password, not used for Keycloak auth
            $user->role_id = $this->mapKeycloakRole($userInfo);
            $user->status = 1;
            $user->company_id = 1; // Default company ID
            $user->unit_id = 1; // Default unit ID
            $user->auth_type = 'keycloak';
            $user->save();
        }

        return $user;
    }

    /**
     * Map Keycloak role to application role
     */
    protected function mapKeycloakRole(array $userInfo): int
    {
        // Default role ID (user)
        $roleId = 2;

        // Check if user has admin role in Keycloak
        if (isset($userInfo['realm_access']['roles']) && in_array('admin', $userInfo['realm_access']['roles'])) {
            $roleId = 1; // Admin role ID
        }

        return $roleId;
    }

    /**
     * Get token URL
     */
    protected function getTokenUrl(): string
    {
        return $this->config['auth_server_url'].'/realms/'.$this->config['realm'].'/protocol/openid-connect/token';
    }

    /**
     * Get user info URL
     */
    protected function getUserInfoUrl(): string
    {
        return $this->config['auth_server_url'].'/realms/'.$this->config['realm'].'/protocol/openid-connect/userinfo';
    }

    /**
     * Get introspect URL
     */
    protected function getIntrospectUrl(): string
    {
        return $this->config['auth_server_url'].'/realms/'.$this->config['realm'].'/protocol/openid-connect/token/introspect';
    }

    /**
     * Get logout URL
     */
    protected function getLogoutUrl(): string
    {
        return $this->config['auth_server_url'].'/realms/'.$this->config['realm'].'/protocol/openid-connect/logout';
    }
}
