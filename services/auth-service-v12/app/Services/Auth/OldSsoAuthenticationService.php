<?php

namespace App\Services\Auth;

use App\Models\User;
use App\Services\Auth\OldSsoApiClient;
use App\Services\Auth\KeycloakAuthenticationService;
use App\Services\Auth\KeycloakUserRegistrationService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Old SSO Authentication Service
 * 
 * Handles authentication logic that integrates old SSO with Keycloak
 */
class OldSsoAuthenticationService
{
    public function __construct(
        private OldSsoApiClient $oldSsoClient,
        private KeycloakAuthenticationService $keycloakService,
        private KeycloakUserRegistrationService $keycloakRegistrationService
    ) {}

    /**
     * Check if user exists in old SSO system and Keycloak
     */
    public function userExists(string $username): array
    {
        // Check Old SSO first
        $oldSsoResult = $this->oldSsoClient->searchUser($username);
        dd($oldSsoResult);

        $oldSsoExists = false;
        $oldSsoUserData = null;

        if ($oldSsoResult['success']) {
            // Check if user data indicates user exists
            $userData = $oldSsoResult['data']['data'] ?? null;
            $oldSsoExists = !empty($userData) && isset($userData['user_id']);
            $oldSsoUserData = $oldSsoExists ? $userData : null;
        }

        // Check Keycloak
        $keycloakExists = $this->keycloakRegistrationService->userExists($username);

        // Determine overall status and recommendations
        if ($oldSsoExists && $keycloakExists) {
            return [
                'exists' => true,
                'message' => 'User found in both Old SSO and Keycloak',
                'user_data' => $oldSsoUserData,
                'old_sso_exists' => true,
                'keycloak_exists' => true,
                'sync_status' => 'synced',
                'recommendations' => [
                    'can_login_password' => true,
                    'can_login_otp' => true,
                    'needs_keycloak_registration' => false,
                ],
            ];
        } elseif ($oldSsoExists && !$keycloakExists) {

            return [
                'exists' => true,
                'message' => 'User found and ready for login',
                'user_data' => $oldSsoUserData,
                'old_sso_exists' => true,
                'keycloak_exists' => false, // Show the actual status
                'sync_status' => 'will_sync_on_login', // More accurate status
                'recommendations' => [
                    'can_login_password' => true,
                    'can_login_otp' => true,
                    'needs_keycloak_registration' => false, // User doesn't need to do anything
                    'auto_register_on_login' => true, // Will happen automatically
                ],
            ];
        } elseif (!$oldSsoExists && $keycloakExists) {
            return [
                'exists' => true,
                'message' => 'User found in Keycloak but not in Old SSO',
                'user_data' => null,
                'old_sso_exists' => false,
                'keycloak_exists' => true,
                'sync_status' => 'keycloak_only',
                'recommendations' => [
                    'can_login_password' => true,
                    'can_login_otp' => false,
                    'needs_keycloak_registration' => false,
                    'suggest_keycloak_login' => true,
                ],
            ];
        } else {
            return [
                'exists' => false,
                'message' => 'User not found in either Old SSO or Keycloak',
                'user_data' => null,
                'old_sso_exists' => false,
                'keycloak_exists' => false,
                'sync_status' => 'not_found',
                'recommendations' => [
                    'can_login_password' => false,
                    'can_login_otp' => false,
                    'needs_registration' => true,
                    'can_register' => true,
                ],
            ];
        }
    }



    /**
     * Authenticate user with password (hybrid approach)
     */
    public function authenticateWithPassword(string $username, string $password): array
    {
        try {
            // First, authenticate with old SSO
            $oldSsoResult = $this->oldSsoClient->loginWithPassword($username, $password);
            
            if (!$oldSsoResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Old SSO authentication failed: ' . $oldSsoResult['message'],
                    'auth_type' => 'old_sso',
                ];
            }

            // Find or create user in local database
            // $user = $this->findOrCreateUserFromOldSso($oldSsoResult['data'], $username);
            $oldSsoResult = $this->oldSsoClient->searchUser($username);

            // Check if user exists in Keycloak and register if needed
            $keycloakExists = $this->keycloakRegistrationService->userExists($username);
            $keycloakResult = ['success' => false, 'message' => 'Keycloak sync not attempted'];

            if (!$keycloakExists) {
                // Register user in Keycloak
                $keycloakRegisterResult = $this->registerUserInKeycloak($oldSsoResult['data']['data'], $password);

                if ($keycloakRegisterResult['success']) {
                    Log::info('User automatically registered in Keycloak during login', [
                        'username' => $username,
                        'user_id' => $user->id,
                    ]);
                    $keycloakResult = ['success' => true, 'message' => 'User registered and synced with Keycloak'];
                } else {
                    Log::warning('Failed to register user in Keycloak during login', [
                        'username' => $username,
                        'error' => $keycloakRegisterResult['message'],
                    ]);
                    $keycloakResult = $keycloakRegisterResult;
                }
            } else {
                // Try to authenticate with Keycloak using the same credentials
                $keycloakResult = $this->syncWithKeycloak($username, $password);

                // If Keycloak auth fails but user exists, try to update credentials
                if (!$keycloakResult['success'] && $keycloakExists) {
                    Log::info('Keycloak authentication failed, attempting credentials update', [
                        'username' => $username,
                        'keycloak_error' => $keycloakResult['message'],
                    ]);

                    $updateResult = $this->updateKeycloakUserCredentials($username, $password);
                    if ($updateResult['success']) {
                        Log::info('Keycloak credentials updated, retrying authentication', [
                            'username' => $username,
                        ]);

                        // Retry Keycloak authentication after password update
                        $keycloakResult = $this->syncWithKeycloak($username, $password);
                    }
                }
            }

            // Create Sanctum token for API access
            // $token = $user->createToken('hybrid_auth_token');
            // $user->auth_token = $token->plainTextToken;
            // $user->auth_type = 'hybrid';
            // $user->save();

            return [
                'success' => true,
                'message' => 'Hybrid authentication successful',
                // 'user' => $user,
                'tokens' => $keycloakResult['keycloak_tokens'],
                'auth_type' => 'hybrid',
                'old_sso_data' => $oldSsoResult['data'],
                'keycloak_synced' => $keycloakResult['success'],
                'keycloak_message' => $keycloakResult['message'] ?? null,
            ];

        } catch (Exception $e) {
            Log::error('Hybrid authentication failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Authentication failed: ' . $e->getMessage(),
                'auth_type' => 'hybrid',
            ];
        }
    }

    /**
     * Authenticate user with OTP (hybrid approach)
     */
    public function authenticateWithOtp(string $username, string $otp): array
    {
        try {
            // dd($username, $otp);
            // Authenticate with old SSO using OTP
            $oldSsoResult = $this->oldSsoClient->loginWithOtp($username, $otp);
            dd($oldSsoResult);
            
            if (!$oldSsoResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Old SSO OTP authentication failed: ' . $oldSsoResult['message'],
                    'auth_type' => 'old_sso_otp',
                ];
            }

            Log::info('Old SSO OTP authentication successful', [
                'username' => $username,
                'old_sso_data' => $oldSsoResult['data'],
            ]);

            // Find or create user in local database
            $user = $this->findOrCreateUserFromOldSso($oldSsoResult['data'], $username);

            // Check if user exists in Keycloak and register if needed
            $keycloakExists = $this->keycloakRegistrationService->userExists($username);
            $keycloakResult = ['success' => false, 'message' => 'Keycloak sync not attempted'];

            // For OTP login, we'll use a fallback password for Keycloak
            $fallbackPassword = $this->generateFallbackPassword($username);

            if (!$keycloakExists) {
                // Register user in Keycloak with fallback password
                $keycloakRegisterResult = $this->registerUserInKeycloak($user, $fallbackPassword);
                if ($keycloakRegisterResult['success']) {
                    Log::info('User automatically registered in Keycloak during OTP login', [
                        'username' => $username,
                        'user_id' => $user->id,
                    ]);
                    $keycloakResult = ['success' => true, 'message' => 'User registered in Keycloak with fallback password'];
                } else {
                    Log::warning('Failed to register user in Keycloak during OTP login', [
                        'username' => $username,
                        'error' => $keycloakRegisterResult['message'],
                    ]);
                    $keycloakResult = $keycloakRegisterResult;
                }
            } else {
                // Try to authenticate with Keycloak using fallback password
                $keycloakResult = $this->syncWithKeycloak($username, $fallbackPassword);
            }

            // Create Sanctum token for API access
            $token = $user->createToken('hybrid_otp_auth_token');
            $user->auth_token = $token->plainTextToken;
            $user->auth_type = 'hybrid_otp';
            $user->save();

            return [
                'success' => true,
                'message' => 'Hybrid OTP authentication successful',
                'user' => $user,
                'token' => $token->plainTextToken,
                'auth_type' => 'hybrid_otp',
                'old_sso_data' => $oldSsoResult['data'],
                'keycloak_synced' => $keycloakResult['success'],
                'keycloak_message' => $keycloakResult['message'] ?? null,
            ];

        } catch (Exception $e) {
            Log::error('Hybrid OTP authentication failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'OTP authentication failed: ' . $e->getMessage(),
                'auth_type' => 'hybrid_otp',
            ];
        }
    }

    /**
     * Request OTP for user
     */
    public function requestOtp(string $username): array
    {
        // First check if user exists
        $userCheck = $this->userExists($username);
        
        if (!$userCheck['exists']) {
            return [
                'success' => false,
                'message' => 'User not found in old SSO system',
            ];
        }

        $userId = $userCheck['user_data']['user_id'] ?? $userCheck['user_data']['id'] ?? null;
        if (!$userId) {
            return [
                'success' => false,
                'message' => 'Invalid user data from old SSO',
            ];
        }

        return $this->oldSsoClient->requestOtp($userId);
    }

    /**
     * Register user in both old SSO and Keycloak
     */
    public function registerUser(array $userData): array
    {
        try {
            // Register in old SSO first
            $oldSsoResult = $this->oldSsoClient->registerUser($userData);

            if (!$oldSsoResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Old SSO registration failed: ' . $oldSsoResult['message'],
                    'auth_type' => 'old_sso',
                ];
            }

            //array:4 [ // app/Services/Auth/OldSsoAuthenticationService.php:309
            // "success" => true
            // "message" => "Operation register_user completed successfully"
            // "data" => array:4 [
            //     "app" => array:2 [
            //     "version" => "v2"
            //     "name" => "ONEAPP Auth"
            //     ]
            //     "status_code" => 201
            //     "data" => array:34 [
            //     "profile_visiblity" => "private"
            //     "first_name" => "Dinesh"
            //     "last_name" => "Koli"
            //     "email" => "<EMAIL>"
            //     "mobile" => "************"
            //     "salutation" => null
            //     "gender" => null
            //     "dob" => null
            //     "wedding_anniversary" => null
            //     "pan" => null
            //     "aadhaar" => null
            //     "blood_group" => null
            //     "highest_education" => null
            //     "address_line_1" => null
            //     "address_line_2" => null
            //     "zip_code" => null
            //     "city" => null
            //     "state" => null
            //     "country" => null
            //     "avatar" => null
            //     "source" => null
            //     "vendor_ref_id" => null
            //     "created_by" => null
            //     "updated_by" => null
            //     "t_and_c" => "no"
            //     "username" => "************"
            //     "status" => "inactive"
            //     "updated_at" => "2025-08-01 13:37:53"
            //     "created_at" => "2025-08-01 13:37:53"
            //     "user_id" => 97213
            //     "avatar_large" => null
            //     "avatar_medium" => null
            //     "avatar_small" => null
            //     "password_status" => 1
            //     ]
            //     "message" => "User registered successfully."
            // ]
            // "status_code" => 201
            // ]

            Log::info('Old SSO registration successful', [
                'email' => $oldSsoResult['data']['data']['email'],
                'mobile' => $oldSsoResult['data']['data']['mobile'],
                'old_sso_data' => $oldSsoResult['data']['data'],
            ]);

            // Create user in local database
            // $user = $this->createUserFromRegistration($userData, $oldSsoResult['data']);

            // Try to register in Keycloak as well
            $keycloakResult = $this->registerUserInKeycloak($oldSsoResult['data']['data'], $userData['password']);

            return [
                'success' => true,
                'message' => 'Hybrid registration successful',
                'user' => $oldSsoResult['data']['data'],
                'auth_type' => 'hybrid',
                'old_sso_data' => $oldSsoResult['data'],
                'keycloak_registered' => $keycloakResult['success'],
                'keycloak_message' => $keycloakResult['message'] ?? null,
            ];

        } catch (Exception $e) {
            Log::error('Hybrid registration failed', [
                'email' => $userData['email'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Registration failed: ' . $e->getMessage(),
                'auth_type' => 'hybrid',
            ];
        }
    }

    /**
     * Find or create user from old SSO data
     */
    private function findOrCreateUserFromOldSso(array $oldSsoData, string $username): User
    {
        // Try to find user by email or phone
        $user = User::where('email_id', $username)
                   ->orWhere('phone', $username)
                   ->first();

        if (!$user) {
            // Create new user from old SSO data
            $user = new User();
            $user->email_id = $oldSsoData['email'] ?? $username;
            $user->phone = $oldSsoData['mobile'] ?? $username;
            $user->first_name = $oldSsoData['first_name'] ?? '';
            $user->last_name = $oldSsoData['last_name'] ?? '';
            $user->username = $username;
            $user->role_id = 2; // Default role for regular users
            $user->status = 1;
            $user->company_id = 1;
            $user->unit_id = 1;
            $user->prosim_user_id = $oldSsoData['id'] ?? uniqid();
            $user->auth_type = 'hybrid';
            $user->save();

            Log::info('Created new user from old SSO data', [
                'user_id' => $user->id,
                'username' => $username,
            ]);
        }

        return $user;
    }

    /**
     * Register user in Keycloak - ACTUAL IMPLEMENTATION
     */
    private function registerUserInKeycloak($user, string $password): array
    {
        try {
            $result = $this->keycloakRegistrationService->registerUser($user, $password);

            if ($result['success']) {
                Log::info('Keycloak user registration successful during login', [
                    'user_id' => $user['user_id'],
                    'keycloak_user_id' => $result['user_id'] ?? 'unknown',
                    'username' => $user['username'] ?: $user['email_id'],
                ]);
            } else {
                Log::error('Keycloak user registration failed during login', [
                    'user_id' => $user['user_id'],
                    'username' => $user['username'] ?: $user['email_id'],
                    'error' => $result['message'],
                ]);
            }

            return $result;

        } catch (Exception $e) {
            Log::error('Exception during Keycloak user registration', [
                'user_id' => $user['user_id'],
                'username' => $user['username'] ?: $user['email_id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak registration error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Sync user with Keycloak
     */
    private function syncWithKeycloak(string $username, string $password): array
    {
        try {
            // Try to authenticate with Keycloak
            $keycloakResult = $this->keycloakService->authenticate($username, $password);

            if ($keycloakResult['success']) {
                return [
                    'success' => true,
                    'keycloak_tokens' => $keycloakResult['keycloak_tokens'],
                    'message' => 'Keycloak sync successful',
                ];
            }

            // If authentication failed, try to update password in Keycloak
            // This would require additional Keycloak admin API calls
            Log::warning('Keycloak sync failed, password may need updating', [
                'username' => $username,
                'keycloak_message' => $keycloakResult['message'],
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak sync failed: ' . $keycloakResult['message'],
            ];

        } catch (Exception $e) {
            Log::error('Keycloak sync error', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak sync error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate fallback password for Keycloak
     */
    private function generateFallbackPassword(string $username): string
    {
        $pattern = config('old_sso.password_sync.fallback_password_pattern', '123456789');
        
        // For mobile numbers, use the mobile number as password
        if (preg_match('/^91\d{10}$/', $username)) {
            return $username;
        }
        
        return $pattern;
    }

    /**
     * Create user from registration data
     */
    private function createUserFromRegistration(array $userData, array $oldSsoData): User
    {
        $user = new User();
        $user->email_id = $userData['email'];
        $user->phone = $userData['mobile'];
        $user->first_name = $userData['first_name'];
        $user->last_name = $userData['last_name'];
        $user->username = $userData['email'];
        $user->role_id = 2; // Default role for regular users
        $user->status = 1;
        $user->company_id = 1;
        $user->unit_id = 1;
        $user->prosim_user_id = $oldSsoData['id'] ?? uniqid();
        $user->auth_type = 'hybrid';
        $user->save();

        Log::info('Created new user from registration', [
            'user_id' => $user->id,
            'email' => $userData['email'],
        ]);

        return $user;
    }

    /**
     * Register user in Keycloak - ACTUAL IMPLEMENTATION
     */
    private function registerInKeycloak($user, array $userData): array
    {
        try {

            // Use the KeycloakUserRegistrationService to actually register the user
            $keycloakUserData = [
                'username' => $user->username ?? $user->email_id,
                'email' => $userData['email'],
                'first_name' => $userData['first_name'] ?? $user->first_name,
                'last_name' => $userData['last_name'] ?? $user->last_name,
                'password' => $userData['password'],
            ];

            $result = $this->keycloakRegistrationService->registerUser($keycloakUserData);

            if ($result['success']) {
                Log::info('Keycloak registration successful', [
                    'user_id' => $user->id,
                    'keycloak_user_id' => $result['user_id'] ?? 'unknown',
                    'email' => $userData['email'],
                ]);

                return [
                    'success' => true,
                    'message' => 'Keycloak registration completed successfully',
                    'keycloak_user_id' => $result['user_id'] ?? null,
                ];
            } else {
                Log::error('Keycloak registration failed', [
                    'user_id' => $user->id,
                    'error' => $result['message'],
                    'email' => $userData['email'],
                ]);

                return [
                    'success' => false,
                    'message' => 'Keycloak registration failed: ' . $result['message'],
                ];
            }

        } catch (Exception $e) {
            Log::error('Exception during Keycloak registration', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak registration failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Update Keycloak user when Old SSO auth succeeds but Keycloak auth fails
     */
    public function updateKeycloakUserCredentials(string $username, string $password): array
    {
        try {
            Log::info('Starting Keycloak user credentials update', [
                'username' => $username,
            ]);

            // Check if user exists in Keycloak
            $userExists = $this->keycloakRegistrationService->userExists($username);

            if (!$userExists) {
                Log::warning('User does not exist in Keycloak for credentials update', [
                    'username' => $username,
                ]);
                return [
                    'success' => false,
                    'message' => 'User does not exist in Keycloak',
                ];
            }

            // Update password in Keycloak
            $passwordResult = $this->keycloakRegistrationService->updateUserPassword($username, $password);

            if ($passwordResult['success']) {
                Log::info('Keycloak user credentials updated successfully', [
                    'username' => $username,
                    'keycloak_user_id' => $passwordResult['user_id'] ?? 'unknown',
                ]);

                return [
                    'success' => true,
                    'message' => 'Keycloak user credentials updated successfully',
                    'keycloak_user_id' => $passwordResult['user_id'] ?? null,
                ];
            } else {
                Log::error('Failed to update Keycloak user credentials', [
                    'username' => $username,
                    'error' => $passwordResult['message'],
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to update Keycloak credentials: ' . $passwordResult['message'],
                ];
            }

        } catch (Exception $e) {
            Log::error('Exception during Keycloak credentials update', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak credentials update failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify mobile OTP and activate user
     */
    public function verifyMobileOtp(string $username, string $otp): array
    {
        try {

            // First, get user data from Old SSO to get user_id
            $userCheck = $this->userExists($username);
            dd($userCheck);

            if (!$userCheck['exists'] || !$userCheck['old_sso_exists']) {
                return [
                    'success' => false,
                    'message' => 'User not found in Old SSO system',
                ];
            }

            $userId = $userCheck['user_data']['user_id'] ?? $userCheck['user_data']['id'] ?? null;

            if (!$userId) {
                Log::error('User ID not found in Old SSO data', [
                    'username' => $username,
                    'user_data' => $userCheck['user_data'],
                ]);
                return [
                    'success' => false,
                    'message' => 'User ID not found in Old SSO data',
                ];
            }

            // Verify OTP with Old SSO
            $otpResult = $this->oldSsoClient->verifyMobileOtp($userId, $otp);

            if (!$otpResult['success']) {
                Log::warning('Mobile OTP verification failed', [
                    'username' => $username,
                    'user_id' => $userId,
                    'message' => $otpResult['message'],
                ]);

                return [
                    'success' => false,
                    'message' => $otpResult['message'] ?? 'OTP verification failed',
                ];
            }

            Log::info('Mobile OTP verification successful', [
                'username' => $username,
                'user_id' => $userId,
            ]);

            // Note: User status becomes active and mobile_verified becomes 1 in Old SSO
            // We don't modify the local database as per your requirement

            return [
                'success' => true,
                'message' => 'Mobile OTP verified successfully. User is now active.',
                'user_data' => $otpResult['data'] ?? null,
            ];

        } catch (Exception $e) {
            Log::error('Exception during mobile OTP verification', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Mobile OTP verification failed: ' . $e->getMessage(),
            ];
        }
    }
}
