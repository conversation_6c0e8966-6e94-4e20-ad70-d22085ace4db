<?php

namespace App\Services\Auth;

use App\Models\User;
use App\Services\Auth\OldSsoApiClient;
use App\Services\Auth\KeycloakAuthenticationService;
use App\Services\Auth\KeycloakUserRegistrationService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Old SSO Authentication Service
 * 
 * Handles authentication logic that integrates old SSO with Keycloak
 */
class OldSsoAuthenticationService
{
    public function __construct(
        private OldSsoApiClient $oldSsoClient,
        private KeycloakAuthenticationService $keycloakService,
        private KeycloakUserRegistrationService $keycloakRegistrationService
    ) {}

    public function userExistsBase(string $username): array
    {
        // Check Old SSO first
        return $this->oldSsoClient->searchUser($username);
    }

    /**
     * Check if user exists in old SSO system and Keycloak
     */
    public function userExists(string $username): array
    {
        // Check Old SSO first
        $oldSsoResult = $this->oldSsoClient->searchUser($username);

        $oldSsoExists = false;
        $oldSsoUserData = null;

        if ($oldSsoResult['success']) {
            // Check if user data indicates user exists
            $userData = $oldSsoResult['data']['data'] ?? null;
            $oldSsoExists = !empty($userData) && isset($userData['user_id']);
            $oldSsoUserData = $oldSsoExists ? $userData : null;
        }

        // Check for inactive user using dedicated method
        $inactiveCheck = $this->checkForInactiveUser($oldSsoResult);
        $isInactiveUser = $inactiveCheck['is_inactive'];
        $inactiveUserData = $inactiveCheck['user_data'];

        // Check Keycloak
        $keycloakExists = $this->keycloakRegistrationService->userExists($username);

        // Determine overall status and recommendations
        if ($oldSsoExists && $keycloakExists) {
            return [
                'exists' => true,
                'message' => 'User found in both Old SSO and Keycloak',
                'user_data' => $oldSsoUserData,
                'old_sso_exists' => true,
                'keycloak_exists' => true,
                'sync_status' => 'synced',
                'recommendations' => [
                    'can_login_password' => true,
                    'can_login_otp' => true,
                    'needs_keycloak_registration' => false,
                ],
            ];
        } elseif ($oldSsoExists && !$keycloakExists) {

            return [
                'exists' => true,
                'message' => 'User found and ready for login',
                'user_data' => $oldSsoUserData,
                'old_sso_exists' => true,
                'keycloak_exists' => false, // Show the actual status
                'sync_status' => 'will_sync_on_login', // More accurate status
                'recommendations' => [
                    'can_login_password' => true,
                    'can_login_otp' => true,
                    'needs_keycloak_registration' => false, // User doesn't need to do anything
                    'auto_register_on_login' => true, // Will happen automatically
                ],
            ];
        } elseif ($inactiveUserData && $keycloakExists) {
            // User exists in Keycloak but is inactive/unverified in Old SSO
            return $this->generateInactiveUserResponse($inactiveUserData, true, 'search');
        } elseif ($inactiveUserData && !$keycloakExists) {
            // User is inactive/unverified in Old SSO and doesn't exist in Keycloak
            return $this->generateInactiveUserResponse($inactiveUserData, false, 'search');
        } elseif (!$oldSsoExists && $keycloakExists) {
            return [
                'exists' => true,
                'message' => 'User found in Keycloak but not in Old SSO',
                'user_data' => null,
                'old_sso_exists' => false,
                'keycloak_exists' => true,
                'sync_status' => 'keycloak_only',
                'recommendations' => [
                    'can_login_password' => true,
                    'can_login_otp' => false,
                    'needs_keycloak_registration' => false,
                    'suggest_keycloak_login' => true,
                ],
            ];
        } else {
            return [
                'exists' => false,
                'message' => 'User not found in either Old SSO or Keycloak',
                'user_data' => null,
                'old_sso_exists' => false,
                'keycloak_exists' => false,
                'sync_status' => 'not_found',
                'recommendations' => [
                    'can_login_password' => false,
                    'can_login_otp' => false,
                    'needs_registration' => true,
                    'can_register' => true,
                ],
            ];
        }
    }



    /**
     * Authenticate user with password (optimized hybrid approach)
     */
    public function authenticateWithPassword(string $username, string $password): array
    {
        try {
            Log::info('Starting optimized hybrid authentication', [
                'username' => $username,
            ]);

            // Step 1: Authenticate with Old SSO (primary source of truth)
            $oldSsoResult = $this->oldSsoClient->loginWithPassword($username, $password);

            if (!$oldSsoResult['success']) {
                // Check if this is an inactive user before returning generic error
                $inactiveCheck = $this->checkForInactiveUser($oldSsoResult);

                if ($inactiveCheck['is_inactive']) {
                    Log::info('Login blocked: User is inactive/unverified', [
                        'username' => $username,
                        'error_type' => $inactiveCheck['error_type'],
                        'user_id' => $inactiveCheck['user_data']['user_id'] ?? 'unknown',
                    ]);

                    // Quick check if user exists in Keycloak for proper response
                    $keycloakExists = $this->keycloakRegistrationService->userExists($username);

                    // Return inactive user response with login context
                    $inactiveResponse = $this->generateInactiveUserResponse(
                        $inactiveCheck['user_data'],
                        $keycloakExists,
                        'login'
                    );

                    return [
                        'success' => false,
                        'message' => $inactiveResponse['message'],
                        'auth_type' => 'blocked_inactive',
                        'user_data' => $inactiveResponse['user_data'],
                        'recommendations' => $inactiveResponse['recommendations'],
                        'sync_status' => $inactiveResponse['sync_status'],
                    ];
                }

                // Generic Old SSO authentication failure
                return [
                    'success' => false,
                    'message' => 'Old SSO authentication failed: ' . $oldSsoResult['message'],
                    'auth_type' => 'old_sso',
                ];
            }

            // Step 2: Get complete Old SSO user profile for timestamp comparison
            Log::info('Old SSO authentication successful, fetching complete user profile for timestamp comparison', [
                'username' => $username,
            ]);

            $oldSsoProfileResult = $this->oldSsoClient->searchUser($username);

            if (!$oldSsoProfileResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to fetch Old SSO user profile: ' . $oldSsoProfileResult['message'],
                    'auth_type' => 'old_sso',
                ];
            }

            $oldSsoUserData = $oldSsoProfileResult['data']['data'] ?? null;
            if (!$oldSsoUserData) {
                return [
                    'success' => false,
                    'message' => 'Invalid Old SSO user profile data received',
                    'auth_type' => 'old_sso',
                ];
            }

            Log::info('Old SSO user profile fetched successfully', [
                'username' => $username,
                'user_id' => $oldSsoUserData['user_id'] ?? 'unknown',
                'updated_at' => $oldSsoUserData['updated_at'] ?? 'unknown',
                'mobile_verified' => $oldSsoUserData['mobile_verified'] ?? 'unknown',
                'email_verified' => $oldSsoUserData['email_verified'] ?? 'unknown',
            ]);

            // Step 3: Optimized Keycloak sync using timestamp comparison
            $keycloakSyncResult = $this->optimizedKeycloakSync($username, $password, $oldSsoUserData);

            // Step 4: authenticate
            $authResult = $this->syncWithKeycloak($username, $password);

            return [
                'success' => true,
                'message' => 'Hybrid authentication successful',
                'tokens' => $authResult['keycloak_tokens'] ?? null,
                'auth_type' => 'hybrid',
                'old_sso_data' => [
                    'data' => $oldSsoUserData, // Use complete profile data instead of login response
                ],
                'keycloak_synced' => $authResult['success'] ?? false,
                'keycloak_message' => $keycloakSyncResult['message'] ?? null
            ];

        } catch (Exception $e) {
            Log::error('Hybrid authentication failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Authentication failed: ' . $e->getMessage(),
                'auth_type' => 'hybrid',
            ];
        }
    }

    /**
     * Authenticate user with OTP (optimized hybrid approach)
     */
    public function authenticateWithOtp(string $username, string $otp): array
    {
        try {
            Log::info('Starting optimized hybrid OTP authentication', [
                'username' => $username,
            ]);

            // Step 1: Authenticate with Old SSO using OTP
            $oldSsoResult = $this->oldSsoClient->loginWithOtp($username, $otp);

            if (!$oldSsoResult['success']) {
                // Check if this is an inactive user before returning generic error
                $inactiveCheck = $this->checkForInactiveUser($oldSsoResult);

                if ($inactiveCheck['is_inactive']) {
                    Log::info('OTP login blocked: User is inactive/unverified', [
                        'username' => $username,
                        'error_type' => $inactiveCheck['error_type'],
                        'user_id' => $inactiveCheck['user_data']['user_id'] ?? 'unknown',
                    ]);

                    // Quick check if user exists in Keycloak for proper response
                    $keycloakExists = $this->keycloakRegistrationService->userExists($username);

                    // Return inactive user response with OTP login context
                    $inactiveResponse = $this->generateInactiveUserResponse(
                        $inactiveCheck['user_data'],
                        $keycloakExists,
                        'otp_login'
                    );

                    return [
                        'success' => false,
                        'message' => $inactiveResponse['message'],
                        'auth_type' => 'blocked_inactive_otp',
                        'user_data' => $inactiveResponse['user_data'],
                        'recommendations' => $inactiveResponse['recommendations'],
                        'sync_status' => $inactiveResponse['sync_status'],
                    ];
                }

                // Generic Old SSO OTP authentication failure
                return [
                    'success' => false,
                    'message' => 'Old SSO OTP authentication failed: ' . $oldSsoResult['message'],
                    'auth_type' => 'old_sso_otp',
                ];
            }

            // Step 2: Get complete Old SSO user profile for timestamp comparison
            Log::info('Old SSO OTP authentication successful, fetching complete user profile for timestamp comparison', [
                'username' => $username,
            ]);

            $oldSsoProfileResult = $this->oldSsoClient->searchUser($username);
            if (!$oldSsoProfileResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to fetch Old SSO user profile: ' . $oldSsoProfileResult['message'],
                    'auth_type' => 'old_sso_otp',
                ];
            }

            $oldSsoUserData = $oldSsoProfileResult['data']['data'] ?? null;
            if (!$oldSsoUserData) {
                return [
                    'success' => false,
                    'message' => 'Invalid Old SSO user profile data received',
                    'auth_type' => 'old_sso_otp',
                ];
            }

            Log::info('Old SSO user profile fetched successfully for OTP login', [
                'username' => $username,
                'user_id' => $oldSsoUserData['user_id'] ?? 'unknown',
                'updated_at' => $oldSsoUserData['updated_at'] ?? 'unknown',
                'mobile_verified' => $oldSsoUserData['mobile_verified'] ?? 'unknown',
                'email_verified' => $oldSsoUserData['email_verified'] ?? 'unknown',
            ]);

            // Step 3: Generate fallback password for Keycloak sync
            $fallbackPassword = $this->generateFallbackPassword($username);

            // Step 4: Optimized Keycloak sync using timestamp comparison with fallback password
            $keycloakSyncResult = $this->optimizedKeycloakSync($username, $fallbackPassword, $oldSsoUserData);

            $authResult = $this->syncWithKeycloak($username, $fallbackPassword);
            
            return [
                'success' => true,
                'message' => 'Hybrid OTP authentication successful',
                'tokens' => $authResult['keycloak_tokens'] ?? null,
                'auth_type' => 'hybrid_otp',
                'old_sso_data' => [
                    'data' => $oldSsoUserData, // Use complete profile data instead of login response
                ],
                'keycloak_synced' => $keycloakSyncResult['success'] ?? false,
                'keycloak_message' => $keycloakSyncResult['message'] ?? null
            ];

        } catch (Exception $e) {
            Log::error('Hybrid OTP authentication failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'OTP authentication failed: ' . $e->getMessage(),
                'auth_type' => 'hybrid_otp',
            ];
        }
    }

    /**
     * Request OTP for user
     */
    public function requestOtp(string $username): array
    {
        // First check if user exists
        $userCheck = $this->userExistsBase($username);
        
        if (!$userCheck['status_code'] == 200) {
            return [
                'success' => false,
                'message' => $userCheck['status_code'],
            ];
        }
        $userId = $userCheck['data']['data']['user_id']  ?? null;
        if (!$userId) {
            return [
                'success' => false,
                'message' => 'Invalid user data from old SSO',
            ];
        }

        return $this->oldSsoClient->requestOtp($userId);
    }

    /**
     * Register user in both old SSO and Keycloak
     */
    public function registerUser(array $userData): array
    {
        try {
            // Register in old SSO first
            $oldSsoResult = $this->oldSsoClient->registerUser($userData);
            // dd($oldSsoResult);

            if (!$oldSsoResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Old SSO registration failed: ' . $oldSsoResult['message'],
                    'auth_type' => 'old_sso',
                ];
            }

            return $oldSsoResult;

            // Try to register in Keycloak as well
            // $keycloakResult = $this->registerUserInKeycloak($oldSsoResult['data']['data'], $userData['password']);

            // return [
            //     'success' => true,
            //     'message' => 'Hybrid registration successful',
            //     'user' => $oldSsoResult['data']['data'],
            //     'auth_type' => 'hybrid',
            //     'old_sso_data' => $oldSsoResult['data'],
            //     'keycloak_registered' => $keycloakResult['success'],
            //     'keycloak_message' => $keycloakResult['message'] ?? null,
            // ];

        } catch (Exception $e) {
            Log::error('Hybrid registration failed', [
                'email' => $userData['email'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Registration failed: ' . $e->getMessage(),
                'auth_type' => 'hybrid',
            ];
        }
    }

    /**
     * Find or create user from old SSO data
     */
    private function findOrCreateUserFromOldSso(array $oldSsoData, string $username): User
    {
        // Try to find user by email or phone
        $user = User::where('email_id', $username)
                   ->orWhere('phone', $username)
                   ->first();

        if (!$user) {
            // Create new user from old SSO data
            $user = new User();
            $user->email_id = $oldSsoData['email'] ?? $username;
            $user->phone = $oldSsoData['mobile'] ?? $username;
            $user->first_name = $oldSsoData['first_name'] ?? '';
            $user->last_name = $oldSsoData['last_name'] ?? '';
            $user->username = $username;
            $user->role_id = 2; // Default role for regular users
            $user->status = 1;
            $user->company_id = 1;
            $user->unit_id = 1;
            $user->prosim_user_id = $oldSsoData['id'] ?? uniqid();
            $user->auth_type = 'hybrid';
            $user->save();

            Log::info('Created new user from old SSO data', [
                'user_id' => $user->id,
                'username' => $username,
            ]);
        }

        return $user;
    }

    /**
     * Register user in Keycloak - ACTUAL IMPLEMENTATION
     */
    private function registerUserInKeycloak($user, string $password): array
    {
        try {
            $result = $this->keycloakRegistrationService->registerUser($user, $password);

            if ($result['success']) {
                Log::info('Keycloak user registration successful during login', [
                    'user_id' => $user['user_id'],
                    'keycloak_user_id' => $result['user_id'] ?? 'unknown',
                    'username' => $user['username'] ?: $user['email_id'],
                ]);
            } else {
                Log::error('Keycloak user registration failed during login', [
                    'user_id' => $user['user_id'],
                    'username' => $user['username'] ?: $user['email_id'],
                    'error' => $result['message'],
                ]);
            }

            return $result;

        } catch (Exception $e) {
            Log::error('Exception during Keycloak user registration', [
                'user_id' => $user['user_id'],
                'username' => $user['username'] ?: $user['email_id'],
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak registration error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Sync user with Keycloak
     */
    private function syncWithKeycloak(string $username, string $password): array
    {
        try {
            // Try to authenticate with Keycloak
            $keycloakResult = $this->keycloakService->authenticate($username, $password);
            
            // dd($keycloakResult);
            if ($keycloakResult['success']) {
                return [
                    'success' => true,
                    'keycloak_tokens' => $keycloakResult['keycloak_tokens'],
                    'message' => 'Keycloak sync successful',
                ];
            }

            // If authentication failed, try to update password in Keycloak
            // This would require additional Keycloak admin API calls
            Log::warning('Keycloak sync failed, password may need updating', [
                'username' => $username,
                'keycloak_message' => $keycloakResult['message'],
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak sync failed: ' . $keycloakResult['message'],
            ];

        } catch (Exception $e) {
            Log::error('Keycloak sync error', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak sync error: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate fallback password for Keycloak
     */
    private function generateFallbackPassword(string $username): string
    {
        $pattern = config('old_sso.password_sync.fallback_password_pattern', '123456789');
        
        // For mobile numbers, use the mobile number as password
        if (preg_match('/^91\d{10}$/', $username)) {
            return $username;
        }
        
        return $pattern;
    }

    /**
     * Create user from registration data
     */
    private function createUserFromRegistration(array $userData, array $oldSsoData): User
    {
        $user = new User();
        $user->email_id = $userData['email'];
        $user->phone = $userData['mobile'];
        $user->first_name = $userData['first_name'];
        $user->last_name = $userData['last_name'];
        $user->username = $userData['email'];
        $user->role_id = 2; // Default role for regular users
        $user->status = 1;
        $user->company_id = 1;
        $user->unit_id = 1;
        $user->prosim_user_id = $oldSsoData['id'] ?? uniqid();
        $user->auth_type = 'hybrid';
        $user->save();

        Log::info('Created new user from registration', [
            'user_id' => $user->id,
            'email' => $userData['email'],
        ]);

        return $user;
    }

    /**
     * Register user in Keycloak - ACTUAL IMPLEMENTATION
     */
    private function registerInKeycloak($user, array $userData): array
    {
        try {

            // Use the KeycloakUserRegistrationService to actually register the user
            $keycloakUserData = [
                'username' => $user->username ?? $user->email_id,
                'email' => $userData['email'],
                'first_name' => $userData['first_name'] ?? $user->first_name,
                'last_name' => $userData['last_name'] ?? $user->last_name,
                'password' => $userData['password'],
            ];

            $result = $this->keycloakRegistrationService->registerUser($keycloakUserData);

            if ($result['success']) {
                Log::info('Keycloak registration successful', [
                    'user_id' => $user->id,
                    'keycloak_user_id' => $result['user_id'] ?? 'unknown',
                    'email' => $userData['email'],
                ]);

                return [
                    'success' => true,
                    'message' => 'Keycloak registration completed successfully',
                    'keycloak_user_id' => $result['user_id'] ?? null,
                ];
            } else {
                Log::error('Keycloak registration failed', [
                    'user_id' => $user->id,
                    'error' => $result['message'],
                    'email' => $userData['email'],
                ]);

                return [
                    'success' => false,
                    'message' => 'Keycloak registration failed: ' . $result['message'],
                ];
            }

        } catch (Exception $e) {
            Log::error('Exception during Keycloak registration', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak registration failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Update complete Keycloak user profile when Old SSO auth succeeds but Keycloak auth fails
     */
    public function updateKeycloakUserCredentials(string $username, string $password, array $oldSsoUserData = null): array
    {
        try {
            Log::info('Starting Keycloak user profile and credentials update', [
                'username' => $username,
            ]);

            // Check if user exists in Keycloak
            $userExists = $this->keycloakRegistrationService->userExists($username);

            if (!$userExists) {
                Log::warning('User does not exist in Keycloak for profile update', [
                    'username' => $username,
                ]);
                return [
                    'success' => false,
                    'message' => 'User does not exist in Keycloak',
                ];
            }

            // Get Old SSO user data if not provided
            if (!$oldSsoUserData) {
                $userCheck = $this->userExists($username);
                if ($userCheck['exists'] && $userCheck['old_sso_exists']) {
                    $oldSsoUserData = $userCheck['user_data'];
                }
            }

            $updateResults = [];

            // Update user profile details if Old SSO data is available
            if ($oldSsoUserData) {
                $profileData = [
                    'first_name' => $oldSsoUserData['first_name'] ?? '',
                    'last_name' => $oldSsoUserData['last_name'] ?? '',
                    'email' => $oldSsoUserData['email'] ?? $username,
                ];

                $profileResult = $this->keycloakRegistrationService->updateUserDetails($username, $profileData);
                $updateResults['profile'] = $profileResult;

                Log::info('Keycloak user profile update result', [
                    'username' => $username,
                    'profile_success' => $profileResult['success'],
                    'profile_message' => $profileResult['message'],
                ]);
            }

            // Update password in Keycloak
            $passwordResult = $this->keycloakRegistrationService->updateUserPassword($username, $password);
            $updateResults['password'] = $passwordResult;

            if ($passwordResult['success']) {
                Log::info('Keycloak user profile and credentials updated successfully', [
                    'username' => $username,
                    'keycloak_user_id' => $passwordResult['user_id'] ?? 'unknown',
                    'profile_updated' => isset($updateResults['profile']) ? $updateResults['profile']['success'] : false,
                ]);

                return [
                    'success' => true,
                    'message' => 'Keycloak user profile and credentials updated successfully',
                    'keycloak_user_id' => $passwordResult['user_id'] ?? null,
                    'updates' => $updateResults,
                ];
            } else {
                Log::error('Failed to update Keycloak user credentials', [
                    'username' => $username,
                    'error' => $passwordResult['message'],
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to update Keycloak credentials: ' . $passwordResult['message'],
                    'updates' => $updateResults,
                ];
            }

        } catch (Exception $e) {
            Log::error('Exception during Keycloak profile update', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak profile update failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Verify mobile OTP and activate user
     */
    public function verifyMobileOtp(string $username, string $otp): array
    {
        try {

            // First, get user data from Old SSO to get user_id
            $userCheck = $this->userExistsBase($username);

            if (!$userCheck['status_code'] == 200) {
                return [
                    'success' => false,
                    'message' => $userCheck['message'],
                ];
            }

            $userId = $userCheck['data']['data']['user_id'] ?? null;

            if (!$userId) {
                Log::error('User ID not found in Old SSO data', [
                    'username' => $username,
                    'user_data' => $userCheck['user_data'],
                ]);
                return [
                    'success' => false,
                    'message' => 'User ID not found in Old SSO data',
                ];
            }

            // Verify OTP with Old SSO
            $otpResult = $this->oldSsoClient->verifyMobileOtp($userId, $otp);

            if (!$otpResult['success']) {
                Log::warning('Mobile OTP verification failed', [
                    'username' => $username,
                    'user_id' => $userId,
                    'message' => $otpResult['message'],
                ]);

                return [
                    'success' => false,
                    'message' => $otpResult['message'] ?? 'OTP verification failed',
                ];
            }

            // Note: User status becomes active and mobile_verified becomes 1 in Old SSO
            // We don't modify the local database as per your requirement

            return [
                'success' => true,
                'message' => 'Mobile OTP verified successfully. User is now active.',
                'user_data' => $otpResult['data'] ?? null,
            ];

        } catch (Exception $e) {
            Log::error('Exception during mobile OTP verification', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Mobile OTP verification failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check if Old SSO response indicates an inactive/unverified user
     * Follows Single Responsibility Principle
     */
    private function checkForInactiveUser(array $oldSsoResult): array
    {
        $result = [
            'is_inactive' => false,
            'user_data' => null,
            'error_type' => null,
            'message' => null,
        ];

        // Check if response indicates inactive user
        if (!$oldSsoResult['success'] && isset($oldSsoResult['data']['status_code']) && $oldSsoResult['data']['status_code'] === 1010) {
            $error = $oldSsoResult['data']['error'] ?? '';

            // Handle different types of inactive user errors
            if (in_array($error, ['User is not active/verified', 'User is not active to login.'])) {
                $result['is_inactive'] = true;
                $result['user_data'] = $oldSsoResult['data']['data'] ?? null;
                $result['error_type'] = $error === 'User is not active/verified' ? 'not_verified' : 'not_active_login';
                $result['message'] = $error;

                Log::info('Detected inactive/unverified user', [
                    'user_id' => $result['user_data']['user_id'] ?? 'unknown',
                    'error_type' => $result['error_type'],
                    'error_message' => $error,
                ]);
            }
        }

        return $result;
    }

    /**
     * Generate inactive user response
     * Follows Single Responsibility Principle
     */
    private function generateInactiveUserResponse(array $inactiveUserData, bool $keycloakExists, string $operation = 'search'): array
    {
        $baseResponse = [
            'exists' => true,
            'message' => 'User found but requires mobile verification',
            'user_data' => $inactiveUserData,
            'old_sso_exists' => false, // Not active in Old SSO
            'keycloak_exists' => $keycloakExists,
            'sync_status' => 'requires_mobile_verification',
            'recommendations' => [
                'can_login_password' => false,
                'can_login_otp' => false,
                'needs_mobile_verification' => true,
                'mobile_verified' => (bool)($inactiveUserData['mobile_verified'] ?? false),
                'email_verified' => (bool)($inactiveUserData['email_verified'] ?? false),
                'status' => $inactiveUserData['status'] ?? 'inactive',
                'verification_message' => 'Please verify your mobile number using OTP to activate your account',
            ],
        ];

        // Customize message based on operation
        if ($operation === 'login') {
            $baseResponse['message'] = 'Login failed: User requires mobile verification';
            $baseResponse['recommendations']['login_blocked_reason'] = 'Account not activated - mobile verification required';
        } elseif ($operation === 'otp_login') {
            $baseResponse['message'] = 'OTP login failed: User requires mobile verification';
            $baseResponse['recommendations']['login_blocked_reason'] = 'Account not activated - mobile verification required';
            $baseResponse['recommendations']['otp_login_blocked'] = true;
        }

        return $baseResponse;
    }

    /**
     * Verify and sync user details between Old SSO and Keycloak
     * Ensures data consistency across both systems
     */
    private function verifyAndSyncUserDetails(string $username, array $oldSsoUserData): array
    {
        try {
            Log::info('Starting user details verification and sync', [
                'username' => $username,
                'old_sso_user_id' => $oldSsoUserData['user_id'] ?? 'unknown',
            ]);

            // Get current Keycloak user details
            $keycloakUserDetails = $this->getKeycloakUserDetails($username);

            if (!$keycloakUserDetails) {
                Log::warning('Could not retrieve Keycloak user details for comparison', [
                    'username' => $username,
                ]);
                return [
                    'success' => false,
                    'message' => 'Could not retrieve Keycloak user details',
                    'sync_needed' => false,
                ];
            }

            // Compare user details
            $differences = $this->compareUserDetails($oldSsoUserData, $keycloakUserDetails);

            if (empty($differences)) {
                Log::info('User details are already in sync', [
                    'username' => $username,
                ]);
                return [
                    'success' => true,
                    'message' => 'User details are already synchronized',
                    'sync_needed' => false,
                    'differences' => [],
                ];
            }

            Log::info('User details differences detected, syncing from Old SSO to Keycloak', [
                'username' => $username,
                'differences' => $differences,
            ]);

            // Update Keycloak with Old SSO data
            $updateData = [];
            foreach ($differences as $field => $diff) {
                switch ($field) {
                    case 'first_name':
                        $updateData['first_name'] = $oldSsoUserData['first_name'] ?? '';
                        break;
                    case 'last_name':
                        $updateData['last_name'] = $oldSsoUserData['last_name'] ?? '';
                        break;
                    case 'email':
                        $updateData['email'] = $oldSsoUserData['email'] ?? $username;
                        break;
                }
            }

            if (!empty($updateData)) {
                $syncResult = $this->keycloakRegistrationService->updateUserDetails($username, $updateData);

                if ($syncResult['success']) {
                    Log::info('User details successfully synced to Keycloak', [
                        'username' => $username,
                        'updated_fields' => array_keys($updateData),
                        'keycloak_user_id' => $syncResult['user_id'] ?? 'unknown',
                    ]);

                    return [
                        'success' => true,
                        'message' => 'User details synchronized successfully',
                        'sync_needed' => true,
                        'differences' => $differences,
                        'updated_fields' => array_keys($updateData),
                        'keycloak_user_id' => $syncResult['user_id'] ?? null,
                    ];
                } else {
                    Log::error('Failed to sync user details to Keycloak', [
                        'username' => $username,
                        'error' => $syncResult['message'],
                        'attempted_updates' => $updateData,
                    ]);

                    return [
                        'success' => false,
                        'message' => 'Failed to sync user details: ' . $syncResult['message'],
                        'sync_needed' => true,
                        'differences' => $differences,
                    ];
                }
            }

            return [
                'success' => true,
                'message' => 'No actionable differences found',
                'sync_needed' => false,
                'differences' => $differences,
            ];

        } catch (Exception $e) {
            Log::error('Exception during user details verification and sync', [
                'username' => $username,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'User details sync failed: ' . $e->getMessage(),
                'sync_needed' => false,
            ];
        }
    }

    /**
     * Get Keycloak user details for comparison
     */
    private function getKeycloakUserDetails(string $username): ?array
    {
        try {
            $adminToken = $this->keycloakRegistrationService->getAdminToken();
            if (!$adminToken) {
                return null;
            }

            $config = config('keycloak');
            $usersUrl = $config['auth_server_url'] . '/admin/realms/' . $config['realm'] . '/users';

            $response = \Illuminate\Support\Facades\Http::withToken($adminToken)
                ->get($usersUrl, ['username' => $username]);

            if ($response->successful()) {
                $users = $response->json();
                return $users[0] ?? null;
            }

            return null;
        } catch (Exception $e) {
            Log::error('Failed to get Keycloak user details', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Compare user details between Old SSO and Keycloak
     */
    private function compareUserDetails(array $oldSsoData, array $keycloakData): array
    {
        $differences = [];

        // Compare first name
        $oldSsoFirstName = trim($oldSsoData['first_name'] ?? '');
        $keycloakFirstName = trim($keycloakData['firstName'] ?? '');
        if ($oldSsoFirstName !== $keycloakFirstName) {
            $differences['first_name'] = [
                'old_sso' => $oldSsoFirstName,
                'keycloak' => $keycloakFirstName,
            ];
        }

        // Compare last name
        $oldSsoLastName = trim($oldSsoData['last_name'] ?? '');
        $keycloakLastName = trim($keycloakData['lastName'] ?? '');
        if ($oldSsoLastName !== $keycloakLastName) {
            $differences['last_name'] = [
                'old_sso' => $oldSsoLastName,
                'keycloak' => $keycloakLastName,
            ];
        }

        // Compare email
        $oldSsoEmail = strtolower(trim($oldSsoData['email'] ?? ''));
        $keycloakEmail = strtolower(trim($keycloakData['email'] ?? ''));
        if ($oldSsoEmail !== $keycloakEmail && !empty($oldSsoEmail)) {
            $differences['email'] = [
                'old_sso' => $oldSsoEmail,
                'keycloak' => $keycloakEmail,
            ];
        }

        return $differences;
    }

    /**
     * Optimized Keycloak sync using timestamp comparison for better performance
     */
    private function optimizedKeycloakSync(string $username, string $password, array $oldSsoUserData): array
    {
        try {

            // Step 1: Check if user exists in Keycloak
            $keycloakExists = $this->keycloakRegistrationService->userExists($username);
            // dd($keycloakExists);
            if (!$keycloakExists) {
                // User doesn't exist in Keycloak - register them
                Log::info('User not found in Keycloak, registering new user', [
                    'username' => $username,
                ]);

                $registrationResult = $this->registerUserInKeycloak($oldSsoUserData, $password);
                dd($registrationResult);
                return [
                    'success' => $registrationResult['success'],
                    'message' => $registrationResult['message'],
                    'keycloak_tokens' => $registrationResult['tokens'] ?? null,
                    'sync_info' => [
                        'details_verified' => true,
                        'details_synced' => $registrationResult['success'],
                        'sync_method' => 'new_registration',
                        'sync_message' => $registrationResult['success'] ? 'New user registered in Keycloak' : 'Registration failed',
                    ],
                ];
            }

            $updateResult = $this->updateCompleteKeycloakProfile($username, $password, $oldSsoUserData);

            return [
                'success' => $updateResult['success'],
                'message' => $updateResult['message'],
                'keycloak_tokens' => $updateResult['tokens'] ?? null,
                'sync_info' => [
                    'details_verified' => true,
                    'details_synced' => $updateResult['success'],
                    'sync_method' => 'update_complete_profile',
                    'sync_message' => $updateResult['success'] ? 'Profile updated in Keycloak' : 'Update failed',
                ],
            ];

        } catch (Exception $e) {
            Log::error('Exception during optimized Keycloak sync', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak sync failed: ' . $e->getMessage(),
                'sync_info' => [
                    'details_verified' => false,
                    'details_synced' => false,
                    'sync_method' => 'failed',
                    'sync_message' => 'Sync failed due to exception',
                ],
            ];
        }
    }

    /**
     * Check if Keycloak sync is needed using timestamp comparison
     */
    private function isKeycloakSyncNeeded(string $username, array $oldSsoUserData): array
    {
        try {
            // Get Keycloak user details with timestamp
            $keycloakUserDetails = $this->getKeycloakUserDetails($username);

            if (!$keycloakUserDetails) {
                return [
                    'needs_sync' => true,
                    'reason' => 'Could not retrieve Keycloak user details',
                ];
            }

            // Compare timestamps
            $oldSsoUpdatedAt = $oldSsoUserData['updated_at'] ?? null;
            $keycloakModifiedTimestamp = $keycloakUserDetails['createdTimestamp'] ?? null;

            if (!$oldSsoUpdatedAt) {
                return [
                    'needs_sync' => true,
                    'reason' => 'Old SSO updated_at timestamp not available',
                ];
            }

            if (!$keycloakModifiedTimestamp) {
                return [
                    'needs_sync' => true,
                    'reason' => 'Keycloak timestamp not available',
                ];
            }

            // Convert timestamps for comparison
            $oldSsoTimestamp = strtotime($oldSsoUpdatedAt);
            $keycloakTimestamp = intval($keycloakModifiedTimestamp / 1000); // Keycloak uses milliseconds

            Log::info('Timestamp comparison for sync decision', [
                'username' => $username,
                'old_sso_updated_at' => $oldSsoUpdatedAt,
                'old_sso_timestamp' => $oldSsoTimestamp,
                'keycloak_timestamp' => $keycloakTimestamp,
                'old_sso_newer' => $oldSsoTimestamp > $keycloakTimestamp,
                'time_difference_seconds' => $oldSsoTimestamp - $keycloakTimestamp,
            ]);

            // Add a small buffer (5 minutes) to avoid unnecessary syncs for minor time differences
            $timeDifference = $oldSsoTimestamp - $keycloakTimestamp;
            $syncThreshold = 300; // 5 minutes in seconds

            if ($timeDifference > $syncThreshold) {
                return [
                    'needs_sync' => true,
                    'reason' => 'Old SSO data is significantly newer than Keycloak data',
                    'old_sso_timestamp' => $oldSsoTimestamp,
                    'keycloak_timestamp' => $keycloakTimestamp,
                    'time_difference_seconds' => $timeDifference,
                ];
            }

            return [
                'needs_sync' => false,
                'reason' => 'Keycloak data is up to date (within sync threshold)',
                'old_sso_timestamp' => $oldSsoTimestamp,
                'keycloak_timestamp' => $keycloakTimestamp,
                'time_difference_seconds' => $timeDifference,
            ];

        } catch (Exception $e) {
            Log::error('Error during timestamp comparison', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'needs_sync' => true,
                'reason' => 'Error during timestamp comparison - forcing sync',
            ];
        }
    }

    /**
     * Register new user in Keycloak with complete profile data
     */
    // private function registerNewUserInKeycloak(string $username, string $password, array $oldSsoUserData): array
    // {
    //     try {
    //         $result = $this->keycloakRegistrationService->registerUser($oldSsoUserData, $password);

    //         if ($result['success']) {
    //             // Try to get tokens after registration
    //             $authResult = $this->syncWithKeycloak($username, $password);
    //             $result['tokens'] = $authResult['keycloak_tokens'] ?? null;
    //         }

    //         return $result;

    //     } catch (Exception $e) {
    //         Log::error('Error registering new user in Keycloak', [
    //             'username' => $username,
    //             'error' => $e->getMessage(),
    //         ]);

    //         return [
    //             'success' => false,
    //             'message' => 'Registration failed: ' . $e->getMessage(),
    //         ];
    //     }
    // }

    /**
     * Update complete Keycloak profile including mobile verification status
     */
    private function updateCompleteKeycloakProfile(string $username, string $password, array $oldSsoUserData): array
    {
        try {

            // $updatedFields = [];

            // // Update basic profile details
            // $profileData = [
            //     'first_name' => $oldSsoUserData['first_name'] ?? '',
            //     'last_name' => $oldSsoUserData['last_name'] ?? '',
            //     'email' => $oldSsoUserData['email'] ?? $username,
            // ];

            // $profileResult = $this->keycloakRegistrationService->updateUserDetails($username, $profileData);
            // if ($profileResult['success']) {
            //     $updatedFields = array_merge($updatedFields, ['first_name', 'last_name', 'email']);
            // }

            // // Update password
            // $passwordResult = $this->keycloakRegistrationService->updateUserPassword($username, $password);
            // if ($passwordResult['success']) {
            //     $updatedFields[] = 'password';
            // }
            $preparedData = $this->keycloakRegistrationService->prepareUserData($oldSsoUserData, $password);
            // dd($preparedData);
            // Update mobile verification attributes (if supported by Keycloak setup)
            return $this->updateKeycloakUserAttributes($username, $preparedData);
            // dd($attributesResult);

            // if ($attributesResult['success']) {
            //     $updatedFields = array_merge($updatedFields, ['mobile_verified', 'email_verified', 'mobile']);
            // }

            // $overallSuccess = $profileResult['success'] && $passwordResult['success'];

            // return [
            //     'success' => $attributesResult[],
            //     'message' => $overallSuccess ? 'Complete profile updated successfully' : 'Partial profile update completed',
            //     // 'updated_fields' => $updatedFields,
            //     // 'profile_result' => $profileResult,
            //     // 'password_result' => $passwordResult,
            //     // 'attributes_result' => $attributesResult,
            // ];

        } catch (Exception $e) {
            Log::error('Error updating complete Keycloak profile', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Complete profile update failed: ' . $e->getMessage(),
                'updated_fields' => [],
            ];
        }
    }

    /**
     * Update Keycloak user attributes (mobile verification, etc.)
     */
    private function updateKeycloakUserAttributes(string $username, array $attributes): array
    {
        try {
            $adminToken = $this->keycloakRegistrationService->getAdminToken();
            if (!$adminToken) {
                return [
                    'success' => false,
                    'message' => 'Failed to get admin token for attributes update',
                ];
            }

            // Get user ID
            $userId = $this->getUserIdByUsername($username, $adminToken);
            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not found for attributes update',
                ];
            }

            $config = config('keycloak');
            $attributesUrl = $config['auth_server_url'] . '/admin/realms/' . $config['realm'] . '/users/' . $userId;


            $response = \Illuminate\Support\Facades\Http::withToken($adminToken)
                ->put($attributesUrl, $attributes);

            if ($response->successful()) {
                Log::info('Keycloak user attributes updated successfully', [
                    'username' => $username,
                    'attributes' => array_keys($attributes),
                ]);

                return [
                    'success' => true,
                    'message' => 'User attributes updated successfully',
                ];
            } else {
                Log::error('Failed to update Keycloak user attributes', [
                    'username' => $username,
                    'status' => $response->status(),
                    'response' => $response->json(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to update user attributes: ' . $response->body(),
                ];
            }

        } catch (Exception $e) {
            Log::error('Exception updating Keycloak user attributes', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Attributes update failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Get Keycloak user ID by username
     */
    private function getUserIdByUsername(string $username, string $adminToken): ?string
    {
        try {
            $config = config('keycloak');
            $usersUrl = $config['auth_server_url'] . '/admin/realms/' . $config['realm'] . '/users';

            $response = \Illuminate\Support\Facades\Http::withToken($adminToken)
                ->get($usersUrl, ['username' => $username]);

            if ($response->successful()) {
                $users = $response->json();
                return $users[0]['id'] ?? null;
            }

            return null;
        } catch (Exception $e) {
            Log::error('Failed to get user ID by username', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Complete Keycloak account setup when account exists but is not fully set up
     */
    private function completeKeycloakAccountSetup(string $username, string $password, array $oldSsoUserData): array
    {
        try {
            Log::info('Starting Keycloak account setup completion', [
                'username' => $username,
            ]);

            $adminToken = $this->keycloakRegistrationService->getAdminToken();
            if (!$adminToken) {
                return [
                    'success' => false,
                    'message' => 'Failed to get admin token for account setup',
                ];
            }

            // Get user ID
            $userId = $this->getUserIdByUsername($username, $adminToken);
            if (!$userId) {
                return [
                    'success' => false,
                    'message' => 'User not found for account setup',
                ];
            }

            $config = config('keycloak');
            $userUrl = $config['auth_server_url'] . '/admin/realms/' . $config['realm'] . '/users/' . $userId;

            // Complete account setup by ensuring all required fields are set
            $setupData = [
                'enabled' => true,
                'emailVerified' => true, // Mark email as verified to avoid setup issues
                'firstName' => $oldSsoUserData['first_name'] ?? '',
                'lastName' => $oldSsoUserData['last_name'] ?? '',
                'email' => $oldSsoUserData['email'] ?? $username,
                'attributes' => [
                    'mobile_verified' => [(string)($oldSsoUserData['mobile_verified'] ?? '0')],
                    'email_verified' => [(string)($oldSsoUserData['email_verified'] ?? '1')], // Set to 1 to complete setup
                    'mobile' => [$oldSsoUserData['mobile'] ?? ''],
                ],
            ];

            // Update user profile
            $profileResponse = \Illuminate\Support\Facades\Http::withToken($adminToken)
                ->put($userUrl, $setupData);

            if (!$profileResponse->successful()) {
                Log::error('Failed to update user profile during account setup', [
                    'username' => $username,
                    'status' => $profileResponse->status(),
                    'response' => $profileResponse->json(),
                ]);
            }

            // Reset password to ensure it's properly set
            $passwordData = [
                'type' => 'password',
                'value' => $password,
                'temporary' => false
            ];

            $passwordResponse = \Illuminate\Support\Facades\Http::withToken($adminToken)
                ->put($userUrl . '/reset-password', $passwordData);

            if (!$passwordResponse->successful()) {
                Log::error('Failed to reset password during account setup', [
                    'username' => $username,
                    'status' => $passwordResponse->status(),
                    'response' => $passwordResponse->json(),
                ]);
            }

            // Remove any required actions that might be blocking the account
            $actionsResponse = \Illuminate\Support\Facades\Http::withToken($adminToken)
                ->put($userUrl . '/execute-actions-email', []);

            $success = $profileResponse->successful() && $passwordResponse->successful();

            if ($success) {
                Log::info('Keycloak account setup completed successfully', [
                    'username' => $username,
                    'user_id' => $userId,
                ]);

                return [
                    'success' => true,
                    'message' => 'Account setup completed successfully',
                    'user_id' => $userId,
                ];
            } else {
                Log::error('Keycloak account setup partially failed', [
                    'username' => $username,
                    'profile_success' => $profileResponse->successful(),
                    'password_success' => $passwordResponse->successful(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Account setup partially failed',
                    'profile_success' => $profileResponse->successful(),
                    'password_success' => $passwordResponse->successful(),
                ];
            }

        } catch (Exception $e) {
            Log::error('Exception during Keycloak account setup completion', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Account setup failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Request OTP for forgot password
     * Separate method to handle forgot password OTP requests with type='forget_password'
     */
    public function requestForgotPasswordOtp(string $username): array
    {
        try {
            // First check if user exists
            $userCheck = $this->userExistsBase($username);

            if (!$userCheck['status_code'] == 200) {
                return [
                    'success' => false,
                    'message' => $userCheck['message'],
                    'status_code' => $userCheck['status_code'],
                ];
            }

            $userId = $userCheck['data']['data']['user_id'] ?? null;

            if (!$userId) {
                Log::error('User ID not found in Old SSO data for forgot password', [
                    'username' => $username,
                    'user_data' => $userCheck['data'],
                ]);
                return [
                    'success' => false,
                    'message' => 'User ID not found in Old SSO data',
                    'status_code' => 404,
                ];
            }

            Log::info('Requesting forgot password OTP', [
                'username' => $username,
                'user_id' => $userId,
            ]);

            // Request OTP with type='forget_password'
            return $this->oldSsoClient->requestForgotPasswordOtp($userId, $username);

        } catch (Exception $e) {
            Log::error('Forgot password OTP request failed', [
                'username' => $username,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to request forgot password OTP: ' . $e->getMessage(),
                'status_code' => 500,
            ];
        }
    }

    /**
     * Verify OTP for forgot password and get auth code
     * Separate method to handle forgot password OTP verification with type='forget_password'
     */
    public function verifyForgotPasswordOtp(string $username, string $otp): array
    {
        try {
            // First, get user data from Old SSO to get user_id
            $userCheck = $this->userExistsBase($username);

            if (!$userCheck['status_code'] == 200) {
                return [
                    'success' => false,
                    'message' => $userCheck['message'],
                    'status_code' => $userCheck['status_code'],
                ];
            }

            $userId = $userCheck['data']['data']['user_id'] ?? null;

            if (!$userId) {
                Log::error('User ID not found in Old SSO data for forgot password verification', [
                    'username' => $username,
                    'user_data' => $userCheck['data'],
                ]);
                return [
                    'success' => false,
                    'message' => 'User ID not found in Old SSO data',
                    'status_code' => 404,
                ];
            }

            Log::info('Verifying forgot password OTP', [
                'username' => $username,
                'user_id' => $userId,
            ]);

            // Verify OTP with type='forget_password'
            $otpResult = $this->oldSsoClient->verifyForgotPasswordOtp($userId, $otp);

            if (!$otpResult['success']) {
                Log::warning('Forgot password OTP verification failed', [
                    'username' => $username,
                    'user_id' => $userId,
                    'message' => $otpResult['message'],
                ]);

                return [
                    'success' => false,
                    'message' => $otpResult['message'] ?? 'Forgot password OTP verification failed',
                    'status_code' => $otpResult['status_code'] ?? 400,
                ];
            }

            Log::info('Forgot password OTP verification successful', [
                'username' => $username,
                'user_id' => $userId,
                'has_auth_code' => isset($otpResult['data']['fp_auth_code']),
            ]);

            return [
                'success' => true,
                'message' => 'Forgot password OTP verified successfully. Auth code generated.',
                'data' => $otpResult['data'] ?? null,
                'status_code' => 200,
            ];

        } catch (Exception $e) {
            Log::error('Forgot password OTP verification failed', [
                'username' => $username,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to verify forgot password OTP: ' . $e->getMessage(),
                'status_code' => 500,
            ];
        }
    }
}
