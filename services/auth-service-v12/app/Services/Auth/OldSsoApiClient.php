<?php

namespace App\Services\Auth;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Old SSO API Client
 * 
 * Handles HTTP communication with the legacy SSO system
 */
class OldSsoApiClient
{
    private array $config;
    private array $defaultHeaders;

    public function __construct()
    {
        $this->config = config('old_sso');
        $this->defaultHeaders = $this->config['headers'] ?? [];
    }

    /**
     * Search for a user in the old SSO system
     */
    public function searchUser(string $username): array
    {
        try {
            $url = $this->config['domain_1'] . $this->config['endpoints']['user_search'];
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->get($url, [
                    'source' => $this->config['source'],
                    'api_key' => $this->config['api_key'],
                    'username' => $username,
                ]);

            return $this->handleResponse($response, 'user_search');

        } catch (Exception $e) {
            Log::error('Old SSO user search failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'User search failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Request OTP for user login
     */
    public function requestOtp(string $userId): array
    {
        try {
            $url = $this->config['domain_2'] . $this->config['endpoints']['user_otp'];
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->asForm()
                ->post($url, [
                    'api_key' => $this->config['api_key'],
                    'user_id' => $userId,
                    'source' => $this->config['source'],
                ]);

            return $this->handleResponse($response, 'request_otp');

        } catch (Exception $e) {
            Log::error('Old SSO OTP request failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'OTP request failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Login user with password
     */
    public function loginWithPassword(string $username, string $password): array
    {
        try {
            $url = $this->config['domain_1'] . $this->config['endpoints']['user_login'];
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->asForm()
                ->post($url, [
                    'source' => $this->config['source'],
                    'grant_type' => 'user_login',
                    'client_id' => $this->config['client_id'],
                    'client_secret' => $this->config['client_secret'],
                    'api_key' => $this->config['api_key'],
                    'username' => $username,
                    'password' => $password,
                ]);

            return $this->handleResponse($response, 'login_password');

        } catch (Exception $e) {
            Log::error('Old SSO password login failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Password login failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Login user with OTP
     */
    public function loginWithOtp(string $username, string $otp): array
    {
        try {
            $url = $this->config['domain_1'] . $this->config['endpoints']['user_login'];
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->asForm()
                ->post($url, [
                    'source' => $this->config['source'],
                    'grant_type' => 'user_login',
                    'client_id' => $this->config['client_id'],
                    'client_secret' => $this->config['client_secret'],
                    'api_key' => $this->config['api_key'],
                    'username' => $username,
                    'login_otp' => $otp,
                    'platform' => 'web',
                    'auto_login' => 0
                ]);

            return $this->handleResponse($response, 'login_otp');

        } catch (Exception $e) {
            Log::error('Old SSO OTP login failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'OTP login failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Register a new user
     */
    public function registerUser(array $userData): array
    {
        try {
            $url = $this->config['domain_2'] . $this->config['endpoints']['user_register'];
            
            $headers = array_merge($this->defaultHeaders, [
                'Origin' => $this->config['ref_url_domain'],
                'Referer' => $this->config['ref_url_domain'] . '/',
            ]);

            $formData = [
                'api_key' => $this->config['api_key'],
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
                'mobile' => $userData['mobile'],
                'password' => $userData['password'],
                'password_confirmation' => $userData['password_confirmation'],
            ];

            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($headers)
                ->asForm()
                ->post($url, $formData);

            return $this->handleResponse($response, 'register_user');

        } catch (Exception $e) {
            Log::error('Old SSO user registration failed', [
                'email' => $userData['email'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'User registration failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Handle HTTP response and standardize the format
     */
    private function handleResponse(Response $response, string $operation): array
    {
        $statusCode = $response->status();
        $responseData = $response->json();

        Log::info("Old SSO API response for {$operation}", [
            'status_code' => $statusCode,
            'response_data' => $responseData,
        ]);

        if ($response->successful()) {
            return [
                'success' => true,
                'message' => "Operation {$operation} completed successfully",
                'data' => $responseData,
                'status_code' => $statusCode,
            ];
        }

        return [
            'success' => false,
            'message' => $responseData['message'] ?? "Operation {$operation} failed",
            'data' => $responseData,
            'status_code' => $statusCode,
        ];
    }

    /**
     * Verify mobile OTP
     */
    public function verifyMobileOtp(string $userId, string $otp): array
    {
        try {
            $url = $this->config['domain_2'] . '/api/v2/users/verifyotp';

            $headers = array_merge($this->config['headers'], [
                'Origin' => $this->config['ref_url_domain'],
                'Referer' => $this->config['ref_url_domain'] . '/',
            ]);

            Log::info('Old SSO mobile OTP verification request', [
                'user_id' => $userId,
                'url' => $url,
            ]);

            $formData = [
                'api_key' => $this->config['api_key'],
                'user_id' => $userId,
                'otp' => $otp,
                'otp_type' => 'mobile',
                'source' => $this->config['source'],
            ];

            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($headers)
                ->asForm()
                ->post($url, $formData);

            return $this->handleResponse($response, 'verify_mobile_otp');

        } catch (Exception $e) {
            Log::error('Old SSO mobile OTP verification failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Mobile OTP verification failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Request OTP for forgot password
     * Uses Laravel HTTP client with config-based URLs and headers
     */
    public function requestForgotPasswordOtp(string $userId, string $username): array
    {
        try {
            $url = $this->config['domain_2'] . $this->config['endpoints']['user_otp'];

            Log::info('Old SSO forgot password OTP request', [
                'user_id' => $userId,
                'username' => $username,
                'url' => $url,
            ]);

            // Prepare headers with Origin and Referer from config
            $headers = array_merge($this->config['headers'], [
                'Origin' => $this->config['ref_url_domain'],
                'Referer' => $this->config['ref_url_domain'] . '/',
            ]);

            // Prepare form data
            $formData = [
                'api_key' => $this->config['api_key'],
                'username' => $username,
                'type' => 'forget_password',
                'source' => $this->config['source'],
            ];

            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($headers)
                ->asForm()
                ->post($url, $formData);

            Log::info('Old SSO forgot password OTP response', [
                'user_id' => $userId,
                'username' => $username,
                'status_code' => $response->status(),
                'response' => $response->json(),
            ]);

            return $this->handleResponse($response, 'forgot_password_otp_request');

        } catch (Exception $e) {
            Log::error('Old SSO forgot password OTP request failed', [
                'user_id' => $userId,
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Forgot password OTP request failed: ' . $e->getMessage(),
                'data' => null,
                'status_code' => 500,
            ];
        }
    }

    /**
     * Verify OTP for forgot password
     * Uses Laravel HTTP client with config-based URLs and headers
     */
    public function verifyForgotPasswordOtp(string $userId, string $otp): array
    {
        try {
            $url = $this->config['domain_2'] . $this->config['endpoints']['user_verify_otp'];

            Log::info('Old SSO forgot password OTP verification request', [
                'user_id' => $userId,
                'url' => $url,
            ]);

            // Prepare headers with Origin and Referer from config
            $headers = array_merge($this->config['headers'], [
                'Origin' => $this->config['ref_url_domain'],
                'Referer' => $this->config['ref_url_domain'] . '/',
            ]);

            // Prepare form data
            $formData = [
                'api_key' => $this->config['api_key'],
                'user_id' => $userId,
                'otp' => $otp,
                'otp_type' => 'mobile',
                'type' => 'forget_password',
                'source' => $this->config['source'],
            ];

            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($headers)
                ->asForm()
                ->post($url, $formData);

            Log::info('Old SSO forgot password OTP verification response', [
                'user_id' => $userId,
                'status_code' => $response->status(),
                'response' => $response->json(),
            ]);

            return $this->handleResponse($response, 'forgot_password_otp_verification');

        } catch (Exception $e) {
            Log::error('Old SSO forgot password OTP verification failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Forgot password OTP verification failed: ' . $e->getMessage(),
                'data' => null,
                'status_code' => 500,
            ];
        }
    }

    /**
     * Reset password using auth code
     * Uses Laravel HTTP client with config-based URLs and headers
     */
    public function resetPassword(string $userId, string $fpAuthCode, string $newPassword, string $otpType = 'email'): array
    {
        try {
            $url = $this->config['domain_2'] . $this->config['endpoints']['user_reset_password'];

            Log::info('Old SSO password reset request', [
                'user_id' => $userId,
                'otp_type' => $otpType,
                'url' => $url,
            ]);

            // Prepare headers with Origin and Referer from config
            $headers = array_merge($this->config['headers'], [
                'Origin' => $this->config['ref_url_domain'],
                'Referer' => $this->config['ref_url_domain'] . '/',
            ]);

            // Prepare form data
            $formData = [
                'api_key' => $this->config['api_key'],
                'user_id' => $userId,
                'fp_auth_code' => $fpAuthCode,
                'password' => $newPassword,
                'otp_type' => $otpType,
                'source' => $this->config['source'],
            ];

            // dd($formData);

            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($headers)
                ->asForm()
                ->post($url, $formData);
                // dd($response->json());

            Log::info('Old SSO password reset response', [
                'user_id' => $userId,
                'status_code' => $response->status(),
                'response' => $response->json(),
            ]);

            return $this->handleResponse($response, 'password_reset');

        } catch (Exception $e) {
            Log::error('Old SSO password reset failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Password reset failed: ' . $e->getMessage(),
                'data' => null,
                'status_code' => 500,
            ];
        }
    }
}
