<?php

namespace App\Services\Auth;

use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Old SSO API Client
 * 
 * Handles HTTP communication with the legacy SSO system
 */
class OldSsoApiClient
{
    private array $config;
    private array $defaultHeaders;

    public function __construct()
    {
        $this->config = config('old_sso');
        $this->defaultHeaders = $this->config['headers'] ?? [];
    }

    /**
     * Search for a user in the old SSO system
     */
    public function searchUser(string $username): array
    {
        try {
            $url = $this->config['domain_1'] . $this->config['endpoints']['user_search'];
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->get($url, [
                    'source' => $this->config['source'],
                    'api_key' => $this->config['api_key'],
                    'username' => $username,
                ]);

            return $this->handleResponse($response, 'user_search');

        } catch (Exception $e) {
            Log::error('Old SSO user search failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'User search failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Request OTP for user login
     */
    public function requestOtp(string $userId, string $username, $type): array
    {
        try {
            $url = $this->config['domain_2'] . $this->config['endpoints']['user_otp'];
            // dd($url);
            //https://ssoapi.cubeonebiz.com/api/v2/users/otp
            //https://ssoapi.cubeonebiz.com/api/v2/users/otp

            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://ssoapi.cubeonebiz.com/api/v2/users/otp',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('api_key' => '5b2f4b31f524b6603d81dbcaabd8121078d00f1614a294c3207e39951f7054c8','username' => '************','type' => 'forget_password','source' => 'VEZA'),
            CURLOPT_HTTPHEADER => array(
                'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:140.0) Gecko/******** Firefox/140.0',
                'Accept: application/json, text/plain, */*',
                'Accept-Language: en-US,en;q=0.5',
                'Accept-Encoding: gzip, deflate, br, zstd',
                'Origin: https://account.cubeonebiz.com',
                'Sec-GPC: 1',
                'Connection: keep-alive',
                'Referer: https://account.cubeonebiz.com/',
                'Sec-Fetch-Dest: empty',
                'Sec-Fetch-Mode: cors',
                'Sec-Fetch-Site: same-site',
                'Priority: u=0'
            ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            dd($response);
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->asForm()
                ->post($url, [
                    'api_key' => $this->config['api_key'],
                    "username" => $username,
                    'source' => 'VEZA',
                    "type" => $type
                ]);
            dd($response->json());

            return $this->handleResponse($response, 'request_otp');

        } catch (Exception $e) {
            Log::error('Old SSO OTP request failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'OTP request failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Login user with password
     */
    public function loginWithPassword(string $username, string $password): array
    {
        try {
            $url = $this->config['domain_1'] . $this->config['endpoints']['user_login'];
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->asForm()
                ->post($url, [
                    'source' => $this->config['source'],
                    'grant_type' => 'user_login',
                    'client_id' => $this->config['client_id'],
                    'client_secret' => $this->config['client_secret'],
                    'api_key' => $this->config['api_key'],
                    'username' => $username,
                    'password' => $password,
                ]);

            return $this->handleResponse($response, 'login_password');

        } catch (Exception $e) {
            Log::error('Old SSO password login failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Password login failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Login user with OTP
     */
    public function loginWithOtp(string $username, string $otp): array
    {
        try {
            $url = $this->config['domain_1'] . $this->config['endpoints']['user_login'];
            
            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($this->defaultHeaders)
                ->asForm()
                ->post($url, [
                    'source' => $this->config['source'],
                    'grant_type' => 'user_login',
                    'client_id' => $this->config['client_id'],
                    'client_secret' => $this->config['client_secret'],
                    'api_key' => $this->config['api_key'],
                    'username' => $username,
                    'login_otp' => $otp,
                    'platform' => 'web',
                    'auto_login' => 0
                ]);

            return $this->handleResponse($response, 'login_otp');

        } catch (Exception $e) {
            Log::error('Old SSO OTP login failed', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'OTP login failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Register a new user
     */
    public function registerUser(array $userData): array
    {
        try {
            $url = $this->config['domain_2'] . $this->config['endpoints']['user_register'];
            
            $headers = array_merge($this->defaultHeaders, [
                'Origin' => $this->config['ref_url_domain'],
                'Referer' => $this->config['ref_url_domain'] . '/',
            ]);

            $formData = [
                'api_key' => $this->config['api_key'],
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
                'mobile' => $userData['mobile'],
                'password' => $userData['password'],
                'password_confirmation' => $userData['password_confirmation'],
            ];

            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($headers)
                ->asForm()
                ->post($url, $formData);

            return $this->handleResponse($response, 'register_user');

        } catch (Exception $e) {
            Log::error('Old SSO user registration failed', [
                'email' => $userData['email'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'User registration failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Handle HTTP response and standardize the format
     */
    private function handleResponse(Response $response, string $operation): array
    {
        $statusCode = $response->status();
        $responseData = $response->json();

        Log::info("Old SSO API response for {$operation}", [
            'status_code' => $statusCode,
            'response_data' => $responseData,
        ]);

        if ($response->successful()) {
            return [
                'success' => true,
                'message' => "Operation {$operation} completed successfully",
                'data' => $responseData,
                'status_code' => $statusCode,
            ];
        }

        return [
            'success' => false,
            'message' => $responseData['message'] ?? "Operation {$operation} failed",
            'data' => $responseData,
            'status_code' => $statusCode,
        ];
    }

    /**
     * Verify mobile OTP
     */
    public function verifyMobileOtp(string $userId, string $otp, $type): array
    {
        try {

            $curl = curl_init();

            curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://ssoapi.cubeonebiz.com/api/v2/users/verifyotp',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('api_key' => '5b2f4b31f524b6603d81dbcaabd8121078d00f1614a294c3207e39951f7054c8','user_id' => '92928','otp' => '5130','otp_type' => 'mobile','type' => 'forget_password','source' => 'VEZA'),
            CURLOPT_HTTPHEADER => array(
                'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:140.0) Gecko/******** Firefox/140.0',
                'Accept: application/json, text/plain, */*',
                'Accept-Language: en-US,en;q=0.5',
                'Accept-Encoding: gzip, deflate, br, zstd',
                'Origin: https://account.cubeonebiz.com',
                'Sec-GPC: 1',
                'Connection: keep-alive',
                'Referer: https://account.cubeonebiz.com/',
                'Sec-Fetch-Dest: empty',
                'Sec-Fetch-Mode: cors',
                'Sec-Fetch-Site: same-site',
                'Priority: u=0'
            ),
            ));

            $response = curl_exec($curl);

            curl_close($curl);
            dd($response);

            $url = $this->config['domain_2'] . '/api/v2/users/verifyotp';

            $headers = array_merge($this->config['headers'], [
                'Origin' => $this->config['ref_url_domain'],
                'Referer' => $this->config['ref_url_domain'] . '/',
            ]);

            Log::info('Old SSO mobile OTP verification request', [
                'user_id' => $userId,
                'url' => $url,
            ]);

            $formData = [
                'api_key' => $this->config['api_key'],
                'user_id' => $userId,
                'otp' => $otp,
                'otp_type' => 'mobile',
                'source' => $this->config['source'],
                "type" => 'forgot_password'
            ];

            $response = Http::timeout($this->config['http']['timeout'])
                ->withHeaders($headers)
                ->asForm()
                ->post($url, $formData);
            dd($response->json());

            return $this->handleResponse($response, 'verify_mobile_otp');

        } catch (Exception $e) {
            Log::error('Old SSO mobile OTP verification failed', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Mobile OTP verification failed: ' . $e->getMessage(),
                'data' => null,
            ];
        }
    }
}
