<?php

namespace App\Services\Auth;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class KeycloakUserRegistrationService
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Register a new user in Keycloak
     */
    public function registerUser(array $userData): array
    {
        try {
            Log::info('Starting Keycloak user registration', [
                'username' => $userData['username'] ?? 'unknown',
                'email' => $userData['email'] ?? 'unknown',
            ]);

            // Get admin access token
            $adminToken = $this->getAdminToken();

            if (!$adminToken) {
                Log::error('Failed to get Keycloak admin token for registration');
                return [
                    'success' => false,
                    'message' => 'Failed to get Keycloak admin token'
                ];
            }

            Log::info('Successfully obtained admin token for registration');

            // Prepare user data for Keycloak
            $keycloakUser = [
                'username' => $userData['username'],
                'email' => $userData['email'],
                'firstName' => $userData['first_name'],
                'lastName' => $userData['last_name'],
                'enabled' => true,
                'emailVerified' => false,
                'credentials' => [
                    [
                        'type' => 'password',
                        'value' => $userData['password'],
                        'temporary' => false
                    ]
                ]
            ];

            // Add attributes if provided
            if (isset($userData['attributes']) && is_array($userData['attributes'])) {
                $keycloakUser['attributes'] = $userData['attributes'];
            }

            // Create user in Keycloak
            $response = Http::withToken($adminToken)
                ->post($this->getUsersUrl(), $keycloakUser);

            if ($response->successful() || $response->status() === 201) {
                Log::info('User successfully created in Keycloak', [
                    'username' => $userData['username'],
                    'email' => $userData['email']
                ]);

                // Get the user ID from the Location header or by searching for the user
                $userId = null;
                $locationHeader = $response->header('Location');

                if ($locationHeader) {
                    // Extract user ID from Location header (e.g., .../users/12345-67890-abcdef)
                    $userId = basename($locationHeader);
                } else {
                    // Fallback: search for the user by username
                    $userId = $this->getUserIdByUsername($userData['username'], $adminToken);
                }

                return [
                    'success' => true,
                    'message' => 'User created in Keycloak successfully',
                    'user_id' => $userId
                ];
            } else {
                Log::error('Failed to create user in Keycloak', [
                    'username' => $userData['username'],
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to create user in Keycloak: ' . ($response->json()['errorMessage'] ?? 'Unknown error')
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak user registration', [
                'username' => $userData['username'],
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak registration failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get admin access token for Keycloak admin operations using service account
     */
    protected function getAdminToken(): ?string
    {
        try {
            // Use the startwell client with service account enabled
            $response = Http::asForm()->post($this->getAdminTokenUrl(), [
                'grant_type' => 'client_credentials',
                'client_id' => $this->config['client_id'], // Use the startwell client
                'client_secret' => $this->config['client_secret'],
            ]);

            if ($response->successful()) {
                $tokenData = $response->json();
                Log::info('Successfully obtained Keycloak admin token via service account', [
                    'client_id' => $this->config['client_id']
                ]);
                return $tokenData['access_token'];
            } else {
                Log::error('Failed to get Keycloak admin token via service account', [
                    'client_id' => $this->config['client_id'],
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                return null;
            }
        } catch (\Exception $e) {
            Log::error('Exception getting Keycloak admin token via service account', [
                'client_id' => $this->config['client_id'],
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get admin token URL
     */
    protected function getAdminTokenUrl(): string
    {
        return $this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/token';
    }

    /**
     * Get users management URL
     */
    protected function getUsersUrl(): string
    {
        return $this->config['auth_server_url'] . '/admin/realms/' . $this->config['realm'] . '/users';
    }

    /**
     * Delete user from Keycloak (for rollback purposes)
     */
    public function deleteUser(string $username): bool
    {
        try {
            $adminToken = $this->getAdminToken();
            
            if (!$adminToken) {
                return false;
            }

            // First, find the user by username
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl(), ['username' => $username]);

            if (!$response->successful() || empty($response->json())) {
                return false;
            }

            $users = $response->json();
            if (empty($users)) {
                return false;
            }

            $userId = $users[0]['id'];

            // Delete the user
            $deleteResponse = Http::withToken($adminToken)
                ->delete($this->getUsersUrl() . '/' . $userId);

            return $deleteResponse->successful();

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak user deletion', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Update an existing user in Keycloak
     */
    public function updateUser(string $username, array $userData): array
    {
        try {
            // Get admin access token
            $adminToken = $this->getAdminToken();
            
            if (!$adminToken) {
                return [
                    'success' => false,
                    'message' => 'Failed to get Keycloak admin token'
                ];
            }

            // First, find the user by username
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl(), ['username' => $username]);

            if (!$response->successful() || empty($response->json())) {
                Log::error('Failed to find user in Keycloak', [
                    'username' => $username,
                    'status' => $response->status(),
                    'response' => $response->json() ?? 'Empty response'
                ]);
                
                return [
                    'success' => false,
                    'message' => 'User not found in Keycloak'
                ];
            }

            $users = $response->json();
            if (empty($users)) {
                return [
                    'success' => false,
                    'message' => 'User not found in Keycloak'
                ];
            }

            $userId = $users[0]['id'];

            // Prepare user data for update
            $keycloakUserUpdate = [
                'firstName' => $userData['first_name'] ?? null,
                'lastName' => $userData['last_name'] ?? null,
                'email' => $userData['email'] ?? null,
            ];

            // Remove null values
            $keycloakUserUpdate = array_filter($keycloakUserUpdate, function ($value) {
                return $value !== null;
            });

            // Update user in Keycloak
            $updateResponse = Http::withToken($adminToken)
                ->put($this->getUsersUrl() . '/' . $userId, $keycloakUserUpdate);

            if ($updateResponse->successful()) {
                Log::info('User successfully updated in Keycloak', [
                    'username' => $username,
                    'userId' => $userId,
                    'email' => $userData['email'] ?? 'not provided'
                ]);

                return [
                    'success' => true,
                    'message' => 'User updated in Keycloak successfully'
                ];
            } else {
                Log::error('Failed to update user in Keycloak', [
                    'username' => $username,
                    'userId' => $userId,
                    'status' => $updateResponse->status(),
                    'response' => $updateResponse->json() ?? 'Empty response'
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to update user in Keycloak: ' . ($updateResponse->json()['errorMessage'] ?? 'Unknown error')
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak user update', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak update failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if user exists in Keycloak
     */
    public function userExists(string $username): bool
    {
        try {
            Log::info('Checking if user exists in Keycloak', [
                'username' => $username,
                'config_client_id' => $this->config['client_id'] ?? 'not_set',
                'config_realm' => $this->config['realm'] ?? 'not_set',
                'config_auth_server_url' => $this->config['auth_server_url'] ?? 'not_set',
            ]);

            $adminToken = $this->getAdminToken();

            if (!$adminToken) {
                Log::warning('Failed to get Keycloak admin token for user search', [
                    'username' => $username,
                    'config_client_id' => $this->config['client_id'] ?? 'not_set',
                ]);
                return false;
            }

            // Try searching by username first
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl(), ['username' => $username]);

            if ($response->successful()) {
                $users = $response->json();
                if (!empty($users)) {
                    Log::info('User found in Keycloak by username', [
                        'username' => $username,
                        'keycloak_user_id' => $users[0]['id'] ?? 'unknown',
                    ]);
                    return true;
                }
            }

            // If not found by username, try searching by email (if username is email format)
            if (filter_var($username, FILTER_VALIDATE_EMAIL)) {
                $response = Http::withToken($adminToken)
                    ->get($this->getUsersUrl(), ['email' => $username]);

                if ($response->successful()) {
                    $users = $response->json();
                    if (!empty($users)) {
                        Log::info('User found in Keycloak by email', [
                            'username' => $username,
                            'keycloak_user_id' => $users[0]['id'] ?? 'unknown',
                        ]);
                        return true;
                    }
                }
            }

            Log::info('User not found in Keycloak', [
                'username' => $username,
            ]);
            return false;

        } catch (\Exception $e) {
            Log::error('Error checking user existence in Keycloak', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get user ID by username
     */
    protected function getUserIdByUsername(string $username, string $adminToken): ?string
    {
        try {
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl(), ['username' => $username]);

            if ($response->successful()) {
                $users = $response->json();
                if (!empty($users) && isset($users[0]['id'])) {
                    return $users[0]['id'];
                }
            }

            Log::warning('Could not find user ID for username', [
                'username' => $username,
                'status' => $response->status()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception getting user ID by username', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
}