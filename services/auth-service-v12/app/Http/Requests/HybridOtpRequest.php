<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class HybridOtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // Validate email or mobile number format
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL) && !preg_match('/^91\d{10}$/', $value)) {
                        $fail('The username must be a valid email address or mobile number with country code (91xxxxxxxxxx).');
                    }
                },
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'username.required' => 'Username is required.',
            'username.string' => 'Username must be a string.',
            'username.max' => 'Username cannot exceed 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'username' => 'username (email or mobile)',
        ];
    }
}
