<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class HybridLoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // Validate email or mobile number format
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL) && !preg_match('/^91\d{10}$/', $value)) {
                        $fail('The username must be a valid email address or mobile number with country code (91xxxxxxxxxx).');
                    }
                },
            ],
            'password' => [
                'required_without:login_otp',
                'string',
                'min:6',
                'max:255',
            ],
            'login_otp' => [
                'required_without:password',
                'string',
                'digits:4',
            ],
            'remember_me' => [
                'sometimes',
                'boolean',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'username.required' => 'Username is required.',
            'username.string' => 'Username must be a string.',
            'username.max' => 'Username cannot exceed 255 characters.',
            'password.required_without' => 'Password is required when OTP is not provided.',
            'password.string' => 'Password must be a string.',
            'password.min' => 'Password must be at least 6 characters.',
            'password.max' => 'Password cannot exceed 255 characters.',
            'login_otp.required_without' => 'OTP is required when password is not provided.',
            'login_otp.string' => 'OTP must be a string.',
            'login_otp.digits' => 'OTP must be exactly 4 digits.',
            'remember_me.boolean' => 'Remember me must be true or false.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'username' => 'username (email or mobile)',
            'password' => 'password',
            'login_otp' => 'OTP',
            'remember_me' => 'remember me',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Ensure either password or OTP is provided, but not both
            if ($this->filled('password') && $this->filled('login_otp')) {
                $validator->errors()->add('login_method', 'Please provide either password or OTP, not both.');
            }

            if (!$this->filled('password') && !$this->filled('login_otp')) {
                $validator->errors()->add('login_method', 'Please provide either password or OTP.');
            }
        });
    }
}
