<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Requests\HybridUserSearchRequest;
use App\Http\Requests\HybridLoginRequest;
use App\Http\Requests\HybridOtpRequest;
use App\Http\Requests\HybridRegisterRequest;
use App\Http\Resources\UserResource;
use App\Services\Auth\OldSsoAuthenticationService;
use App\Services\Logging\StructuredLogger;
use App\Traits\ResponseHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Exception;
use Illuminate\Http\Request;

/**
 * Hybrid Authentication Controller
 * 
 * Handles authentication that integrates old SSO with Keycloak
 * Keeps the logic isolated from the existing AuthController
 */
class HybridAuthController extends Controller
{
    use ResponseHelper;

    // Constants for commonly used messages
    private const MSG_USER_FOUND = 'User found in old SSO system';
    private const MSG_USER_NOT_FOUND = 'User not found in old SSO system';
    private const MSG_AUTH_SUCCESS = 'Authentication successful';
    private const MSG_OTP_SENT = 'OTP sent successfully';
    private const MSG_REGISTRATION_SUCCESS = 'Registration successful';

    public function __construct(
        private OldSsoAuthenticationService $hybridAuthService,
        private StructuredLogger $logger
    ) {}

    /**
     * Search for user in old SSO system
     * 
     * @group Hybrid Authentication
     * @bodyParam username string required The username (email or mobile with country code). Example: <EMAIL> or 919988877766
     */
    public function searchUser(HybridUserSearchRequest $request): JsonResponse
    {
        try {
            $username = $request->input('username');

            $result = $this->hybridAuthService->userExistsBase($username);
            return response()->json($result);

            // Log the search result with detailed information

            // Determine response message based on sync status
            $message = $result['message'];
            if ($result['exists']) {
                $message = match($result['sync_status']) {
                    'synced' => 'User found in both Old SSO and Keycloak',
                    'will_sync_on_login' => 'User found and ready for login',
                    'keycloak_only' => 'User found in Keycloak only',
                    'requires_mobile_verification' => 'User found but requires mobile verification',
                    default => 'User found'
                };
            }

            return $this->successResponse($message, [
                'user_exists' => $result['exists'],
                'user_data' => $result['user_data'],
                'old_sso_exists' => $result['old_sso_exists'] ?? false,
                'keycloak_exists' => $result['keycloak_exists'] ?? false,
                'sync_status' => $result['sync_status'] ?? 'unknown',
                'recommendations' => $result['recommendations'] ?? [],
                // Legacy fields for backward compatibility
                'can_login' => $result['recommendations']['can_login_password'] ?? false,
                'can_register' => $result['recommendations']['can_register'] ?? !$result['exists'],
                'login_methods' => $this->getAvailableLoginMethods($result),
            ]);

        } catch (Exception $e) {
            $this->logger->log('error', 'Hybrid user search failed', [
                'event_type' => 'hybrid_user_search_error',
                'username' => $request->input('username'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'User search failed: ' . $e->getMessage(),
                null,
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Login user using password or OTP
     * 
     * @group Hybrid Authentication
     * @bodyParam username string required The username (email or mobile with country code). Example: <EMAIL> or 919988877766
     * @bodyParam password string The user's password (required if login_otp not provided). Example: MySecurePassword123!
     * @bodyParam login_otp string The 6-digit OTP (required if password not provided). Example: 123456
     * @bodyParam remember_me boolean Whether to remember the user. Example: true
     */
    public function login(HybridLoginRequest $request): JsonResponse
    {
        try {
            $username = $request->input('username');
            $password = $request->input('password');
            $loginOtp = $request->input('login_otp');
            $rememberMe = $request->boolean('remember_me', false);

            $this->logger->log('info', 'Hybrid login attempt', [
                'event_type' => 'hybrid_login_attempt',
                'username' => $username,
                'login_method' => $password ? 'password' : 'otp',
                'remember_me' => $rememberMe,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Determine login method and authenticate
            if ($password) {
                $result = $this->hybridAuthService->authenticateWithPassword($username, $password);
            } else {
                $result = $this->hybridAuthService->authenticateWithOtp($username, $loginOtp);
            }

            if (!$result['success']) {
                $this->logger->log('warning', 'Hybrid login failed', [
                    'event_type' => 'hybrid_login_failed',
                    'username' => $username,
                    'login_method' => $password ? 'password' : 'otp',
                    'message' => $result['message'],
                ]);

                return $this->unauthorizedResponse($result['message']);
            }

            $this->logger->log('info', 'Hybrid login successful', [
                'event_type' => 'hybrid_login_success',
                'username' => $username,
                'user_id' => $result['old_sso_data']['data']['user_id'],
                'login_method' => $password ? 'password' : 'otp',
                'auth_type' => $result['auth_type'],
                'keycloak_synced' => $result['keycloak_synced'] ?? false,
            ]);

            return $this->successResponse(self::MSG_AUTH_SUCCESS, [
                'user' => $result['old_sso_data']['data'],
                'tokens' => $result['tokens'],
                'token_type' => 'Bearer',
                'auth_type' => $result['auth_type'],
                'keycloak_synced' => $result['keycloak_synced'] ?? false,
                'expires_in' => $result['tokens']['expires_in'], // Convert to seconds
                'user_details_sync' => $result['user_details_sync'] ?? [
                    'details_verified' => false,
                    'details_synced' => false,
                    'sync_message' => 'User details sync information not available',
                ],
            ]);

        } catch (Exception $e) {
            $this->logger->log('error', 'Hybrid login error', [
                'event_type' => 'hybrid_login_error',
                'username' => $request->input('username'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Login failed: ' . $e->getMessage(),
                null,
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Request OTP for user login
     * 
     * @group Hybrid Authentication
     * @bodyParam username string required The username (email or mobile with country code). Example: <EMAIL> or 919988877766
     */
    public function requestOtp(HybridOtpRequest $request): JsonResponse
    {
        try {
            $username = $request->input('username');

            $this->logger->log('info', 'Hybrid OTP request initiated', [
                'event_type' => 'hybrid_otp_request',
                'username' => $username,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            $result = $this->hybridAuthService->requestOtp($username);

            if (!$result['success']) {
                $this->logger->log('warning', 'Hybrid OTP request failed', [
                    'event_type' => 'hybrid_otp_request_failed',
                    'username' => $username,
                    'message' => $result['message'],
                ]);

                return $this->errorResponse($result['message'], null, Response::HTTP_BAD_REQUEST);
            }

            $this->logger->log('info', 'Hybrid OTP request successful', [
                'event_type' => 'hybrid_otp_request_success',
                'username' => $username,
            ]);

            return $this->successResponse(self::MSG_OTP_SENT, [
                'otp_sent' => true,
                'message' => 'OTP has been sent to your registered mobile number',
                'expires_in' => 300, // 5 minutes
            ]);

        } catch (Exception $e) {
            $this->logger->log('error', 'Hybrid OTP request error', [
                'event_type' => 'hybrid_otp_request_error',
                'username' => $request->input('username'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'OTP request failed: ' . $e->getMessage(),
                null,
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Register a new user in both old SSO and Keycloak
     * 
     * @group Hybrid Authentication
     * @bodyParam first_name string required The user's first name. Example: John
     * @bodyParam last_name string required The user's last name. Example: Doe
     * @bodyParam email string required The user's email address. Example: <EMAIL>
     * @bodyParam mobile string required The user's mobile number with country code. Example: 919988877766
     * @bodyParam password string required The user's password (min 8 chars, must contain uppercase, lowercase, number, special char). Example: MySecurePassword123!
     * @bodyParam password_confirmation string required Password confirmation. Example: MySecurePassword123!
     */
    public function register(HybridRegisterRequest $request): JsonResponse
    {
        try {
            $userData = $request->validated();

            $this->logger->log('info', 'Hybrid registration initiated', [
                'event_type' => 'hybrid_registration_attempt',
                'email' => $userData['email'],
                'mobile' => $userData['mobile'],
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            $result = $this->hybridAuthService->registerUser($userData);

            if (!$result['success']) {
                $this->logger->log('warning', 'Hybrid registration failed', [
                    'event_type' => 'hybrid_registration_failed',
                    'email' => $userData['email'],
                    'mobile' => $userData['mobile'],
                    'message' => $result['message'],
                ]);

                return $this->errorResponse($result['message'], null, Response::HTTP_BAD_REQUEST);
            }

            return $this->successResponse(self::MSG_REGISTRATION_SUCCESS, [
                'user' => $result['user'],
                'auth_type' => $result['auth_type'],
                'keycloak_registered' => $result['keycloak_registered'] ?? false,
                'message' => 'Registration completed successfully. You can now login with your credentials.',
            ], Response::HTTP_CREATED);

        } catch (Exception $e) {
            $this->logger->log('error', 'Hybrid registration error', [
                'event_type' => 'hybrid_registration_error',
                'email' => $request->input('email'),
                'mobile' => $request->input('mobile'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Registration failed: ' . $e->getMessage(),
                null,
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get available login methods based on user existence status
     */
    private function getAvailableLoginMethods(array $result): array
    {
        $methods = [];

        if ($result['recommendations']['can_login_password'] ?? false) {
            $methods[] = 'password';
        }

        if ($result['recommendations']['can_login_otp'] ?? false) {
            $methods[] = 'otp';
        }

        return $methods;
    }

    /**
     * Update Keycloak user credentials when Old SSO auth succeeds but Keycloak fails
     *
     * @group Hybrid Authentication
     * @bodyParam username string required The username (email or mobile with country code). Example: <EMAIL> or 919988877766
     * @bodyParam password string required The user's password. Example: MySecurePassword123!
     */
    public function updateKeycloakCredentials(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'username' => 'required|string|max:255',
                'password' => 'required|string|min:6|max:255',
            ]);

            $username = $request->input('username');
            $password = $request->input('password');

            $this->logger->log('info', 'Keycloak credentials update initiated', [
                'event_type' => 'keycloak_credentials_update',
                'username' => $username,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            $result = $this->hybridAuthService->updateKeycloakUserCredentials($username, $password);

            if ($result['success']) {
                $this->logger->log('info', 'Keycloak credentials update successful', [
                    'event_type' => 'keycloak_credentials_update_success',
                    'username' => $username,
                    'keycloak_user_id' => $result['keycloak_user_id'] ?? 'unknown',
                ]);

                return $this->successResponse('Keycloak credentials updated successfully', [
                    'updated' => true,
                    'keycloak_user_id' => $result['keycloak_user_id'] ?? null,
                    'message' => $result['message'],
                ]);
            } else {
                $this->logger->log('warning', 'Keycloak credentials update failed', [
                    'event_type' => 'keycloak_credentials_update_failed',
                    'username' => $username,
                    'message' => $result['message'],
                ]);

                return $this->errorResponse($result['message'], null, Response::HTTP_BAD_REQUEST);
            }

        } catch (Exception $e) {
            $this->logger->log('error', 'Keycloak credentials update error', [
                'event_type' => 'keycloak_credentials_update_error',
                'username' => $request->input('username'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Keycloak credentials update failed: ' . $e->getMessage(),
                null,
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Verify mobile OTP and activate user
     *
     * @group Hybrid Authentication
     * @bodyParam username string required The username (email or mobile with country code). Example: <EMAIL> or 919988877766
     * @bodyParam otp string required The 6-digit OTP. Example: 123456
     */
    public function verifyMobileOtp(Request $request)
    {
        try {
            $request->validate([
                'username' => 'required|string|max:255',
                'otp' => 'required|string|size:4', // Assuming 4-digit OTP, adjust if needed
            ]);

            $username = $request->input('username');
            $otp = $request->input('otp');

            $result = $this->hybridAuthService->verifyMobileOtp($username, $otp);

            if ($result['success']) {
                $this->logger->log('info', 'Mobile OTP verification successful', [
                    'event_type' => 'mobile_otp_verification_success',
                    'username' => $username,
                ]);

                return $this->successResponse('Mobile OTP verified successfully', [
                    'verified' => true,
                    'user_activated' => true,
                    'mobile_verified' => true,
                    'message' => $result['message'],
                    'user_data' => $result['user_data'] ?? null,
                ]);
            } else {
                $this->logger->log('warning', 'Mobile OTP verification failed', [
                    'event_type' => 'mobile_otp_verification_failed',
                    'username' => $username,
                    'message' => $result['message'],
                ]);

                return $this->errorResponse($result['message'], null, Response::HTTP_BAD_REQUEST);
            }

        } catch (Exception $e) {
            $this->logger->log('error', 'Mobile OTP verification error', [
                'event_type' => 'mobile_otp_verification_error',
                'username' => $request->input('username'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(
                'Mobile OTP verification failed: ' . $e->getMessage(),
                null,
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
