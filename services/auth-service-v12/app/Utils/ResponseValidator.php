<?php

namespace App\Utils;

/**
 * Response Validator Utility
 * 
 * Validates that API responses follow the standardized format:
 * {
 *   "success": boolean,
 *   "data": object|array,
 *   "message": string
 * }
 */
class ResponseValidator
{
    /**
     * Validate that a response follows the standardized format
     */
    public static function validateFormat(array $response): array
    {
        $validation = [
            'is_valid' => true,
            'errors' => [],
            'warnings' => [],
        ];

        // Check required fields
        if (!array_key_exists('success', $response)) {
            $validation['is_valid'] = false;
            $validation['errors'][] = 'Missing required field: success';
        } elseif (!is_bool($response['success'])) {
            $validation['is_valid'] = false;
            $validation['errors'][] = 'Field "success" must be boolean';
        }

        if (!array_key_exists('data', $response)) {
            $validation['is_valid'] = false;
            $validation['errors'][] = 'Missing required field: data';
        } elseif (!is_array($response['data']) && !is_object($response['data'])) {
            $validation['is_valid'] = false;
            $validation['errors'][] = 'Field "data" must be array or object';
        }

        if (!array_key_exists('message', $response)) {
            $validation['is_valid'] = false;
            $validation['errors'][] = 'Missing required field: message';
        } elseif (!is_string($response['message'])) {
            $validation['is_valid'] = false;
            $validation['errors'][] = 'Field "message" must be string';
        } elseif (empty(trim($response['message']))) {
            $validation['warnings'][] = 'Field "message" is empty';
        }

        // Check for unexpected fields at root level
        $allowedFields = ['success', 'data', 'message'];
        $extraFields = array_diff(array_keys($response), $allowedFields);
        if (!empty($extraFields)) {
            $validation['warnings'][] = 'Unexpected fields at root level: ' . implode(', ', $extraFields);
        }

        return $validation;
    }

    /**
     * Validate response format and throw exception if invalid
     */
    public static function assertValidFormat(array $response): void
    {
        $validation = self::validateFormat($response);
        
        if (!$validation['is_valid']) {
            throw new \InvalidArgumentException(
                'Response format validation failed: ' . implode(', ', $validation['errors'])
            );
        }
    }

    /**
     * Get a human-readable validation report
     */
    public static function getValidationReport(array $response): string
    {
        $validation = self::validateFormat($response);
        
        $report = "Response Format Validation Report:\n";
        $report .= "================================\n";
        
        if ($validation['is_valid']) {
            $report .= "✅ Status: VALID\n";
        } else {
            $report .= "❌ Status: INVALID\n";
        }
        
        if (!empty($validation['errors'])) {
            $report .= "\n🚫 Errors:\n";
            foreach ($validation['errors'] as $error) {
                $report .= "  - $error\n";
            }
        }
        
        if (!empty($validation['warnings'])) {
            $report .= "\n⚠️  Warnings:\n";
            foreach ($validation['warnings'] as $warning) {
                $report .= "  - $warning\n";
            }
        }
        
        $report .= "\n📊 Response Structure:\n";
        $report .= "  - success: " . (array_key_exists('success', $response) ? 
            (is_bool($response['success']) ? ($response['success'] ? 'true' : 'false') : gettype($response['success'])) : 
            'missing') . "\n";
        $report .= "  - data: " . (array_key_exists('data', $response) ? 
            gettype($response['data']) : 'missing') . "\n";
        $report .= "  - message: " . (array_key_exists('message', $response) ? 
            '"' . (is_string($response['message']) ? substr($response['message'], 0, 50) . '...' : gettype($response['message'])) . '"' : 
            'missing') . "\n";
        
        return $report;
    }

    /**
     * Check if response indicates success
     */
    public static function isSuccessResponse(array $response): bool
    {
        return isset($response['success']) && $response['success'] === true;
    }

    /**
     * Check if response indicates error
     */
    public static function isErrorResponse(array $response): bool
    {
        return isset($response['success']) && $response['success'] === false;
    }

    /**
     * Extract data from standardized response
     */
    public static function extractData(array $response): array
    {
        return $response['data'] ?? [];
    }

    /**
     * Extract message from standardized response
     */
    public static function extractMessage(array $response): string
    {
        return $response['message'] ?? '';
    }

    /**
     * Create a standardized response array (for testing purposes)
     */
    public static function createResponse(bool $success, string $message, array $data = []): array
    {
        return [
            'success' => $success,
            'data' => $data,
            'message' => $message,
        ];
    }

    /**
     * Validate multiple responses at once
     */
    public static function validateMultiple(array $responses): array
    {
        $results = [];
        
        foreach ($responses as $name => $response) {
            $results[$name] = self::validateFormat($response);
        }
        
        return $results;
    }

    /**
     * Get summary of multiple validation results
     */
    public static function getMultipleValidationSummary(array $validationResults): array
    {
        $summary = [
            'total' => count($validationResults),
            'valid' => 0,
            'invalid' => 0,
            'total_errors' => 0,
            'total_warnings' => 0,
        ];
        
        foreach ($validationResults as $result) {
            if ($result['is_valid']) {
                $summary['valid']++;
            } else {
                $summary['invalid']++;
            }
            
            $summary['total_errors'] += count($result['errors']);
            $summary['total_warnings'] += count($result['warnings']);
        }
        
        return $summary;
    }
}
