<?php

namespace App\Providers;

use App\Services\Auth\OldSsoApiClient;
use App\Services\Auth\OldSsoAuthenticationService;
use App\Services\Auth\KeycloakAuthenticationService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register Old SSO API Client as singleton
        $this->app->singleton(OldSsoApiClient::class, function () {
            return new OldSsoApiClient();
        });

        // Register Old SSO Authentication Service
        $this->app->singleton(OldSsoAuthenticationService::class, function ($app) {
            return new OldSsoAuthenticationService(
                $app->make(OldSsoApiClient::class),
                $app->make(KeycloakAuthenticationService::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
