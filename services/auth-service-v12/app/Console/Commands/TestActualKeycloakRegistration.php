<?php

namespace App\Console\Commands;

use App\Services\Auth\KeycloakUserRegistrationService;
use Illuminate\Console\Command;

class TestActualKeycloakRegistration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keycloak:test-actual-registration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test actual Keycloak user registration and verify user ID';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🧪 Testing ACTUAL Keycloak User Registration');
        $this->line('==========================================');

        // Test user data
        $testUsername = 'test-user-' . time();
        $testEmail = $testUsername . '@test.com';
        
        $userData = [
            'username' => $testUsername,
            'email' => $testEmail,
            'first_name' => 'Test',
            'last_name' => 'User',
            'password' => 'TestPassword123!',
        ];

        $this->line("Test Username: {$testUsername}");
        $this->line("Test Email: {$testEmail}");
        $this->line('');

        try {
            $service = app(KeycloakUserRegistrationService::class);

            // Step 1: Verify user doesn't exist
            $this->info('1️⃣ Verifying user does not exist...');
            $exists = $service->userExists($testUsername);
            
            if ($exists) {
                $this->error("❌ User '{$testUsername}' already exists in Keycloak");
                return 1;
            }
            
            $this->info("✅ Confirmed user '{$testUsername}' does not exist");

            // Step 2: Register the user
            $this->info('2️⃣ Registering user in Keycloak...');
            
            $result = $service->registerUser($userData);

            if (!$result['success']) {
                $this->error('❌ User registration failed');
                $this->line("Error: " . $result['message']);
                return 1;
            }

            $this->info('✅ User registration reported as successful!');
            $keycloakUserId = $result['user_id'] ?? null;
            
            if ($keycloakUserId) {
                $this->line("🆔 Keycloak User ID: {$keycloakUserId}");
            } else {
                $this->warn("⚠️  No Keycloak User ID returned in response");
            }

            // Step 3: Verify user now exists
            $this->info('3️⃣ Verifying user was actually created...');
            $exists = $service->userExists($testUsername);
            
            if ($exists) {
                $this->info('✅ User verification successful - user exists in Keycloak');
                
                // Step 4: Get user details to confirm Keycloak ID
                $this->info('4️⃣ Fetching user details from Keycloak...');
                
                // Get admin token and fetch user details
                $adminToken = $this->getAdminToken();
                if ($adminToken) {
                    $userDetails = $this->getUserDetails($adminToken, $testUsername);
                    if ($userDetails) {
                        $actualKeycloakId = $userDetails['id'] ?? 'unknown';
                        $this->info("🎉 SUCCESS! Keycloak User ID confirmed: {$actualKeycloakId}");
                        
                        if ($keycloakUserId && $keycloakUserId === $actualKeycloakId) {
                            $this->info("✅ Returned User ID matches actual Keycloak ID");
                        } elseif ($keycloakUserId) {
                            $this->warn("⚠️  Returned User ID ({$keycloakUserId}) differs from actual ({$actualKeycloakId})");
                        }
                        
                        $this->line('');
                        $this->info('📊 User Details:');
                        $this->line("  - ID: {$userDetails['id']}");
                        $this->line("  - Username: {$userDetails['username']}");
                        $this->line("  - Email: {$userDetails['email']}");
                        $this->line("  - First Name: {$userDetails['firstName']}");
                        $this->line("  - Last Name: {$userDetails['lastName']}");
                        $this->line("  - Enabled: " . ($userDetails['enabled'] ? 'Yes' : 'No'));
                        
                        return 0;
                    }
                }
                
                $this->warn("⚠️  Could not fetch detailed user information");
                return 0;
                
            } else {
                $this->error('❌ User verification failed - user not found after registration');
                $this->error('🚨 REGISTRATION DID NOT ACTUALLY WORK!');
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("❌ Exception during registration test: " . $e->getMessage());
            $this->line("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }

    private function getAdminToken(): ?string
    {
        try {
            $config = config('keycloak');
            $tokenUrl = $config['auth_server_url'] . '/realms/' . $config['realm'] . '/protocol/openid-connect/token';
            
            $response = \Illuminate\Support\Facades\Http::asForm()->post($tokenUrl, [
                'grant_type' => 'client_credentials',
                'client_id' => $config['client_id'],
                'client_secret' => $config['client_secret'],
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['access_token'] ?? null;
            }
            
            return null;
        } catch (\Exception $e) {
            $this->error("Failed to get admin token: " . $e->getMessage());
            return null;
        }
    }

    private function getUserDetails(string $adminToken, string $username): ?array
    {
        try {
            $config = config('keycloak');
            $usersUrl = $config['auth_server_url'] . '/admin/realms/' . $config['realm'] . '/users';
            
            $response = \Illuminate\Support\Facades\Http::withToken($adminToken)
                ->get($usersUrl, ['username' => $username]);

            if ($response->successful()) {
                $users = $response->json();
                return $users[0] ?? null;
            }
            
            return null;
        } catch (\Exception $e) {
            $this->error("Failed to get user details: " . $e->getMessage());
            return null;
        }
    }
}
