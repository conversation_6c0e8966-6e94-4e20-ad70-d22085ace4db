<?php

namespace App\Console\Commands;

use App\Services\Auth\KeycloakUserRegistrationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class TestKeycloakConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keycloak:test-connection';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Keycloak connection and configuration';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Testing Keycloak Connection and Configuration');
        $this->line('================================================');

        // Get configuration
        $config = config('keycloak');
        
        $this->info('📋 Current Configuration:');
        $this->line("Auth Server URL: {$config['auth_server_url']}");
        $this->line("Realm: {$config['realm']}");
        $this->line("Client ID: {$config['client_id']}");
        $this->line("Client Secret: " . substr($config['client_secret'], 0, 10) . '...');
        $this->line('');

        // Test 1: Server connectivity
        $this->info('1️⃣ Testing server connectivity...');
        try {
            $response = Http::timeout(10)->get($config['auth_server_url']);
            if ($response->successful() || $response->status() === 404) {
                $this->info("✅ Keycloak server is reachable (HTTP {$response->status()})");
            } else {
                $this->error("❌ Keycloak server returned HTTP {$response->status()}");
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ Cannot reach Keycloak server: {$e->getMessage()}");
            return 1;
        }

        // Test 2: Realm accessibility
        $this->info('2️⃣ Testing realm accessibility...');
        $realmUrl = $config['auth_server_url'] . '/realms/' . $config['realm'];
        try {
            $response = Http::timeout(10)->get($realmUrl);
            if ($response->successful()) {
                $this->info("✅ Realm '{$config['realm']}' is accessible");
            } else {
                $this->error("❌ Realm '{$config['realm']}' is not accessible (HTTP {$response->status()})");
                $this->warn("💡 Please check if the realm exists in Keycloak");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error accessing realm: {$e->getMessage()}");
        }

        // Test 3: Token endpoint
        $this->info('3️⃣ Testing client credentials flow...');
        $tokenUrl = $config['auth_server_url'] . '/realms/' . $config['realm'] . '/protocol/openid-connect/token';
        
        try {
            $response = Http::asForm()->post($tokenUrl, [
                'grant_type' => 'client_credentials',
                'client_id' => $config['client_id'],
                'client_secret' => $config['client_secret'],
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $this->info('✅ Client credentials flow successful!');
                $this->line("Token Type: {$data['token_type']}");
                $this->line("Expires In: {$data['expires_in']} seconds");
                
                $accessToken = $data['access_token'];
                $this->line("Access Token: " . substr($accessToken, 0, 20) . '...');

                // Test 4: Admin API access
                $this->info('4️⃣ Testing admin API access...');
                $usersUrl = $config['auth_server_url'] . '/admin/realms/' . $config['realm'] . '/users';
                
                $adminResponse = Http::withToken($accessToken)->get($usersUrl, ['max' => 1]);
                
                if ($adminResponse->successful()) {
                    $users = $adminResponse->json();
                    $this->info('✅ Admin API access successful!');
                    $this->line("Found " . count($users) . " user(s) in the realm");
                    
                    // Test 5: Test the service
                    $this->info('5️⃣ Testing KeycloakUserRegistrationService...');
                    $service = app(KeycloakUserRegistrationService::class);
                    
                    // Test user existence check
                    $testUsername = 'test-user-' . time();
                    $exists = $service->userExists($testUsername);
                    
                    if ($exists === false) {
                        $this->info("✅ User existence check working (user '$testUsername' does not exist)");
                    } else {
                        $this->warn("⚠️  User existence check returned true for non-existent user");
                    }
                    
                    $this->info('🎉 All tests passed! Keycloak integration is working correctly.');
                    
                } elseif ($adminResponse->status() === 403) {
                    $this->error('❌ Admin API access forbidden (HTTP 403)');
                    $this->warn("💡 Client '{$config['client_id']}' doesn't have admin permissions");
                    $this->warn('💡 Please enable Service Account and assign admin roles');
                    return 1;
                } else {
                    $this->error("❌ Admin API access failed (HTTP {$adminResponse->status()})");
                    $this->line("Response: " . $adminResponse->body());
                    return 1;
                }

            } elseif ($response->status() === 401) {
                $this->error('❌ Client credentials authentication failed (HTTP 401)');
                $this->warn('💡 Please check client ID and secret');
                $this->warn("💡 Ensure client '{$config['client_id']}' exists with correct credentials");
                return 1;
            } elseif ($response->status() === 400) {
                $this->error('❌ Bad request (HTTP 400)');
                $this->warn("💡 Please check if 'Service Account' is enabled for client '{$config['client_id']}'");
                $this->line("Error: " . $response->body());
                return 1;
            } else {
                $this->error("❌ Client credentials flow failed (HTTP {$response->status()})");
                $this->line("Error: " . $response->body());
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("❌ Exception during token request: {$e->getMessage()}");
            return 1;
        }

        $this->line('');
        $this->info('📋 Configuration Summary:');
        $this->line('✅ Server connectivity: OK');
        $this->line('✅ Realm accessibility: OK');
        $this->line('✅ Client credentials: OK');
        $this->line('✅ Admin API access: OK');
        $this->line('✅ Service integration: OK');

        return 0;
    }
}
