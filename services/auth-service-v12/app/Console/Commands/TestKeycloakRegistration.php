<?php

namespace App\Console\Commands;

use App\Services\Auth\KeycloakUserRegistrationService;
use Illuminate\Console\Command;

class TestKeycloakRegistration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keycloak:test-registration {username} {email} {firstName} {lastName}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Keycloak user registration';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $username = $this->argument('username');
        $email = $this->argument('email');
        $firstName = $this->argument('firstName');
        $lastName = $this->argument('lastName');

        $this->info("🧪 Testing Keycloak User Registration");
        $this->line("Username: $username");
        $this->line("Email: $email");
        $this->line("Name: $firstName $lastName");
        $this->line('');

        try {
            $service = app(KeycloakUserRegistrationService::class);

            // First check if user already exists
            $this->info('1️⃣ Checking if user already exists...');
            $exists = $service->userExists($username);
            
            if ($exists) {
                $this->warn("⚠️  User '$username' already exists in Keycloak");
                return 1;
            }
            
            $this->info("✅ User '$username' does not exist, proceeding with registration");

            // Test registration
            $this->info('2️⃣ Attempting to register user...');
            
            $userData = [
                'username' => $username,
                'email' => $email,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'password' => 'TempPassword123!', // Temporary password
            ];

            $result = $service->registerUser($userData);

            if ($result['success']) {
                $this->info('✅ User registration successful!');
                $this->line("User ID: " . ($result['user_id'] ?? 'Unknown'));
                $this->line("Message: " . $result['message']);
                
                // Verify the user was created
                $this->info('3️⃣ Verifying user was created...');
                $exists = $service->userExists($username);
                
                if ($exists) {
                    $this->info('✅ User verification successful - user exists in Keycloak');
                } else {
                    $this->error('❌ User verification failed - user not found after registration');
                }
                
                return 0;
            } else {
                $this->error('❌ User registration failed');
                $this->line("Message: " . $result['message']);
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("❌ Exception during registration test: " . $e->getMessage());
            return 1;
        }
    }
}
