<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;

/**
 * Response Helper Trait
 *
 * Provides standardized response methods for API controllers.
 *
 * STANDARDIZED RESPONSE FORMAT:
 * {
 *   "success": boolean,    // true for success, false for error
 *   "data": object|array,  // response data (always present, empty {} if no data)
 *   "message": string      // human-readable message
 * }
 *
 * COMMON HTTP STATUS CODES:
 * - 200: OK (successful GET, PUT, PATCH)
 * - 201: Created (successful POST)
 * - 202: Accepted (request accepted for processing)
 * - 204: No Content (successful DELETE)
 * - 400: Bad Request (client error)
 * - 401: Unauthorized (authentication required)
 * - 403: Forbidden (access denied)
 * - 404: Not Found (resource not found)
 * - 409: Conflict (resource conflict)
 * - 422: Unprocessable Entity (validation error)
 * - 429: Too Many Requests (rate limiting)
 * - 500: Internal Server Error (server error)
 *
 * USAGE EXAMPLES:
 * - Success: return $this->successResponse('User found', $userData);
 * - Error: return $this->errorResponse('User not found');
 * - Validation: return $this->validationErrorResponse($errors);
 * - Custom: return $this->apiResponse(true, 'Custom message', $data, 200);
 */
trait ResponseHelper
{
    /**
     * Return a standardized success response.
     * Format: { success: true, data: {}, message: "string" }
     */
    protected function successResponse(string $message, ?array $data = null, int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'data' => $data ?? [],
            'message' => $message,
        ];

        return response()->json($response, $statusCode);
    }

    /**
     * Return a standardized error response.
     * Format: { success: false, data: {}, message: "string" }
     */
    protected function errorResponse(string $message, ?array $data = null, int $statusCode = 400): JsonResponse
    {
        $response = [
            'success' => false,
            'data' => $data ?? [],
            'message' => $message,
        ];

        return response()->json($response, $statusCode);
    }

    /**
     * Return a standardized API response with flexible data structure.
     * This is the core method that ensures consistent response format.
     */
    protected function apiResponse(bool $success, string $message, ?array $data = null, int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => $success,
            'data' => $data ?? [],
            'message' => $message,
        ];

        return response()->json($response, $statusCode);
    }

    /**
     * Return a validation error response.
     * Format: { success: false, data: { errors: {...} }, message: "Validation failed" }
     */
    protected function validationErrorResponse(array $errors, string $message = 'Validation failed'): JsonResponse
    {
        return $this->apiResponse(false, $message, ['errors' => $errors], 422);
    }

    /**
     * Return an unauthorized error response.
     * Format: { success: false, data: {}, message: "Unauthorized" }
     */
    protected function unauthorizedResponse(string $message = 'Unauthorized', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(false, $message, $data, 401);
    }

    /**
     * Return a forbidden error response.
     * Format: { success: false, data: {}, message: "Forbidden" }
     */
    protected function forbiddenResponse(string $message = 'Forbidden', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(false, $message, $data, 403);
    }

    /**
     * Return a not found error response.
     * Format: { success: false, data: {}, message: "Not found" }
     */
    protected function notFoundResponse(string $message = 'Not found', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(false, $message, $data, 404);
    }

    /**
     * Return a server error response.
     * Format: { success: false, data: {}, message: "Server error" }
     */
    protected function serverErrorResponse(string $message = 'Server error', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(false, $message, $data, 500);
    }

    /**
     * Return a created response (201).
     * Format: { success: true, data: {}, message: "Resource created successfully" }
     */
    protected function createdResponse(string $message = 'Resource created successfully', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(true, $message, $data, 201);
    }

    /**
     * Return an accepted response (202).
     * Format: { success: true, data: {}, message: "Request accepted" }
     */
    protected function acceptedResponse(string $message = 'Request accepted', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(true, $message, $data, 202);
    }

    /**
     * Return a no content response (204) with standardized format.
     * Format: { success: true, data: {}, message: "No content" }
     */
    protected function noContentResponse(string $message = 'No content'): JsonResponse
    {
        return $this->apiResponse(true, $message, [], 204);
    }

    /**
     * Return a conflict error response (409).
     * Format: { success: false, data: {}, message: "Conflict" }
     */
    protected function conflictResponse(string $message = 'Conflict', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(false, $message, $data, 409);
    }

    /**
     * Return an unprocessable entity response (422).
     * Format: { success: false, data: {}, message: "Unprocessable entity" }
     */
    protected function unprocessableEntityResponse(string $message = 'Unprocessable entity', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(false, $message, $data, 422);
    }

    /**
     * Return a too many requests response (429).
     * Format: { success: false, data: {}, message: "Too many requests" }
     */
    protected function tooManyRequestsResponse(string $message = 'Too many requests', ?array $data = null): JsonResponse
    {
        return $this->apiResponse(false, $message, $data, 429);
    }
}
