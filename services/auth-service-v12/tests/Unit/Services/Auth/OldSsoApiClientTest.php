<?php

namespace Tests\Unit\Services\Auth;

use App\Services\Auth\OldSsoApiClient;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class OldSsoApiClientTest extends TestCase
{
    private OldSsoApiClient $client;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        config([
            'old_sso.domain_1' => 'https://test-domain-1.com',
            'old_sso.domain_2' => 'https://test-domain-2.com',
            'old_sso.api_key' => 'test-api-key',
            'old_sso.client_id' => 'test-client-id',
            'old_sso.client_secret' => 'test-client-secret',
            'old_sso.source' => 'test-source',
            'old_sso.ref_url_domain' => 'https://test-ref.com',
            'old_sso.endpoints' => [
                'user_search' => '/api/v2/users/search',
                'user_otp' => '/api/v2/users/otp',
                'user_login' => '/api/v2/users/login',
                'user_register' => '/api/v2/users',
            ],
            'old_sso.http' => [
                'timeout' => 30,
            ],
            'old_sso.headers' => [
                'User-Agent' => 'Laravel Auth Service v12',
                'Accept' => 'application/json',
            ],
        ]);

        $this->client = new OldSsoApiClient();
    }

    public function test_search_user_success(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $expectedResponse = [
            'id' => 123,
            'email' => $username,
            'first_name' => 'Test',
            'last_name' => 'User',
        ];

        Http::fake([
            'https://test-domain-1.com/api/v2/users/search*' => Http::response($expectedResponse, 200),
        ]);

        // Act
        $result = $this->client->searchUser($username);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('Operation user_search completed successfully', $result['message']);
        $this->assertEquals($expectedResponse, $result['data']);
        $this->assertEquals(200, $result['status_code']);

        Http::assertSent(function ($request) use ($username) {
            return $request->url() === 'https://test-domain-1.com/api/v2/users/search?source=test-source&api_key=test-api-key&username=' . urlencode($username);
        });
    }

    public function test_search_user_not_found(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $errorResponse = ['message' => 'User not found'];

        Http::fake([
            'https://test-domain-1.com/api/v2/users/search*' => Http::response($errorResponse, 404),
        ]);

        // Act
        $result = $this->client->searchUser($username);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertEquals('User not found', $result['message']);
        $this->assertEquals($errorResponse, $result['data']);
        $this->assertEquals(404, $result['status_code']);
    }

    public function test_request_otp_success(): void
    {
        // Arrange
        $userId = '123';
        $expectedResponse = ['message' => 'OTP sent successfully'];

        Http::fake([
            'https://test-domain-2.com/api/v2/users/otp' => Http::response($expectedResponse, 200),
        ]);

        // Act
        $result = $this->client->requestOtp($userId);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('Operation request_otp completed successfully', $result['message']);
        $this->assertEquals($expectedResponse, $result['data']);

        Http::assertSent(function ($request) use ($userId) {
            $body = $request->data();
            return $request->url() === 'https://test-domain-2.com/api/v2/users/otp' &&
                   $body['api_key'] === 'test-api-key' &&
                   $body['user_id'] === $userId &&
                   $body['source'] === 'test-source';
        });
    }

    public function test_login_with_password_success(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $password = 'testpassword';
        $expectedResponse = [
            'access_token' => 'test-token',
            'user' => ['id' => 123, 'email' => $username],
        ];

        Http::fake([
            'https://test-domain-1.com/api/v2/users/login' => Http::response($expectedResponse, 200),
        ]);

        // Act
        $result = $this->client->loginWithPassword($username, $password);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals($expectedResponse, $result['data']);

        Http::assertSent(function ($request) use ($username, $password) {
            $body = $request->data();
            return $request->url() === 'https://test-domain-1.com/api/v2/users/login' &&
                   $body['username'] === $username &&
                   $body['password'] === $password &&
                   $body['grant_type'] === 'user_login';
        });
    }

    public function test_login_with_otp_success(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $otp = '123456';
        $expectedResponse = [
            'access_token' => 'test-token',
            'user' => ['id' => 123, 'email' => $username],
        ];

        Http::fake([
            'https://test-domain-1.com/api/v2/users/login' => Http::response($expectedResponse, 200),
        ]);

        // Act
        $result = $this->client->loginWithOtp($username, $otp);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals($expectedResponse, $result['data']);

        Http::assertSent(function ($request) use ($username, $otp) {
            $body = $request->data();
            return $request->url() === 'https://test-domain-1.com/api/v2/users/login' &&
                   $body['username'] === $username &&
                   $body['login_otp'] === $otp &&
                   $body['grant_type'] === 'user_login';
        });
    }

    public function test_register_user_success(): void
    {
        // Arrange
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'mobile' => '919988877766',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
        ];
        $expectedResponse = [
            'id' => 456,
            'message' => 'User registered successfully',
        ];

        Http::fake([
            'https://test-domain-2.com/api/v2/users' => Http::response($expectedResponse, 201),
        ]);

        // Act
        $result = $this->client->registerUser($userData);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals($expectedResponse, $result['data']);

        Http::assertSent(function ($request) use ($userData) {
            $body = $request->data();
            return $request->url() === 'https://test-domain-2.com/api/v2/users' &&
                   $body['first_name'] === $userData['first_name'] &&
                   $body['email'] === $userData['email'] &&
                   $body['api_key'] === 'test-api-key';
        });
    }

    public function test_http_exception_handling(): void
    {
        // Arrange
        Http::fake([
            '*' => function () {
                throw new \Exception('Network error');
            },
        ]);

        // Act
        $result = $this->client->searchUser('<EMAIL>');

        // Assert
        $this->assertFalse($result['success']);
        $this->assertStringContains('User search failed: Network error', $result['message']);
        $this->assertNull($result['data']);
    }
}
