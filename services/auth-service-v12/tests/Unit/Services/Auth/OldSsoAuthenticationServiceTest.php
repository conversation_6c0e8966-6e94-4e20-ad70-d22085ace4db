<?php

namespace Tests\Unit\Services\Auth;

use App\Models\User;
use App\Services\Auth\OldSsoApiClient;
use App\Services\Auth\OldSsoAuthenticationService;
use App\Services\Auth\KeycloakAuthenticationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class OldSsoAuthenticationServiceTest extends TestCase
{
    use RefreshDatabase;

    private OldSsoAuthenticationService $service;
    private $oldSsoClientMock;
    private $keycloakServiceMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->oldSsoClientMock = Mockery::mock(OldSsoApiClient::class);
        $this->keycloakServiceMock = Mockery::mock(KeycloakAuthenticationService::class);

        $this->service = new OldSsoAuthenticationService(
            $this->oldSsoClientMock,
            $this->keycloakServiceMock
        );

        // Set up test configuration
        config([
            'old_sso.password_sync.fallback_password_pattern' => '1to9',
        ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_user_exists_returns_true_when_user_found(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $userData = [
            'id' => 123,
            'email' => $username,
            'first_name' => 'Test',
            'last_name' => 'User',
        ];

        $this->oldSsoClientMock
            ->shouldReceive('searchUser')
            ->with($username)
            ->once()
            ->andReturn([
                'success' => true,
                'data' => $userData,
            ]);

        // Act
        $result = $this->service->userExists($username);

        // Assert
        $this->assertTrue($result['exists']);
        $this->assertEquals('User found in old SSO', $result['message']);
        $this->assertEquals($userData, $result['user_data']);
    }

    public function test_user_exists_returns_false_when_user_not_found(): void
    {
        // Arrange
        $username = '<EMAIL>';

        $this->oldSsoClientMock
            ->shouldReceive('searchUser')
            ->with($username)
            ->once()
            ->andReturn([
                'success' => true,
                'data' => null,
            ]);

        // Act
        $result = $this->service->userExists($username);

        // Assert
        $this->assertFalse($result['exists']);
        $this->assertEquals('User not found in old SSO', $result['message']);
        $this->assertNull($result['user_data']);
    }

    public function test_authenticate_with_password_success(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $password = 'testpassword';
        $oldSsoData = [
            'id' => 123,
            'email' => $username,
            'first_name' => 'Test',
            'last_name' => 'User',
        ];

        $this->oldSsoClientMock
            ->shouldReceive('loginWithPassword')
            ->with($username, $password)
            ->once()
            ->andReturn([
                'success' => true,
                'data' => $oldSsoData,
            ]);

        $this->keycloakServiceMock
            ->shouldReceive('authenticate')
            ->with($username, $password)
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'Keycloak authentication successful',
            ]);

        // Act
        $result = $this->service->authenticateWithPassword($username, $password);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('Hybrid authentication successful', $result['message']);
        $this->assertInstanceOf(User::class, $result['user']);
        $this->assertNotEmpty($result['token']);
        $this->assertEquals('hybrid', $result['auth_type']);
        $this->assertTrue($result['keycloak_synced']);
    }

    public function test_authenticate_with_password_fails_when_old_sso_fails(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $password = 'wrongpassword';

        $this->oldSsoClientMock
            ->shouldReceive('loginWithPassword')
            ->with($username, $password)
            ->once()
            ->andReturn([
                'success' => false,
                'message' => 'Invalid credentials',
            ]);

        // Act
        $result = $this->service->authenticateWithPassword($username, $password);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertStringContains('Old SSO authentication failed', $result['message']);
        $this->assertEquals('old_sso', $result['auth_type']);
    }

    public function test_authenticate_with_otp_success(): void
    {
        // Arrange
        $username = '919988877766';
        $otp = '123456';
        $oldSsoData = [
            'id' => 123,
            'mobile' => $username,
            'first_name' => 'Test',
            'last_name' => 'User',
        ];

        $this->oldSsoClientMock
            ->shouldReceive('loginWithOtp')
            ->with($username, $otp)
            ->once()
            ->andReturn([
                'success' => true,
                'data' => $oldSsoData,
            ]);

        $this->keycloakServiceMock
            ->shouldReceive('authenticate')
            ->with($username, '123456789') // fallback password
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'Keycloak authentication successful',
            ]);

        // Act
        $result = $this->service->authenticateWithOtp($username, $otp);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('Hybrid OTP authentication successful', $result['message']);
        $this->assertInstanceOf(User::class, $result['user']);
        $this->assertNotEmpty($result['token']);
        $this->assertEquals('hybrid_otp', $result['auth_type']);
        $this->assertTrue($result['keycloak_synced']);
    }

    public function test_request_otp_success(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $userData = ['id' => 123];

        $this->oldSsoClientMock
            ->shouldReceive('searchUser')
            ->with($username)
            ->once()
            ->andReturn([
                'success' => true,
                'data' => $userData,
            ]);

        $this->oldSsoClientMock
            ->shouldReceive('requestOtp')
            ->with('123')
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'OTP sent successfully',
            ]);

        // Act
        $result = $this->service->requestOtp($username);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('OTP sent successfully', $result['message']);
    }

    public function test_request_otp_fails_when_user_not_found(): void
    {
        // Arrange
        $username = '<EMAIL>';

        $this->oldSsoClientMock
            ->shouldReceive('searchUser')
            ->with($username)
            ->once()
            ->andReturn([
                'success' => true,
                'data' => null,
            ]);

        // Act
        $result = $this->service->requestOtp($username);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertEquals('User not found in old SSO system', $result['message']);
    }

    public function test_register_user_success(): void
    {
        // Arrange
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'mobile' => '919988877766',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
        ];

        $oldSsoData = [
            'id' => 456,
            'message' => 'User registered successfully',
        ];

        $this->oldSsoClientMock
            ->shouldReceive('registerUser')
            ->with($userData)
            ->once()
            ->andReturn([
                'success' => true,
                'data' => $oldSsoData,
            ]);

        // Act
        $result = $this->service->registerUser($userData);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertEquals('Hybrid registration successful', $result['message']);
        $this->assertInstanceOf(User::class, $result['user']);
        $this->assertEquals('hybrid', $result['auth_type']);
        $this->assertTrue($result['keycloak_registered']);
    }

    public function test_register_user_fails_when_old_sso_fails(): void
    {
        // Arrange
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'mobile' => '919988877766',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
        ];

        $this->oldSsoClientMock
            ->shouldReceive('registerUser')
            ->with($userData)
            ->once()
            ->andReturn([
                'success' => false,
                'message' => 'Email already exists',
            ]);

        // Act
        $result = $this->service->registerUser($userData);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertStringContains('Old SSO registration failed', $result['message']);
        $this->assertEquals('old_sso', $result['auth_type']);
    }
}
