<?php

namespace Tests\Feature\Api\V2;

use App\Models\User;
use App\Services\Auth\OldSsoAuthenticationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Tests\TestCase;

class HybridAuthControllerTest extends TestCase
{
    use RefreshDatabase;

    private $hybridAuthServiceMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->hybridAuthServiceMock = Mockery::mock(OldSsoAuthenticationService::class);
        $this->app->instance(OldSsoAuthenticationService::class, $this->hybridAuthServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function test_search_user_returns_user_found(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $userData = [
            'id' => 123,
            'email' => $username,
            'first_name' => 'Test',
            'last_name' => 'User',
        ];

        $this->hybridAuthServiceMock
            ->shouldReceive('userExists')
            ->with($username)
            ->once()
            ->andReturn([
                'exists' => true,
                'user_data' => $userData,
            ]);

        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/search-user', [
            'username' => $username,
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User found in old SSO system',
                'data' => [
                    'user_exists' => true,
                    'user_data' => $userData,
                    'can_login' => true,
                    'login_methods' => ['password', 'otp'],
                ],
            ]);
    }

    public function test_search_user_returns_user_not_found(): void
    {
        // Arrange
        $username = '<EMAIL>';

        $this->hybridAuthServiceMock
            ->shouldReceive('userExists')
            ->with($username)
            ->once()
            ->andReturn([
                'exists' => false,
                'user_data' => null,
            ]);

        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/search-user', [
            'username' => $username,
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'User not found in old SSO system',
                'data' => [
                    'user_exists' => false,
                    'user_data' => null,
                    'can_login' => false,
                    'can_register' => true,
                ],
            ]);
    }

    public function test_search_user_validates_username_format(): void
    {
        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/search-user', [
            'username' => 'invalid-username',
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['username']);
    }

    public function test_login_with_password_success(): void
    {
        // Arrange
        $username = '<EMAIL>';
        $password = 'testpassword';
        $user = User::factory()->create([
            'email_id' => $username,
            'first_name' => 'Test',
            'last_name' => 'User',
        ]);

        $this->hybridAuthServiceMock
            ->shouldReceive('authenticateWithPassword')
            ->with($username, $password)
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'Hybrid authentication successful',
                'user' => $user,
                'token' => 'test-token-123',
                'auth_type' => 'hybrid',
                'keycloak_synced' => true,
            ]);

        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/login', [
            'username' => $username,
            'password' => $password,
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Authentication successful',
                'data' => [
                    'token' => 'test-token-123',
                    'token_type' => 'Bearer',
                    'auth_type' => 'hybrid',
                    'keycloak_synced' => true,
                ],
            ]);
    }

    public function test_login_with_otp_success(): void
    {
        // Arrange
        $username = '919988877766';
        $otp = '123456';
        $user = User::factory()->create([
            'phone' => $username,
            'first_name' => 'Test',
            'last_name' => 'User',
        ]);

        $this->hybridAuthServiceMock
            ->shouldReceive('authenticateWithOtp')
            ->with($username, $otp)
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'Hybrid OTP authentication successful',
                'user' => $user,
                'token' => 'test-otp-token-123',
                'auth_type' => 'hybrid_otp',
                'keycloak_synced' => true,
            ]);

        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/login', [
            'username' => $username,
            'login_otp' => $otp,
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Authentication successful',
                'data' => [
                    'token' => 'test-otp-token-123',
                    'token_type' => 'Bearer',
                    'auth_type' => 'hybrid_otp',
                    'keycloak_synced' => true,
                ],
            ]);
    }

    public function test_login_validates_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/login', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['username', 'login_method']);
    }

    public function test_login_validates_password_or_otp_required(): void
    {
        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/login', [
            'username' => '<EMAIL>',
            'password' => 'testpass',
            'login_otp' => '123456',
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['login_method']);
    }

    public function test_request_otp_success(): void
    {
        // Arrange
        $username = '<EMAIL>';

        $this->hybridAuthServiceMock
            ->shouldReceive('requestOtp')
            ->with($username)
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'OTP sent successfully',
            ]);

        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/request-otp', [
            'username' => $username,
        ]);

        // Assert
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'OTP sent successfully',
                'data' => [
                    'otp_sent' => true,
                    'message' => 'OTP has been sent to your registered mobile number',
                    'expires_in' => 300,
                ],
            ]);
    }

    public function test_register_user_success(): void
    {
        // Arrange
        $userData = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'mobile' => '919988877766',
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!',
        ];

        $user = User::factory()->create([
            'email_id' => $userData['email'],
            'phone' => $userData['mobile'],
            'first_name' => $userData['first_name'],
            'last_name' => $userData['last_name'],
        ]);

        $this->hybridAuthServiceMock
            ->shouldReceive('registerUser')
            ->with($userData)
            ->once()
            ->andReturn([
                'success' => true,
                'message' => 'Hybrid registration successful',
                'user' => $user,
                'auth_type' => 'hybrid',
                'keycloak_registered' => true,
            ]);

        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/register', $userData);

        // Assert
        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'message' => 'Registration successful',
                'data' => [
                    'auth_type' => 'hybrid',
                    'keycloak_registered' => true,
                    'message' => 'Registration completed successfully. You can now login with your credentials.',
                ],
            ]);
    }

    public function test_register_user_validates_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/register', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'first_name',
                'last_name',
                'email',
                'mobile',
                'password',
                'password_confirmation',
            ]);
    }

    public function test_register_user_validates_password_strength(): void
    {
        // Act
        $response = $this->postJson('/api/v2/hybrid-auth/register', [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'mobile' => '919988877766',
            'password' => 'weak',
            'password_confirmation' => 'weak',
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['password']);
    }

    public function test_rate_limiting_is_applied(): void
    {
        // Arrange
        $username = '<EMAIL>';

        $this->hybridAuthServiceMock
            ->shouldReceive('userExists')
            ->andReturn(['exists' => false, 'user_data' => null]);

        // Act - Make multiple requests to trigger rate limiting
        for ($i = 0; $i < 12; $i++) {
            $response = $this->postJson('/api/v2/hybrid-auth/search-user', [
                'username' => $username,
            ]);
        }

        // Assert - The last request should be rate limited
        $response->assertStatus(429);
    }
}
