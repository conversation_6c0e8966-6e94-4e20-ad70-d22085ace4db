<?php

/**
 * Complete Forgot Password Flow Test
 * Tests all 3 steps: Request OTP → Verify OTP → Reset Password
 */

// Test configuration
$baseUrl = 'http://192.168.1.34:8000/api/v2/hybrid-auth';
$username = '918793644824'; // Replace with actual test username
$testOtp = '1234'; // Replace with actual OTP received
$newPassword = 'newPassword123!'; // New password to set
$fpAuthCode = ''; // Will be populated from verify OTP response

echo "=== Complete Forgot Password Flow Test ===\n\n";

// Helper function to make HTTP requests
function makeRequest($url, $data) {
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Step 1: Request OTP for forgot password
echo "Step 1: Requesting OTP for forgot password\n";
echo "URL: {$baseUrl}/forgot-password/request-otp\n";

$requestData = ['username' => $username];
echo "Request: " . json_encode($requestData, JSON_PRETTY_PRINT) . "\n";

$result1 = makeRequest($baseUrl . '/forgot-password/request-otp', $requestData);
echo "HTTP Code: {$result1['http_code']}\n";
echo "Response: " . $result1['response'] . "\n\n";

if ($result1['http_code'] !== 200 || !$result1['data']['success']) {
    echo "❌ Step 1 FAILED: Could not request OTP\n";
    exit(1);
}

echo "✅ Step 1 SUCCESS: OTP requested successfully\n\n";

// Step 2: Verify OTP and get auth code
echo "Step 2: Verifying OTP to get auth code\n";
echo "URL: {$baseUrl}/forgot-password/verify-otp\n";

$verifyData = [
    'username' => $username,
    'otp' => $testOtp
];
echo "Request: " . json_encode($verifyData, JSON_PRETTY_PRINT) . "\n";

$result2 = makeRequest($baseUrl . '/forgot-password/verify-otp', $verifyData);
echo "HTTP Code: {$result2['http_code']}\n";
echo "Response: " . $result2['response'] . "\n\n";

if ($result2['http_code'] !== 200 || !$result2['data']['success']) {
    echo "❌ Step 2 FAILED: Could not verify OTP\n";
    exit(1);
}

// Extract fp_auth_code from response
$fpAuthCode = $result2['data']['data']['fp_auth_code'] ?? null;
if (!$fpAuthCode) {
    echo "❌ Step 2 FAILED: fp_auth_code not found in response\n";
    exit(1);
}

echo "✅ Step 2 SUCCESS: OTP verified, auth code received\n";
echo "Auth Code: {$fpAuthCode}\n\n";

// Step 3: Reset password using auth code
echo "Step 3: Resetting password using auth code\n";
echo "URL: {$baseUrl}/forgot-password/reset-password\n";

$resetData = [
    'username' => $username,
    'fp_auth_code' => $fpAuthCode,
    'password' => $newPassword,
    'otp_type' => 'email'
];
echo "Request: " . json_encode(array_merge($resetData, ['password' => '***HIDDEN***']), JSON_PRETTY_PRINT) . "\n";

$result3 = makeRequest($baseUrl . '/forgot-password/reset-password', $resetData);
echo "HTTP Code: {$result3['http_code']}\n";
echo "Response: " . $result3['response'] . "\n\n";

if ($result3['http_code'] !== 200 || !$result3['data']['success']) {
    echo "❌ Step 3 FAILED: Could not reset password\n";
    exit(1);
}

echo "✅ Step 3 SUCCESS: Password reset successfully\n\n";

// Summary
echo "=== COMPLETE FLOW SUMMARY ===\n";
echo "✅ Step 1: OTP Request - SUCCESS\n";
echo "✅ Step 2: OTP Verification - SUCCESS (Auth code: " . substr($fpAuthCode, 0, 10) . "...)\n";
echo "✅ Step 3: Password Reset - SUCCESS\n\n";

echo "🎉 COMPLETE FORGOT PASSWORD FLOW - ALL STEPS SUCCESSFUL!\n";
echo "User can now login with the new password.\n\n";

echo "=== API ENDPOINTS USED ===\n";
echo "1. POST {$baseUrl}/forgot-password/request-otp\n";
echo "2. POST {$baseUrl}/forgot-password/verify-otp\n";
echo "3. POST {$baseUrl}/forgot-password/reset-password\n\n";

echo "=== Test Complete ===\n";
