<?php

/**
 * Test Registration Error Handling
 * Tests how the API handles registration errors with detailed error messages
 */

// Test configuration
$baseUrl = 'http://192.168.1.34:8000/api/v2/hybrid-auth';

echo "=== Registration Error Handling Test ===\n\n";

// Helper function to make HTTP requests
function makeRequest($url, $data) {
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Test 1: Registration with existing email
echo "Test 1: Registration with existing email\n";
echo "URL: {$baseUrl}/register\n";

$registrationData = [
    'username' => 'test_user_' . time(),
    'email' => '<EMAIL>', // Use an email that already exists
    'mobile' => '918793644824',
    'password' => 'TestPassword123!',
    'password_confirmation' => 'TestPassword123!',
    'first_name' => 'Test',
    'last_name' => 'User'
];

echo "Request: " . json_encode(array_merge($registrationData, [
    'password' => '***HIDDEN***',
    'password_confirmation' => '***HIDDEN***'
]), JSON_PRETTY_PRINT) . "\n";

$result1 = makeRequest($baseUrl . '/register', $registrationData);
echo "HTTP Code: {$result1['http_code']}\n";
echo "Response: " . $result1['response'] . "\n\n";

// Analyze the response
echo "=== Response Analysis ===\n";

if ($result1['http_code'] === 422) {
    echo "✅ CORRECT: HTTP 422 status code for validation error\n";
} else {
    echo "❌ UNEXPECTED: Expected HTTP 422, got {$result1['http_code']}\n";
}

if (!$result1['data']['success']) {
    echo "✅ CORRECT: success = false\n";
} else {
    echo "❌ UNEXPECTED: success should be false\n";
}

$message = $result1['data']['message'] ?? '';
echo "Message: {$message}\n";

if (isset($result1['data']['data']['errors'])) {
    echo "✅ CORRECT: Detailed errors included in response\n";
    echo "Errors: " . json_encode($result1['data']['data']['errors'], JSON_PRETTY_PRINT) . "\n";
} else {
    echo "❌ MISSING: No detailed errors in response\n";
}

if (isset($result1['data']['data']['error_code'])) {
    echo "✅ CORRECT: Error code included: " . $result1['data']['data']['error_code'] . "\n";
} else {
    echo "❌ MISSING: No error code in response\n";
}

if (isset($result1['data']['data']['user_id'])) {
    echo "✅ CORRECT: User ID included: " . $result1['data']['data']['user_id'] . "\n";
} else {
    echo "❌ MISSING: No user ID in response\n";
}

echo "\n=== Test 2: Registration with multiple validation errors ===\n";

$invalidRegistrationData = [
    'username' => '', // Empty username
    'email' => 'invalid-email', // Invalid email format
    'mobile' => '123', // Invalid mobile
    'password' => '123', // Weak password
    'password_confirmation' => '456', // Password mismatch
    'first_name' => '',
    'last_name' => ''
];

echo "Request: " . json_encode(array_merge($invalidRegistrationData, [
    'password' => '***HIDDEN***',
    'password_confirmation' => '***HIDDEN***'
]), JSON_PRETTY_PRINT) . "\n";

$result2 = makeRequest($baseUrl . '/register', $invalidRegistrationData);
echo "HTTP Code: {$result2['http_code']}\n";
echo "Response: " . $result2['response'] . "\n\n";

echo "=== Expected Error Response Format ===\n";
echo "{\n";
echo "  \"success\": false,\n";
echo "  \"message\": \"Email address is already registered.\",\n";
echo "  \"data\": {\n";
echo "    \"errors\": {\n";
echo "      \"email\": [\n";
echo "        \"Email already registered.\"\n";
echo "      ]\n";
echo "    },\n";
echo "    \"error_code\": 1018,\n";
echo "    \"user_id\": 92928\n";
echo "  }\n";
echo "}\n";

echo "\n=== Error Handling Features ===\n";
echo "1. User-friendly error messages (not technical)\n";
echo "2. Detailed validation errors for each field\n";
echo "3. Proper HTTP status codes (422 for validation errors)\n";
echo "4. Error codes for programmatic handling\n";
echo "5. User ID for existing user conflicts\n";
echo "6. Comprehensive logging for debugging\n";

echo "\n=== Error Message Improvements ===\n";
echo "Before: 'Old SSO registration failed: Array'\n";
echo "After: 'Email address is already registered.'\n";

echo "\n=== Test Complete ===\n";
