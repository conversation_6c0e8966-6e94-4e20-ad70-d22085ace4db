<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Old SSO Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for integrating with the legacy SSO system.
    | These settings control how the application communicates with the old
    | SSO API endpoints for user authentication and management.
    |
    */

    'domain_1' => env('OLD_SSO_DOMAIN_1', 'https://company-domain-1'),
    'domain_2' => env('OLD_SSO_DOMAIN_2', 'https://company-domain-2'),
    'api_key' => env('OLD_SSO_API_KEY'),
    'client_id' => env('OLD_SSO_CLIENT_ID'),
    'client_secret' => env('OLD_SSO_CLIENT_SECRET'),
    'source' => env('OLD_SSO_SOURCE'),
    'ref_url_domain' => env('OLD_SSO_REF_URL_DOMAIN'),
    'auth_server_url' => env('OLD_SSO_AUTH_SERVER_URL'),
    'realm' => env('OLD_SSO_REALM'),

    /*
    |--------------------------------------------------------------------------
    | API Endpoints
    |--------------------------------------------------------------------------
    |
    | Define the specific API endpoints for different operations
    |
    */

    'endpoints' => [
        'user_search' => '/api/v2/users/search',
        'user_otp' => '/api/v2/users/otp',
        'user_verify_otp' => '/api/v2/users/verifyotp',
        'user_login' => '/api/v2/users/login',
        'user_register' => '/api/v2/users',
    ],

    /*
    |--------------------------------------------------------------------------
    | HTTP Configuration
    |--------------------------------------------------------------------------
    |
    | HTTP client configuration for API calls
    |
    */

    'http' => [
        'timeout' => 30,
        'connect_timeout' => 10,
        'retry_attempts' => 3,
        'retry_delay' => 1000, // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Headers
    |--------------------------------------------------------------------------
    |
    | Default headers to be sent with all requests
    |
    */

    'headers' => [
        'User-Agent' => env('OLD_SSO_USER_AGENT', 'OneFoodDialer-AuthService/2.0 (Laravel/11.x; PHP/8.3)'),
        'Accept' => 'application/json, text/plain, */*',
        'Accept-Language' => 'en-US,en;q=0.5',
        'Accept-Encoding' => 'gzip, deflate, br, zstd',
        'Connection' => 'keep-alive',
        'Content-Type' => 'application/x-www-form-urlencoded',
        'Cache-Control' => 'no-cache',
        'Sec-Fetch-Dest' => 'empty',
        'Sec-Fetch-Mode' => 'cors',
        'Sec-Fetch-Site' => 'same-site',
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Sync Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for password synchronization between old SSO and Keycloak
    |
    */

    'password_sync' => [
        'enabled' => true,
        'update_on_login' => true,
        'fallback_password_pattern' => '1to9', // Pattern for Keycloak fallback passwords
    ],
];
