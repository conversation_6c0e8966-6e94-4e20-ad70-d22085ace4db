#!/bin/bash

# Enhanced Hybrid Authentication Search Test Script
# Tests the enhanced search functionality that checks both Old SSO and Keycloak

# Configuration
BASE_URL="http://localhost:8001/api/v2/hybrid-auth"
CONTENT_TYPE="Content-Type: application/json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
    esac
}

# Function to test search endpoint with detailed analysis
test_search() {
    local username=$1
    local description=$2
    
    print_status "INFO" "Testing: $description"
    print_status "INFO" "Username: $username"
    
    response=$(curl -s -w "\n%{http_code}" -X POST \
        -H "$CONTENT_TYPE" \
        -d "{\"username\": \"$username\"}" \
        "$BASE_URL/search-user")
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    echo "Response Code: $http_code"
    echo "Response Body: $response_body"
    
    if [ "$http_code" -eq 200 ]; then
        print_status "SUCCESS" "Search completed successfully"
        
        # Parse JSON response to extract key information
        user_exists=$(echo "$response_body" | grep -o '"user_exists":[^,]*' | cut -d':' -f2 | tr -d ' ')
        old_sso_exists=$(echo "$response_body" | grep -o '"old_sso_exists":[^,]*' | cut -d':' -f2 | tr -d ' ')
        keycloak_exists=$(echo "$response_body" | grep -o '"keycloak_exists":[^,]*' | cut -d':' -f2 | tr -d ' ')
        sync_status=$(echo "$response_body" | grep -o '"sync_status":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        
        print_status "INFO" "Analysis Results:"
        echo "  - User Exists: $user_exists"
        echo "  - Old SSO Exists: $old_sso_exists"
        echo "  - Keycloak Exists: $keycloak_exists"
        echo "  - Sync Status: $sync_status"
        
        # Provide recommendations based on sync status
        case $sync_status in
            "synced")
                print_status "SUCCESS" "✅ User is fully synchronized between both systems"
                ;;
            "needs_keycloak_registration")
                print_status "WARNING" "⚠️  User exists in Old SSO but needs Keycloak registration"
                print_status "INFO" "💡 Recommendation: User will be auto-registered in Keycloak on next login"
                ;;
            "keycloak_only")
                print_status "WARNING" "⚠️  User exists only in Keycloak"
                print_status "INFO" "💡 Recommendation: Use Keycloak login instead of hybrid login"
                ;;
            "not_found")
                print_status "ERROR" "❌ User not found in either system"
                print_status "INFO" "💡 Recommendation: User needs to register"
                ;;
            *)
                print_status "WARNING" "❓ Unknown sync status: $sync_status"
                ;;
        esac
    else
        print_status "ERROR" "Search failed (HTTP $http_code)"
    fi
    
    echo "========================================="
}

# Start testing
print_status "INFO" "Starting Enhanced Hybrid Authentication Search Tests"
echo "========================================"

# Test cases with different scenarios
test_search "918793644824" "Mobile number from your cURL example"
test_search "<EMAIL>" "Email address - typical user"
test_search "<EMAIL>" "Admin email - might exist in Keycloak"
test_search "<EMAIL>" "Non-existent user"
test_search "919988877766" "Another mobile number"

print_status "INFO" "Enhanced Search Tests Complete!"
echo "========================================"

# Additional test: Login after search to verify auto-registration
print_status "INFO" "Testing auto-registration during login..."

# Test login with a user that might need Keycloak registration
print_status "INFO" "Attempting login to trigger auto-registration..."
login_response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "$CONTENT_TYPE" \
    -d '{"username": "918793644824", "password": "testpassword"}' \
    "$BASE_URL/login")

login_http_code=$(echo "$login_response" | tail -n1)
login_response_body=$(echo "$login_response" | head -n -1)

echo "Login Response Code: $login_http_code"
echo "Login Response Body: $login_response_body"

if [ "$login_http_code" -eq 200 ]; then
    print_status "SUCCESS" "Login successful - auto-registration may have occurred"
    
    # Check if keycloak_synced is true in response
    keycloak_synced=$(echo "$login_response_body" | grep -o '"keycloak_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    if [ "$keycloak_synced" = "true" ]; then
        print_status "SUCCESS" "✅ Keycloak synchronization successful"
    else
        print_status "WARNING" "⚠️  Keycloak synchronization may have failed"
    fi
else
    print_status "WARNING" "Login failed - this is expected if credentials are incorrect"
fi

print_status "INFO" "All Enhanced Tests Complete!"
