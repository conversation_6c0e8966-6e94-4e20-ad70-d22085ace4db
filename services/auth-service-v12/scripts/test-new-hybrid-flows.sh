#!/bin/bash

# Test New Hybrid Authentication Flows
# 1. Update Keycloak credentials when Old SSO succeeds but Key<PERSON>loak fails
# 2. Mobile OTP verification for user activation

# Configuration
BASE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"

# Test user data
TEST_USERNAME="918793644824"
TEST_PASSWORD="your-actual-password-here"  # You'll need to provide the real password
TEST_OTP="1234"  # You'll need to provide the actual OTP

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

print_status "INFO" "🧪 Testing New Hybrid Authentication Flows"
echo "Test Username: $TEST_USERNAME"
echo "========================================"

# Test 1: Update Keycloak Credentials
print_status "INFO" "1️⃣ Testing Keycloak credentials update..."

if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
    print_status "WARNING" "⚠️  Please provide the actual password for testing"
    print_status "INFO" "Skipping Keycloak credentials update test"
else
    UPDATE_RESPONSE=$(curl -s -X POST "$BASE_URL/update-keycloak-credentials" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")

    echo "Update Credentials Response: $UPDATE_RESPONSE"

    UPDATE_SUCCESS=$(echo "$UPDATE_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$UPDATE_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Keycloak credentials update successful!"
        KEYCLOAK_USER_ID=$(echo "$UPDATE_RESPONSE" | grep -o '"keycloak_user_id":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        if [ -n "$KEYCLOAK_USER_ID" ] && [ "$KEYCLOAK_USER_ID" != "null" ]; then
            print_status "SUCCESS" "🆔 Keycloak User ID: $KEYCLOAK_USER_ID"
        fi
    else
        UPDATE_MESSAGE=$(echo "$UPDATE_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "WARNING" "⚠️  Keycloak credentials update result: $UPDATE_MESSAGE"
    fi
fi

echo ""

# Test 2: Mobile OTP Verification
print_status "INFO" "2️⃣ Testing mobile OTP verification..."

if [ "$TEST_OTP" = "1234" ]; then
    print_status "WARNING" "⚠️  Please provide the actual OTP for testing"
    print_status "INFO" "Skipping mobile OTP verification test"
else
    OTP_RESPONSE=$(curl -s -X POST "$BASE_URL/verify-mobile-otp" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"otp\": \"$TEST_OTP\"}")

    echo "OTP Verification Response: $OTP_RESPONSE"

    OTP_SUCCESS=$(echo "$OTP_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$OTP_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Mobile OTP verification successful!"
        
        USER_ACTIVATED=$(echo "$OTP_RESPONSE" | grep -o '"user_activated":[^,]*' | cut -d':' -f2 | tr -d ' ')
        MOBILE_VERIFIED=$(echo "$OTP_RESPONSE" | grep -o '"mobile_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "SUCCESS" "📱 User Activated: $USER_ACTIVATED"
        print_status "SUCCESS" "✅ Mobile Verified: $MOBILE_VERIFIED"
    else
        OTP_MESSAGE=$(echo "$OTP_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "WARNING" "⚠️  Mobile OTP verification result: $OTP_MESSAGE"
    fi
fi

echo ""

# Test 3: Test the enhanced login flow (should now handle Keycloak credential updates automatically)
print_status "INFO" "3️⃣ Testing enhanced login flow with automatic Keycloak credential updates..."

if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
    print_status "WARNING" "⚠️  Please provide the actual password for testing"
    print_status "INFO" "Skipping enhanced login test"
else
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")

    echo "Enhanced Login Response: $LOGIN_RESPONSE"

    LOGIN_SUCCESS=$(echo "$LOGIN_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$LOGIN_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Enhanced login successful!"
        
        AUTH_TYPE=$(echo "$LOGIN_RESPONSE" | grep -o '"auth_type":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        KEYCLOAK_SYNCED=$(echo "$LOGIN_RESPONSE" | grep -o '"keycloak_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "SUCCESS" "🔐 Auth Type: $AUTH_TYPE"
        print_status "SUCCESS" "🔄 Keycloak Synced: $KEYCLOAK_SYNCED"
        
        if [ "$KEYCLOAK_SYNCED" = "true" ]; then
            print_status "SUCCESS" "🎉 Keycloak synchronization successful!"
        fi
    else
        LOGIN_MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "WARNING" "⚠️  Enhanced login result: $LOGIN_MESSAGE"
    fi
fi

echo ""
echo "========================================"
print_status "INFO" "📋 New Hybrid Authentication Flows Summary:"
echo ""
print_status "INFO" "✅ Added Features:"
echo "  1. 🔄 Update Keycloak Credentials - When Old SSO succeeds but Keycloak fails"
echo "  2. 📱 Mobile OTP Verification - Activate users and mark mobile as verified"
echo "  3. 🚀 Enhanced Login Flow - Automatic Keycloak credential updates during login"
echo ""
print_status "INFO" "🔗 New API Endpoints:"
echo "  - POST /api/v2/hybrid-auth/update-keycloak-credentials"
echo "  - POST /api/v2/hybrid-auth/verify-mobile-otp"
echo ""
print_status "INFO" "📝 Usage Notes:"
echo "  - Keycloak credentials are automatically updated when authentication fails"
echo "  - Mobile OTP verification activates users in Old SSO (status: active, mobile_verified: 1)"
echo "  - No local database modifications are made as per requirements"
echo ""
print_status "INFO" "Test Complete!"
