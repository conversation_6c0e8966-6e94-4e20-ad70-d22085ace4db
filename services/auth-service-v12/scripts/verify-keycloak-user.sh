#!/bin/bash

# Script to verify if a user exists in Keycloak
# Usage: ./verify-keycloak-user.sh <username>

USERNAME=${1:-"918793644824"}
EMAIL="<EMAIL>"

# Configuration
KEY<PERSON>OAK_AUTH_SERVER_URL="https://devsso.cubeone.in"
KEYCLOAK_REALM="futurescape"
KEYCLOAK_CLIENT_ID="sso_migration"
KEYCLOAK_CLIENT_SECRET="OmbOXL0E7I9HGyjvNb2UmyVkRVOHE8ag"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

print_status "INFO" "🔍 Verifying user existence in Keycloak"
echo "Username: $USERNAME"
echo "Email: $EMAIL"
echo "Realm: $KEYCLOAK_REALM"
echo "========================================"

# Get admin token
print_status "INFO" "1️⃣ Getting admin token..."
TOKEN_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials" \
    -d "client_id=$KEYCLOAK_CLIENT_ID" \
    -d "client_secret=$KEYCLOAK_CLIENT_SECRET" \
    "$KEYCLOAK_AUTH_SERVER_URL/realms/$KEYCLOAK_REALM/protocol/openid-connect/token")

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
    print_status "ERROR" "Failed to get admin token"
    echo "Response: $TOKEN_RESPONSE"
    exit 1
fi

print_status "SUCCESS" "Admin token obtained"

# Search by username
print_status "INFO" "2️⃣ Searching by username: $USERNAME"
USERNAME_SEARCH=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
    "$KEYCLOAK_AUTH_SERVER_URL/admin/realms/$KEYCLOAK_REALM/users?username=$USERNAME")

echo "Username search result: $USERNAME_SEARCH"

if [ "$USERNAME_SEARCH" = "[]" ]; then
    print_status "WARNING" "❌ User NOT found by username"
    USERNAME_EXISTS=false
else
    print_status "SUCCESS" "✅ User found by username"
    USERNAME_EXISTS=true
fi

# Search by email
print_status "INFO" "3️⃣ Searching by email: $EMAIL"
EMAIL_SEARCH=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
    "$KEYCLOAK_AUTH_SERVER_URL/admin/realms/$KEYCLOAK_REALM/users?email=$EMAIL")

echo "Email search result: $EMAIL_SEARCH"

if [ "$EMAIL_SEARCH" = "[]" ]; then
    print_status "WARNING" "❌ User NOT found by email"
    EMAIL_EXISTS=false
else
    print_status "SUCCESS" "✅ User found by email"
    EMAIL_EXISTS=true
fi

# Final result
echo "========================================"
print_status "INFO" "📊 Final Results:"
echo "  - Username search: $($USERNAME_EXISTS && echo "EXISTS" || echo "NOT FOUND")"
echo "  - Email search: $($EMAIL_EXISTS && echo "EXISTS" || echo "NOT FOUND")"

if [ "$USERNAME_EXISTS" = true ] || [ "$EMAIL_EXISTS" = true ]; then
    print_status "SUCCESS" "🎉 User EXISTS in Keycloak"
    exit 0
else
    print_status "ERROR" "❌ User does NOT exist in Keycloak"
    exit 1
fi
