#!/bin/bash

# Hybrid Authentication Test Script
# This script tests the hybrid authentication endpoints

# Configuration
BASE_URL="http://localhost:8001/api/v2/hybrid-auth"
CONTENT_TYPE="Content-Type: application/json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
    esac
}

# Function to make HTTP request and check response
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    
    print_status "INFO" "Testing: $description"
    print_status "INFO" "Endpoint: $method $endpoint"
    
    if [ "$method" = "POST" ]; then
        response=$(curl -s -w "\n%{http_code}" -X POST \
            -H "$CONTENT_TYPE" \
            -d "$data" \
            "$BASE_URL$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X GET \
            -H "$CONTENT_TYPE" \
            "$BASE_URL$endpoint")
    fi
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all lines except last)
    response_body=$(echo "$response" | head -n -1)
    
    echo "Response Code: $http_code"
    echo "Response Body: $response_body"
    
    if [ "$http_code" -eq "$expected_status" ]; then
        print_status "SUCCESS" "Test passed (HTTP $http_code)"
    else
        print_status "ERROR" "Test failed (Expected HTTP $expected_status, got HTTP $http_code)"
    fi
    
    echo "----------------------------------------"
}

# Start testing
print_status "INFO" "Starting Hybrid Authentication Tests"
echo "========================================"

# Test 1: Search for existing user
test_endpoint "POST" "/search-user" \
    '{"username": "<EMAIL>"}' \
    200 \
    "Search for existing user"

# Test 2: Search for non-existing user
test_endpoint "POST" "/search-user" \
    '{"username": "<EMAIL>"}' \
    200 \
    "Search for non-existing user"

# Test 3: Search user with invalid username format
test_endpoint "POST" "/search-user" \
    '{"username": "invalid-username"}' \
    422 \
    "Search user with invalid username format"

# Test 4: Search user without username
test_endpoint "POST" "/search-user" \
    '{}' \
    422 \
    "Search user without username"

# Test 5: Request OTP for valid user
test_endpoint "POST" "/request-otp" \
    '{"username": "919988877766"}' \
    200 \
    "Request OTP for valid mobile number"

# Test 6: Request OTP for invalid user
test_endpoint "POST" "/request-otp" \
    '{"username": "invalid-mobile"}' \
    422 \
    "Request OTP for invalid mobile number"

# Test 7: Login with password
test_endpoint "POST" "/login" \
    '{"username": "<EMAIL>", "password": "testpassword"}' \
    200 \
    "Login with password"

# Test 8: Login with OTP
test_endpoint "POST" "/login" \
    '{"username": "919988877766", "login_otp": "123456"}' \
    200 \
    "Login with OTP"

# Test 9: Login with both password and OTP (should fail)
test_endpoint "POST" "/login" \
    '{"username": "<EMAIL>", "password": "testpass", "login_otp": "123456"}' \
    422 \
    "Login with both password and OTP (should fail)"

# Test 10: Login without credentials
test_endpoint "POST" "/login" \
    '{"username": "<EMAIL>"}' \
    422 \
    "Login without password or OTP"

# Test 11: Register new user
test_endpoint "POST" "/register" \
    '{
        "first_name": "John",
        "last_name": "Doe", 
        "email": "<EMAIL>",
        "mobile": "919988877766",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }' \
    201 \
    "Register new user"

# Test 12: Register user with weak password
test_endpoint "POST" "/register" \
    '{
        "first_name": "Jane",
        "last_name": "Doe",
        "email": "<EMAIL>", 
        "mobile": "919988877767",
        "password": "weak",
        "password_confirmation": "weak"
    }' \
    422 \
    "Register user with weak password"

# Test 13: Register user with mismatched passwords
test_endpoint "POST" "/register" \
    '{
        "first_name": "Bob",
        "last_name": "Smith",
        "email": "<EMAIL>",
        "mobile": "919988877768", 
        "password": "SecurePass123!",
        "password_confirmation": "DifferentPass123!"
    }' \
    422 \
    "Register user with mismatched passwords"

# Test 14: Register user with invalid mobile format
test_endpoint "POST" "/register" \
    '{
        "first_name": "Alice",
        "last_name": "Johnson",
        "email": "<EMAIL>",
        "mobile": "1234567890",
        "password": "SecurePass123!",
        "password_confirmation": "SecurePass123!"
    }' \
    422 \
    "Register user with invalid mobile format"

# Test 15: Register user with missing fields
test_endpoint "POST" "/register" \
    '{"email": "<EMAIL>"}' \
    422 \
    "Register user with missing required fields"

print_status "INFO" "All tests completed!"
echo "========================================"

# Rate limiting test
print_status "INFO" "Testing rate limiting..."
print_status "WARNING" "Making multiple rapid requests to test rate limiting"

for i in {1..12}; do
    response=$(curl -s -w "%{http_code}" -X POST \
        -H "$CONTENT_TYPE" \
        -d '{"username": "<EMAIL>"}' \
        "$BASE_URL/search-user")
    
    if [ "$response" = "429" ]; then
        print_status "SUCCESS" "Rate limiting triggered at request $i"
        break
    elif [ $i -eq 12 ]; then
        print_status "WARNING" "Rate limiting not triggered after 12 requests"
    fi
done

print_status "INFO" "Hybrid Authentication Tests Complete!"
