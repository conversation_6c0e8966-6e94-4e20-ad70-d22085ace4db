#!/bin/bash

# Keycloak Connection Test Script
# Tests the Keycloak connection and client credentials flow

# Configuration from .env
KEYCLOAK_AUTH_SERVER_URL="https://devsso.cubeone.in"
KEYCLOAK_REALM="fstech"
KEYCLOAK_CLIENT_ID="sso_migration"
KEYCLOAK_CLIENT_SECRET="YvqAKCayQOvjrrv81ki9QL8pvLw5Oylk"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO")
            echo -e "${BLUE}[INFO]${NC} $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
    esac
}

print_status "INFO" "Testing Keycloak Connection and Configuration"
echo "========================================"

# Test 1: Check if Keycloak server is reachable
print_status "INFO" "1. Testing Keycloak server connectivity..."
KEYCLOAK_BASE_URL="${KEYCLOAK_AUTH_SERVER_URL}"
echo "Keycloak URL: $KEYCLOAK_BASE_URL"

# Test basic connectivity
response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 10 "$KEYCLOAK_BASE_URL")
if [ "$response" -eq 200 ] || [ "$response" -eq 404 ] || [ "$response" -eq 302 ]; then
    print_status "SUCCESS" "✅ Keycloak server is reachable (HTTP $response)"
else
    print_status "ERROR" "❌ Keycloak server is not reachable (HTTP $response)"
    print_status "INFO" "Please check if Keycloak is running on $KEYCLOAK_BASE_URL"
    exit 1
fi

# Test 2: Check realm accessibility
print_status "INFO" "2. Testing realm accessibility..."
REALM_URL="${KEYCLOAK_AUTH_SERVER_URL}/realms/${KEYCLOAK_REALM}"
echo "Realm URL: $REALM_URL"

response=$(curl -s -w "%{http_code}" -o /dev/null "$REALM_URL")
if [ "$response" -eq 200 ]; then
    print_status "SUCCESS" "✅ Realm '$KEYCLOAK_REALM' is accessible"
else
    print_status "ERROR" "❌ Realm '$KEYCLOAK_REALM' is not accessible (HTTP $response)"
    print_status "INFO" "Please check if the realm '$KEYCLOAK_REALM' exists in Keycloak"
fi

# Test 3: Test token endpoint
print_status "INFO" "3. Testing token endpoint..."
TOKEN_URL="${KEYCLOAK_AUTH_SERVER_URL}/realms/${KEYCLOAK_REALM}/protocol/openid-connect/token"
echo "Token URL: $TOKEN_URL"

response=$(curl -s -w "%{http_code}" -o /dev/null "$TOKEN_URL")
if [ "$response" -eq 405 ] || [ "$response" -eq 400 ]; then
    print_status "SUCCESS" "✅ Token endpoint is accessible (HTTP $response - expected for GET request)"
else
    print_status "WARNING" "⚠️  Token endpoint response: HTTP $response"
fi

# Test 4: Test client credentials flow
print_status "INFO" "4. Testing client credentials flow..."
echo "Client ID: $KEYCLOAK_CLIENT_ID"
echo "Client Secret: ${KEYCLOAK_CLIENT_SECRET:0:10}..."

token_response=$(curl -s -w "\n%{http_code}" -X POST \
    -H "Content-Type: application/x-www-form-urlencoded" \
    -d "grant_type=client_credentials" \
    -d "client_id=$KEYCLOAK_CLIENT_ID" \
    -d "client_secret=$KEYCLOAK_CLIENT_SECRET" \
    "$TOKEN_URL")

# Extract HTTP status code (last line)
http_code=$(echo "$token_response" | tail -n1)
# Extract response body (all lines except last)
response_body=$(echo "$token_response" | head -n -1)

echo "Response Code: $http_code"
echo "Response Body: $response_body"

if [ "$http_code" -eq 200 ]; then
    print_status "SUCCESS" "✅ Client credentials flow successful!"
    
    # Extract access token
    access_token=$(echo "$response_body" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    if [ -n "$access_token" ]; then
        print_status "SUCCESS" "✅ Access token obtained: ${access_token:0:20}..."
        
        # Test 5: Test admin API access
        print_status "INFO" "5. Testing admin API access..."
        USERS_URL="${KEYCLOAK_AUTH_SERVER_URL}/admin/realms/${KEYCLOAK_REALM}/users"
        echo "Users API URL: $USERS_URL"
        
        admin_response=$(curl -s -w "\n%{http_code}" \
            -H "Authorization: Bearer $access_token" \
            "$USERS_URL?max=1")
        
        admin_http_code=$(echo "$admin_response" | tail -n1)
        admin_response_body=$(echo "$admin_response" | head -n -1)
        
        echo "Admin API Response Code: $admin_http_code"
        echo "Admin API Response Body: $admin_response_body"
        
        if [ "$admin_http_code" -eq 200 ]; then
            print_status "SUCCESS" "✅ Admin API access successful!"
            print_status "SUCCESS" "🎉 All Keycloak tests passed! The configuration is correct."
        elif [ "$admin_http_code" -eq 403 ]; then
            print_status "ERROR" "❌ Admin API access forbidden (HTTP 403)"
            print_status "INFO" "💡 The client '$KEYCLOAK_CLIENT_ID' doesn't have admin permissions."
            print_status "INFO" "💡 Please enable 'Service Account' and assign admin roles to the client."
        else
            print_status "ERROR" "❌ Admin API access failed (HTTP $admin_http_code)"
        fi
    else
        print_status "ERROR" "❌ No access token found in response"
    fi
elif [ "$http_code" -eq 401 ]; then
    print_status "ERROR" "❌ Client credentials authentication failed (HTTP 401)"
    print_status "INFO" "💡 Please check the client ID and secret"
    print_status "INFO" "💡 Ensure the client '$KEYCLOAK_CLIENT_ID' exists and has the correct secret"
elif [ "$http_code" -eq 400 ]; then
    print_status "ERROR" "❌ Bad request (HTTP 400)"
    print_status "INFO" "💡 Please check if 'Service Account' is enabled for client '$KEYCLOAK_CLIENT_ID'"
    echo "Error details: $response_body"
else
    print_status "ERROR" "❌ Client credentials flow failed (HTTP $http_code)"
    echo "Error details: $response_body"
fi

echo "========================================"
print_status "INFO" "Keycloak Connection Test Complete!"

# Recommendations
echo ""
print_status "INFO" "📋 Troubleshooting Recommendations:"
echo "1. Ensure Keycloak is running on $KEYCLOAK_AUTH_SERVER_URL"
echo "2. Verify realm '$KEYCLOAK_REALM' exists"
echo "3. Check client '$KEYCLOAK_CLIENT_ID' configuration:"
echo "   - Client exists in the realm"
echo "   - Client secret matches: ${KEYCLOAK_CLIENT_SECRET:0:10}..."
echo "   - 'Service Account' is enabled"
echo "   - Client has admin roles assigned (realm-admin, admin)"
echo "4. Check Keycloak logs for detailed error information"
