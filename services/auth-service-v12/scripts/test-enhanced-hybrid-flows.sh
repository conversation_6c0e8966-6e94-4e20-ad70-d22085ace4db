#!/bin/bash

# Test Enhanced Hybrid Authentication Flows
# 1. Complete Keycloak profile updates (not just password)
# 2. Proper handling of inactive/unverified users

# Configuration
BASE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"

# Test users
ACTIVE_USER="918793644824"        # Active user
INACTIVE_USER="918793644823"      # Inactive/unverified user
TEST_PASSWORD="your-actual-password-here"
TEST_OTP="1234"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

test_user_search() {
    local username=$1
    local description=$2
    
    print_status "INFO" "🔍 Testing user search: $description"
    print_status "INFO" "Username: $username"
    
    SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search-user" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$username\"}")

    if [ $? -ne 0 ]; then
        print_status "ERROR" "Failed to connect to server"
        return 1
    fi

    echo "Search Response: $SEARCH_RESPONSE"

    # Extract key information
    SUCCESS=$(echo "$SEARCH_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    MESSAGE=$(echo "$SEARCH_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    SYNC_STATUS=$(echo "$SEARCH_RESPONSE" | grep -o '"sync_status":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    OLD_SSO_EXISTS=$(echo "$SEARCH_RESPONSE" | grep -o '"old_sso_exists":[^,]*' | cut -d':' -f2 | tr -d ' ')
    KEYCLOAK_EXISTS=$(echo "$SEARCH_RESPONSE" | grep -o '"keycloak_exists":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    print_status "INFO" "Results:"
    echo "  - Success: $SUCCESS"
    echo "  - Message: $MESSAGE"
    echo "  - Sync Status: $SYNC_STATUS"
    echo "  - Old SSO Exists: $OLD_SSO_EXISTS"
    echo "  - Keycloak Exists: $KEYCLOAK_EXISTS"
    
    # Check for mobile verification requirements
    NEEDS_MOBILE_VERIFICATION=$(echo "$SEARCH_RESPONSE" | grep -o '"needs_mobile_verification":[^,]*' | cut -d':' -f2 | tr -d ' ')
    VERIFICATION_MESSAGE=$(echo "$SEARCH_RESPONSE" | grep -o '"verification_message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    MOBILE_VERIFIED=$(echo "$SEARCH_RESPONSE" | grep -o '"mobile_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$NEEDS_MOBILE_VERIFICATION" = "true" ]; then
        print_status "WARNING" "📱 Mobile verification required!"
        echo "  - Mobile Verified: $MOBILE_VERIFIED"
        echo "  - Verification Message: $VERIFICATION_MESSAGE"
    fi
    
    echo ""
    return 0
}

test_keycloak_profile_update() {
    local username=$1
    
    print_status "INFO" "🔄 Testing complete Keycloak profile update"
    print_status "INFO" "Username: $username"
    
    if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
        print_status "WARNING" "⚠️  Please provide the actual password for testing"
        return 1
    fi
    
    UPDATE_RESPONSE=$(curl -s -X POST "$BASE_URL/update-keycloak-credentials" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$username\", \"password\": \"$TEST_PASSWORD\"}")

    echo "Profile Update Response: $UPDATE_RESPONSE"

    UPDATE_SUCCESS=$(echo "$UPDATE_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$UPDATE_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Keycloak profile update successful!"
        
        # Check if both profile and password were updated
        PROFILE_SUCCESS=$(echo "$UPDATE_RESPONSE" | grep -o '"profile":{"success":[^}]*' | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
        PASSWORD_SUCCESS=$(echo "$UPDATE_RESPONSE" | grep -o '"password":{"success":[^}]*' | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "INFO" "Update Details:"
        echo "  - Profile Update: $PROFILE_SUCCESS"
        echo "  - Password Update: $PASSWORD_SUCCESS"
        
        KEYCLOAK_USER_ID=$(echo "$UPDATE_RESPONSE" | grep -o '"keycloak_user_id":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        if [ -n "$KEYCLOAK_USER_ID" ] && [ "$KEYCLOAK_USER_ID" != "null" ]; then
            print_status "SUCCESS" "🆔 Keycloak User ID: $KEYCLOAK_USER_ID"
        fi
    else
        UPDATE_MESSAGE=$(echo "$UPDATE_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "WARNING" "⚠️  Profile update result: $UPDATE_MESSAGE"
    fi
    
    echo ""
    return 0
}

test_mobile_otp_verification() {
    local username=$1
    
    print_status "INFO" "📱 Testing mobile OTP verification"
    print_status "INFO" "Username: $username"
    
    if [ "$TEST_OTP" = "1234" ]; then
        print_status "WARNING" "⚠️  Please provide the actual OTP for testing"
        return 1
    fi
    
    OTP_RESPONSE=$(curl -s -X POST "$BASE_URL/verify-mobile-otp" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$username\", \"otp\": \"$TEST_OTP\"}")

    echo "OTP Verification Response: $OTP_RESPONSE"

    OTP_SUCCESS=$(echo "$OTP_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$OTP_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Mobile OTP verification successful!"
        
        USER_ACTIVATED=$(echo "$OTP_RESPONSE" | grep -o '"user_activated":[^,]*' | cut -d':' -f2 | tr -d ' ')
        MOBILE_VERIFIED=$(echo "$OTP_RESPONSE" | grep -o '"mobile_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "SUCCESS" "📱 User Activated: $USER_ACTIVATED"
        print_status "SUCCESS" "✅ Mobile Verified: $MOBILE_VERIFIED"
    else
        OTP_MESSAGE=$(echo "$OTP_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "WARNING" "⚠️  Mobile OTP verification result: $OTP_MESSAGE"
    fi
    
    echo ""
    return 0
}

print_status "INFO" "🧪 Testing Enhanced Hybrid Authentication Flows"
echo "========================================"

# Test 1: Active user search
test_user_search "$ACTIVE_USER" "Active User"

# Test 2: Inactive user search
test_user_search "$INACTIVE_USER" "Inactive/Unverified User"

# Test 3: Complete Keycloak profile update
test_keycloak_profile_update "$ACTIVE_USER"

# Test 4: Mobile OTP verification for inactive user
test_mobile_otp_verification "$INACTIVE_USER"

echo "========================================"
print_status "INFO" "📋 Enhanced Features Summary:"
echo ""
print_status "INFO" "✅ Improvements Made:"
echo "  1. 🔄 Complete Keycloak Profile Updates - Updates name, email, and password"
echo "  2. 📱 Inactive User Detection - Proper handling of unverified users"
echo "  3. 💬 Clear Messaging - Tells users exactly what they need to do"
echo "  4. 🎯 Targeted Recommendations - Specific actions based on user status"
echo ""
print_status "INFO" "🔍 User Status Detection:"
echo "  - 'synced' - User active in both systems"
echo "  - 'will_sync_on_login' - User will be synced during login"
echo "  - 'keycloak_only' - User only in Keycloak"
echo "  - 'requires_mobile_verification' - User needs mobile OTP verification"
echo ""
print_status "INFO" "📱 Mobile Verification Flow:"
echo "  - Detects inactive users with clear error messages"
echo "  - Provides verification instructions"
echo "  - Shows current verification status"
echo ""
print_status "INFO" "Test Complete!"
