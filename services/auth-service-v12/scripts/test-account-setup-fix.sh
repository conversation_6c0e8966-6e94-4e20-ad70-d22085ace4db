#!/bin/bash

# Test Account Setup Fix for "Account is not fully set up" Error
# Verifies the handling and automatic fixing of incomplete Keycloak accounts

# Configuration
BASE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"
KEYCLOAK_AUTH_SERVER_URL="https://devsso.cubeone.in"
KEYCLOAK_REALM="futurescape"
KEYCLOAK_CLIENT_ID="sso_migration"
KEYCLOAK_CLIENT_SECRET="OmbOXL0E7I9HGyjvNb2UmyVkRVOHE8ag"

# Test user
TEST_USERNAME="************"
TEST_PASSWORD="your-actual-password-here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

get_keycloak_admin_token() {
    curl -s -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=client_credentials" \
        -d "client_id=$KEYCLOAK_CLIENT_ID" \
        -d "client_secret=$KEYCLOAK_CLIENT_SECRET" \
        "$KEYCLOAK_AUTH_SERVER_URL/realms/$KEYCLOAK_REALM/protocol/openid-connect/token" | \
        grep -o '"access_token":"[^"]*"' | cut -d'"' -f4
}

get_keycloak_user_details() {
    local username=$1
    local token=$2
    
    curl -s -H "Authorization: Bearer $token" \
        "$KEYCLOAK_AUTH_SERVER_URL/admin/realms/$KEYCLOAK_REALM/users?username=$username"
}

test_direct_keycloak_auth() {
    local username=$1
    local password=$2
    
    print_status "INFO" "🔐 Testing direct Keycloak authentication..."
    
    KEYCLOAK_AUTH_RESPONSE=$(curl -s -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=password" \
        -d "client_id=$KEYCLOAK_CLIENT_ID" \
        -d "client_secret=$KEYCLOAK_CLIENT_SECRET" \
        -d "username=$username" \
        -d "password=$password" \
        "$KEYCLOAK_AUTH_SERVER_URL/realms/$KEYCLOAK_REALM/protocol/openid-connect/token")

    echo "Direct Keycloak Auth Response: $KEYCLOAK_AUTH_RESPONSE"

    # Check for specific errors
    ERROR_TYPE=$(echo "$KEYCLOAK_AUTH_RESPONSE" | grep -o '"error":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    ERROR_DESCRIPTION=$(echo "$KEYCLOAK_AUTH_RESPONSE" | grep -o '"error_description":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    if [ "$ERROR_TYPE" = "invalid_grant" ] && [[ "$ERROR_DESCRIPTION" == *"Account is not fully set up"* ]]; then
        print_status "ERROR" "❌ Keycloak account setup incomplete!"
        print_status "INFO" "Error: $ERROR_DESCRIPTION"
        return 1
    elif [ -n "$ERROR_TYPE" ]; then
        print_status "WARNING" "⚠️  Keycloak authentication failed: $ERROR_TYPE - $ERROR_DESCRIPTION"
        return 1
    else
        print_status "SUCCESS" "✅ Direct Keycloak authentication successful!"
        return 0
    fi
}

test_account_setup_fix() {
    print_status "INFO" "🔧 Testing Account Setup Fix for 'Account is not fully set up' Error"
    print_status "INFO" "Username: $TEST_USERNAME"
    
    if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
        print_status "WARNING" "⚠️  Please provide the actual password for testing"
        return 1
    fi

    # Step 1: Check current Keycloak user status
    print_status "INFO" "1️⃣ Checking current Keycloak user status..."
    ADMIN_TOKEN=$(get_keycloak_admin_token)
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_status "ERROR" "Failed to get Keycloak admin token"
        return 1
    fi

    KEYCLOAK_USER=$(get_keycloak_user_details "$TEST_USERNAME" "$ADMIN_TOKEN")
    
    if [ "$KEYCLOAK_USER" = "[]" ]; then
        print_status "INFO" "User does not exist in Keycloak"
    else
        EMAIL_VERIFIED=$(echo "$KEYCLOAK_USER" | grep -o '"emailVerified":[^,]*' | cut -d':' -f2 | tr -d ' ')
        ENABLED=$(echo "$KEYCLOAK_USER" | grep -o '"enabled":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "INFO" "Current Keycloak User Status:"
        echo "  - Email Verified: $EMAIL_VERIFIED"
        echo "  - Enabled: $ENABLED"
    fi

    # Step 2: Test direct Keycloak authentication to see if account setup is incomplete
    print_status "INFO" "2️⃣ Testing direct Keycloak authentication..."
    test_direct_keycloak_auth "$TEST_USERNAME" "$TEST_PASSWORD"
    DIRECT_AUTH_RESULT=$?

    # Step 3: Test hybrid login (should handle account setup automatically)
    print_status "INFO" "3️⃣ Testing hybrid login with automatic account setup fix..."
    
    START_TIME=$(date +%s.%N)
    
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")

    END_TIME=$(date +%s.%N)
    RESPONSE_TIME=$(echo "$END_TIME - $START_TIME" | bc)

    echo "Hybrid Login Response: $LOGIN_RESPONSE"
    print_status "INFO" "⏱️  Response Time: ${RESPONSE_TIME}s"

    # Extract login results
    LOGIN_SUCCESS=$(echo "$LOGIN_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    KEYCLOAK_SYNCED=$(echo "$LOGIN_RESPONSE" | grep -o '"keycloak_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    print_status "INFO" "Hybrid Login Results:"
    echo "  - Login Success: $LOGIN_SUCCESS"
    echo "  - Keycloak Synced: $KEYCLOAK_SYNCED"

    if [ "$LOGIN_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Hybrid login successful!"
        
        if [ "$DIRECT_AUTH_RESULT" -eq 1 ]; then
            print_status "SUCCESS" "🔧 Account setup issue was automatically fixed!"
            
            # Step 4: Verify direct Keycloak auth now works
            print_status "INFO" "4️⃣ Verifying direct Keycloak authentication now works..."
            test_direct_keycloak_auth "$TEST_USERNAME" "$TEST_PASSWORD"
            FIXED_AUTH_RESULT=$?
            
            if [ "$FIXED_AUTH_RESULT" -eq 0 ]; then
                print_status "SUCCESS" "🎉 Account setup fix successful - direct Keycloak auth now works!"
            else
                print_status "WARNING" "⚠️  Direct Keycloak auth still has issues after fix attempt"
            fi
        else
            print_status "SUCCESS" "✅ Account was already properly set up"
        fi
        
    else
        LOGIN_MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Hybrid login failed: $LOGIN_MESSAGE"
    fi
    
    echo ""
    return 0
}

print_status "INFO" "🧪 Testing Account Setup Fix for Keycloak"
echo "========================================"

test_account_setup_fix

echo "========================================"
print_status "INFO" "📋 Account Setup Fix Features:"
echo ""
print_status "INFO" "🔧 Error Detection:"
echo "  - ✅ Detects 'Account is not fully set up' error"
echo "  - 🎯 Identifies specific error type: account_setup_incomplete"
echo "  - 📊 Logs detailed error information for debugging"
echo ""
print_status "INFO" "🛠️  Automatic Fix Process:"
echo "  1. 🔍 Detect account setup incomplete error"
echo "  2. 🔧 Complete account setup automatically"
echo "  3. ✅ Set emailVerified = true"
echo "  4. 🔐 Reset password properly"
echo "  5. 📱 Set user attributes (mobile, verification status)"
echo "  6. 🔄 Retry authentication after fix"
echo ""
print_status "INFO" "🎯 Prevention Measures:"
echo "  - ✅ New users created with emailVerified = true"
echo "  - 🚫 No required actions set during registration"
echo "  - 📱 Proper attributes set from the start"
echo ""
print_status "INFO" "📊 Error Types Handled:"
echo "  - invalid_grant + 'Account is not fully set up'"
echo "  - Missing email verification"
echo "  - Incomplete user profile"
echo "  - Required actions blocking login"
echo ""
print_status "INFO" "Test Complete!"
