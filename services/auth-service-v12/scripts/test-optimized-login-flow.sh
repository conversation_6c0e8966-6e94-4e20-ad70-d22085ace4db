#!/bin/bash

# Test Optimized Login Flow with Timestamp-Based Sync
# Verifies the new optimized authentication flow that reduces API response time

# Configuration
BASE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"
KEYCLOAK_AUTH_SERVER_URL="https://devsso.cubeone.in"
KEYCLOAK_REALM="futurescape"
KEYCLOAK_CLIENT_ID="sso_migration"
KEYCLOAK_CLIENT_SECRET="OmbOXL0E7I9HGyjvNb2UmyVkRVOHE8ag"

# Test user
TEST_USERNAME="918793644824"
TEST_PASSWORD="your-actual-password-here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

get_keycloak_admin_token() {
    curl -s -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=client_credentials" \
        -d "client_id=$KEYCLOAK_CLIENT_ID" \
        -d "client_secret=$KEYCLOAK_CLIENT_SECRET" \
        "$KEYCLOAK_AUTH_SERVER_URL/realms/$KEYCLOAK_REALM/protocol/openid-connect/token" | \
        grep -o '"access_token":"[^"]*"' | cut -d'"' -f4
}

get_keycloak_user_details() {
    local username=$1
    local token=$2
    
    curl -s -H "Authorization: Bearer $token" \
        "$KEYCLOAK_AUTH_SERVER_URL/admin/realms/$KEYCLOAK_REALM/users?username=$username"
}

test_optimized_login_flow() {
    print_status "INFO" "🚀 Testing Optimized Login Flow with Timestamp-Based Sync"
    print_status "INFO" "Username: $TEST_USERNAME"
    
    if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
        print_status "WARNING" "⚠️  Please provide the actual password for testing"
        return 1
    fi

    # Step 1: Get current Keycloak user details for comparison
    print_status "INFO" "1️⃣ Getting current Keycloak user details..."
    ADMIN_TOKEN=$(get_keycloak_admin_token)
    
    if [ -z "$ADMIN_TOKEN" ]; then
        print_status "ERROR" "Failed to get Keycloak admin token"
        return 1
    fi

    KEYCLOAK_BEFORE=$(get_keycloak_user_details "$TEST_USERNAME" "$ADMIN_TOKEN")
    
    # Extract current details
    KEYCLOAK_FIRST_NAME_BEFORE=$(echo "$KEYCLOAK_BEFORE" | grep -o '"firstName":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    KEYCLOAK_LAST_NAME_BEFORE=$(echo "$KEYCLOAK_BEFORE" | grep -o '"lastName":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    KEYCLOAK_EMAIL_BEFORE=$(echo "$KEYCLOAK_BEFORE" | grep -o '"email":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    KEYCLOAK_CREATED_TIMESTAMP=$(echo "$KEYCLOAK_BEFORE" | grep -o '"createdTimestamp":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    print_status "INFO" "Current Keycloak Details:"
    echo "  - First Name: $KEYCLOAK_FIRST_NAME_BEFORE"
    echo "  - Last Name: $KEYCLOAK_LAST_NAME_BEFORE"
    echo "  - Email: $KEYCLOAK_EMAIL_BEFORE"
    echo "  - Created Timestamp: $KEYCLOAK_CREATED_TIMESTAMP"

    # Step 2: Perform optimized login (measure response time)
    print_status "INFO" "2️⃣ Performing optimized login (measuring response time)..."
    
    START_TIME=$(date +%s.%N)
    
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")

    END_TIME=$(date +%s.%N)
    RESPONSE_TIME=$(echo "$END_TIME - $START_TIME" | bc)

    echo "Login Response: $LOGIN_RESPONSE"
    print_status "INFO" "⏱️  Response Time: ${RESPONSE_TIME}s"

    # Extract login results
    LOGIN_SUCCESS=$(echo "$LOGIN_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    KEYCLOAK_SYNCED=$(echo "$LOGIN_RESPONSE" | grep -o '"keycloak_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    # Extract sync information
    DETAILS_VERIFIED=$(echo "$LOGIN_RESPONSE" | grep -o '"details_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
    DETAILS_SYNCED=$(echo "$LOGIN_RESPONSE" | grep -o '"details_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    SYNC_METHOD=$(echo "$LOGIN_RESPONSE" | grep -o '"sync_method":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    SYNC_MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"sync_message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    print_status "INFO" "Login Results:"
    echo "  - Login Success: $LOGIN_SUCCESS"
    echo "  - Keycloak Synced: $KEYCLOAK_SYNCED"
    echo "  - Details Verified: $DETAILS_VERIFIED"
    echo "  - Details Synced: $DETAILS_SYNCED"
    echo "  - Sync Method: $SYNC_METHOD"
    echo "  - Sync Message: $SYNC_MESSAGE"

    if [ "$LOGIN_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Login successful!"
        
        # Evaluate response time
        if (( $(echo "$RESPONSE_TIME < 2.0" | bc -l) )); then
            print_status "SUCCESS" "🚀 Excellent response time: ${RESPONSE_TIME}s (< 2s)"
        elif (( $(echo "$RESPONSE_TIME < 3.0" | bc -l) )); then
            print_status "WARNING" "⚠️  Acceptable response time: ${RESPONSE_TIME}s (< 3s)"
        else
            print_status "ERROR" "❌ Slow response time: ${RESPONSE_TIME}s (> 3s)"
        fi
        
        # Evaluate sync method
        case $SYNC_METHOD in
            "timestamp_comparison")
                print_status "SUCCESS" "🎯 Optimal sync method: No sync needed based on timestamps"
                ;;
            "timestamp_based_update")
                print_status "SUCCESS" "🔄 Efficient sync method: Updated based on timestamp comparison"
                ;;
            "new_registration")
                print_status "SUCCESS" "🆕 New user registration completed"
                ;;
            *)
                print_status "WARNING" "⚠️  Unknown sync method: $SYNC_METHOD"
                ;;
        esac
        
        if [ "$DETAILS_VERIFIED" = "true" ]; then
            print_status "SUCCESS" "✅ User details verification performed!"
            
            if [ "$DETAILS_SYNCED" = "true" ]; then
                print_status "SUCCESS" "🔄 User details were synchronized!"
                
                # Extract updated fields
                UPDATED_FIELDS=$(echo "$LOGIN_RESPONSE" | grep -o '"updated_fields":\[[^]]*\]' | sed 's/"updated_fields":\[//g' | sed 's/\]//g' | tr -d '"')
                print_status "INFO" "Updated Fields: $UPDATED_FIELDS"
                
                # Check if mobile verification was updated
                if [[ "$UPDATED_FIELDS" == *"mobile_verified"* ]]; then
                    print_status "SUCCESS" "📱 Mobile verification status updated in Keycloak!"
                fi
                
            else
                print_status "SUCCESS" "✅ User details were already in sync!"
            fi
        else
            print_status "WARNING" "⚠️  User details verification was not performed"
        fi
    else
        LOGIN_MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Login failed: $LOGIN_MESSAGE"
    fi
    
    echo ""
    return 0
}

print_status "INFO" "🧪 Testing Optimized Login Flow"
echo "========================================"

test_optimized_login_flow

echo "========================================"
print_status "INFO" "📋 Optimization Features:"
echo ""
print_status "INFO" "🚀 Performance Improvements:"
echo "  1. ⏱️  Timestamp-based sync decisions (faster than field comparison)"
echo "  2. 🎯 Skip unnecessary API calls when data is up-to-date"
echo "  3. 📱 Complete profile sync including mobile verification"
echo "  4. 🔄 Single-pass authentication with intelligent sync"
echo ""
print_status "INFO" "🔍 Optimized Flow Steps:"
echo "  1. ✅ Old SSO Authentication (primary source)"
echo "  2. 🔍 Check Keycloak user existence"
echo "  3. ⏱️  Compare timestamps (Old SSO vs Keycloak)"
echo "  4. 🔄 Sync only if Old SSO data is newer"
echo "  5. 🎯 Authenticate with Keycloak"
echo ""
print_status "INFO" "📊 Sync Methods:"
echo "  - timestamp_comparison: No sync needed"
echo "  - timestamp_based_update: Efficient targeted update"
echo "  - new_registration: First-time user registration"
echo ""
print_status "INFO" "📱 Complete Data Sync:"
echo "  - ✅ Basic profile (name, email)"
echo "  - 🔐 Password synchronization"
echo "  - 📱 Mobile verification status"
echo "  - ✉️  Email verification status"
echo ""
print_status "INFO" "🎯 Target Response Time: < 2 seconds"
print_status "INFO" "Test Complete!"
