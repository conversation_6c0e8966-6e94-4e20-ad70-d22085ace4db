#!/bin/bash

# Test Inactive User Handling with SOLID Principles
# Tests both search and login scenarios for inactive users

# Configuration
BASE_URL="http://***********:8000/api/v2/hybrid-auth"

# Test users
INACTIVE_USER="918793644823"      # Inactive/unverified user
TEST_PASSWORD="your-actual-password-here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

test_inactive_user_search() {
    print_status "INFO" "🔍 Testing inactive user search (Status Code 1010 - 'User is not active/verified')"
    print_status "INFO" "Username: $INACTIVE_USER"
    
    SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search-user" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$INACTIVE_USER\"}")

    if [ $? -ne 0 ]; then
        print_status "ERROR" "Failed to connect to server"
        return 1
    fi

    echo "Search Response: $SEARCH_RESPONSE"

    # Extract key information
    SUCCESS=$(echo "$SEARCH_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    MESSAGE=$(echo "$SEARCH_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    SYNC_STATUS=$(echo "$SEARCH_RESPONSE" | grep -o '"sync_status":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    NEEDS_MOBILE_VERIFICATION=$(echo "$SEARCH_RESPONSE" | grep -o '"needs_mobile_verification":[^,]*' | cut -d':' -f2 | tr -d ' ')
    VERIFICATION_MESSAGE=$(echo "$SEARCH_RESPONSE" | grep -o '"verification_message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    print_status "INFO" "Search Results:"
    echo "  - Success: $SUCCESS"
    echo "  - Message: $MESSAGE"
    echo "  - Sync Status: $SYNC_STATUS"
    echo "  - Needs Mobile Verification: $NEEDS_MOBILE_VERIFICATION"
    echo "  - Verification Message: $VERIFICATION_MESSAGE"
    
    # Verify expected behavior
    if [ "$SYNC_STATUS" = "requires_mobile_verification" ] && [ "$NEEDS_MOBILE_VERIFICATION" = "true" ]; then
        print_status "SUCCESS" "✅ Inactive user properly detected in search!"
    else
        print_status "ERROR" "❌ Inactive user not properly handled in search"
    fi
    
    echo ""
    return 0
}

test_inactive_user_login() {
    print_status "INFO" "🔐 Testing inactive user login (Status Code 1010 - 'User is not active to login.')"
    print_status "INFO" "Username: $INACTIVE_USER"
    
    if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
        print_status "WARNING" "⚠️  Please provide the actual password for testing"
        return 1
    fi
    
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$INACTIVE_USER\", \"password\": \"$TEST_PASSWORD\"}")

    echo "Login Response: $LOGIN_RESPONSE"

    # Extract key information
    SUCCESS=$(echo "$LOGIN_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    AUTH_TYPE=$(echo "$LOGIN_RESPONSE" | grep -o '"auth_type":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    SYNC_STATUS=$(echo "$LOGIN_RESPONSE" | grep -o '"sync_status":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    LOGIN_BLOCKED_REASON=$(echo "$LOGIN_RESPONSE" | grep -o '"login_blocked_reason":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    print_status "INFO" "Login Results:"
    echo "  - Success: $SUCCESS"
    echo "  - Message: $MESSAGE"
    echo "  - Auth Type: $AUTH_TYPE"
    echo "  - Sync Status: $SYNC_STATUS"
    echo "  - Login Blocked Reason: $LOGIN_BLOCKED_REASON"
    
    # Verify expected behavior
    if [ "$SUCCESS" = "false" ] && [ "$AUTH_TYPE" = "blocked_inactive" ] && [ "$SYNC_STATUS" = "requires_mobile_verification" ]; then
        print_status "SUCCESS" "✅ Inactive user login properly blocked!"
    else
        print_status "ERROR" "❌ Inactive user login not properly handled"
    fi
    
    echo ""
    return 0
}

print_status "INFO" "🧪 Testing Inactive User Handling with SOLID Principles"
echo "========================================"

# Test 1: Inactive user search
test_inactive_user_search

# Test 2: Inactive user login
test_inactive_user_login

echo "========================================"
print_status "INFO" "📋 SOLID Principles Applied:"
echo ""
print_status "INFO" "✅ Single Responsibility Principle:"
echo "  - checkForInactiveUser() - Only detects inactive users"
echo "  - generateInactiveUserResponse() - Only generates responses"
echo ""
print_status "INFO" "✅ DRY Principle (Don't Repeat Yourself):"
echo "  - Inactive user detection logic centralized"
echo "  - Response generation reused for search/login"
echo ""
print_status "INFO" "✅ Open/Closed Principle:"
echo "  - Easy to extend for new inactive user types"
echo "  - Can add new error codes without modifying existing logic"
echo ""
print_status "INFO" "🔍 Status Code Handling:"
echo "  - Search: 1010 + 'User is not active/verified'"
echo "  - Login: 1010 + 'User is not active to login.'"
echo ""
print_status "INFO" "📱 Response Differences:"
echo "  - Search: 'User found but requires mobile verification'"
echo "  - Login: 'Login failed: User requires mobile verification'"
echo ""
print_status "INFO" "Test Complete!"
