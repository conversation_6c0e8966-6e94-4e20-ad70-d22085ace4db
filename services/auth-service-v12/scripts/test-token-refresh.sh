#!/bin/bash

# Test Token Refresh API for Auth Service v12
# Tests the refresh token functionality with Keycloak integration

# Configuration
AUTH_SERVICE_URL="http://192.168.1.7:8000/api/v2/auth"
ADMIN_SERVICE_URL="http://192.168.1.7:8001/api/v2/admin"

# Test credentials
TEST_USERNAME="918793644824"
TEST_PASSWORD="your-password-here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        "TEST") echo -e "${CYAN}[TEST]${NC} $message" ;;
    esac
}

# Step 1: Login to get initial tokens
login_and_get_tokens() {
    print_status "INFO" "🔐 Step 1: Logging in to get initial tokens..."
    
    if [ "$TEST_PASSWORD" = "your-password-here" ]; then
        print_status "WARNING" "⚠️  Please provide actual password for testing"
        return 1
    fi
    
    local login_response=$(curl -s -X POST "$AUTH_SERVICE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")
    
    local login_success=$(echo "$login_response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$login_success" = "true" ]; then
        # Extract tokens
        ACCESS_TOKEN=$(echo "$login_response" | grep -o '"access_token":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        REFRESH_TOKEN=$(echo "$login_response" | grep -o '"refresh_token":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        LARAVEL_TOKEN=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        
        if [ -n "$ACCESS_TOKEN" ] && [ -n "$REFRESH_TOKEN" ]; then
            print_status "SUCCESS" "✅ Login successful - tokens obtained"
            print_status "INFO" "Access Token: ${ACCESS_TOKEN:0:50}..."
            print_status "INFO" "Refresh Token: ${REFRESH_TOKEN:0:50}..."
            print_status "INFO" "Laravel Token: ${LARAVEL_TOKEN:0:50}..."
            return 0
        else
            print_status "ERROR" "❌ Failed to extract tokens from login response"
            return 1
        fi
    else
        local error_message=$(echo "$login_response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Login failed: $error_message"
        return 1
    fi
}

# Step 2: Test token refresh
test_token_refresh() {
    print_status "TEST" "🔄 Step 2: Testing token refresh..."
    
    local refresh_response=$(curl -s -X POST "$AUTH_SERVICE_URL/refresh-token" \
        -H "Content-Type: application/json" \
        -d "{\"refresh_token\": \"$REFRESH_TOKEN\"}")
    
    echo "Refresh Response: $refresh_response"
    
    local refresh_success=$(echo "$refresh_response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$refresh_success" = "true" ]; then
        print_status "SUCCESS" "✅ Token refresh successful"
        
        # Extract new tokens
        NEW_ACCESS_TOKEN=$(echo "$refresh_response" | grep -o '"access_token":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        NEW_REFRESH_TOKEN=$(echo "$refresh_response" | grep -o '"refresh_token":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        NEW_LARAVEL_TOKEN=$(echo "$refresh_response" | grep -o '"token":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        
        print_status "INFO" "New Access Token: ${NEW_ACCESS_TOKEN:0:50}..."
        print_status "INFO" "New Refresh Token: ${NEW_REFRESH_TOKEN:0:50}..."
        print_status "INFO" "New Laravel Token: ${NEW_LARAVEL_TOKEN:0:50}..."
        
        # Update tokens for further testing
        ACCESS_TOKEN="$NEW_ACCESS_TOKEN"
        REFRESH_TOKEN="$NEW_REFRESH_TOKEN"
        LARAVEL_TOKEN="$NEW_LARAVEL_TOKEN"
        
        return 0
    else
        local error_message=$(echo "$refresh_response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Token refresh failed: $error_message"
        return 1
    fi
}

# Step 3: Test new access token with admin service
test_new_token_with_admin_service() {
    print_status "TEST" "🔐 Step 3: Testing new access token with admin service..."
    
    local admin_response=$(curl -s -X GET "$ADMIN_SERVICE_URL/health" \
        -H "Authorization: Bearer $ACCESS_TOKEN")
    
    echo "Admin Service Response: $admin_response"
    
    # Check if we get a valid response (not an error page)
    if echo "$admin_response" | grep -q "success\|healthy\|timestamp"; then
        print_status "SUCCESS" "✅ New access token works with admin service"
        return 0
    else
        print_status "ERROR" "❌ New access token failed with admin service"
        return 1
    fi
}

# Step 4: Test token validation
test_token_validation() {
    print_status "TEST" "✅ Step 4: Testing token validation..."
    
    local validation_response=$(curl -s -X POST "$AUTH_SERVICE_URL/validate-token" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $LARAVEL_TOKEN" \
        -d "{\"token\": \"$ACCESS_TOKEN\"}")
    
    echo "Validation Response: $validation_response"
    
    local validation_success=$(echo "$validation_response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    local is_valid=$(echo "$validation_response" | grep -o '"valid":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$validation_success" = "true" ] && [ "$is_valid" = "true" ]; then
        print_status "SUCCESS" "✅ Token validation successful"
        return 0
    else
        print_status "ERROR" "❌ Token validation failed"
        return 1
    fi
}

# Step 5: Test with invalid refresh token
test_invalid_refresh_token() {
    print_status "TEST" "❌ Step 5: Testing with invalid refresh token..."
    
    local invalid_response=$(curl -s -X POST "$AUTH_SERVICE_URL/refresh-token" \
        -H "Content-Type: application/json" \
        -d "{\"refresh_token\": \"invalid.refresh.token\"}")
    
    echo "Invalid Refresh Response: $invalid_response"
    
    local invalid_success=$(echo "$invalid_response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$invalid_success" = "false" ]; then
        print_status "SUCCESS" "✅ Correctly rejected invalid refresh token"
        return 0
    else
        print_status "ERROR" "❌ Should have rejected invalid refresh token"
        return 1
    fi
}

# Step 6: Test rate limiting
test_rate_limiting() {
    print_status "TEST" "⏱️  Step 6: Testing rate limiting on refresh token endpoint..."
    
    local rate_limit_count=0
    local rate_limit_hit=false
    
    for i in {1..7}; do
        local rate_response=$(curl -s -X POST "$AUTH_SERVICE_URL/refresh-token" \
            -H "Content-Type: application/json" \
            -d "{\"refresh_token\": \"test.token.for.rate.limit\"}")
        
        if echo "$rate_response" | grep -q "Too Many Attempts\|429"; then
            rate_limit_hit=true
            rate_limit_count=$i
            break
        fi
        
        sleep 1
    done
    
    if [ "$rate_limit_hit" = true ]; then
        print_status "SUCCESS" "✅ Rate limiting working - hit limit at attempt $rate_limit_count"
        return 0
    else
        print_status "WARNING" "⚠️  Rate limiting may not be working as expected"
        return 1
    fi
}

# Main test function
run_token_refresh_tests() {
    print_status "INFO" "🧪 Testing Token Refresh API for Auth Service v12"
    echo "=================================================="
    echo ""
    
    # Initialize test results
    local tests_passed=0
    local tests_failed=0
    
    # Run tests
    if login_and_get_tokens; then
        ((tests_passed++))
    else
        ((tests_failed++))
        print_status "ERROR" "❌ Cannot proceed without initial tokens"
        return 1
    fi
    
    echo ""
    
    if test_token_refresh; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    echo ""
    
    if test_new_token_with_admin_service; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    echo ""
    
    if test_token_validation; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    echo ""
    
    if test_invalid_refresh_token; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    echo ""
    
    if test_rate_limiting; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    
    echo ""
    echo "=================================================="
    print_status "INFO" "📊 Test Results Summary"
    echo ""
    print_status "SUCCESS" "✅ Tests Passed: $tests_passed"
    print_status "ERROR" "❌ Tests Failed: $tests_failed"
    echo ""
    
    if [ $tests_failed -eq 0 ]; then
        print_status "SUCCESS" "🎉 All token refresh tests passed!"
    else
        print_status "WARNING" "⚠️  Some tests failed - check implementation"
    fi
    
    echo ""
    print_status "INFO" "🔧 Token Refresh API Features:"
    echo "  - ✅ Refresh expired access tokens"
    echo "  - ✅ Generate new refresh tokens"
    echo "  - ✅ Update Laravel Sanctum tokens"
    echo "  - ✅ Validate token integrity"
    echo "  - ✅ Rate limiting protection"
    echo "  - ✅ Error handling and logging"
    echo ""
    print_status "INFO" "📋 API Endpoint: POST $AUTH_SERVICE_URL/refresh-token"
    print_status "INFO" "📋 Required: refresh_token (string)"
    print_status "INFO" "📋 Returns: new access_token, refresh_token, Laravel token"
    echo ""
    print_status "INFO" "Test Complete!"
}

# Run the tests
run_token_refresh_tests
