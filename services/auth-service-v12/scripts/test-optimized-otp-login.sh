#!/bin/bash

# Test Optimized OTP Login Flow
# Verifies the OTP login with same optimizations as password login

# Configuration
BASE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"

# Test user
TEST_USERNAME="918793644824"
TEST_OTP="123456"  # Replace with actual OTP

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

request_otp() {
    local username=$1
    
    print_status "INFO" "📱 Requesting OTP for user: $username"
    
    OTP_REQUEST_RESPONSE=$(curl -s -X POST "$BASE_URL/request-otp" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$username\"}")

    echo "OTP Request Response: $OTP_REQUEST_RESPONSE"

    # Extract OTP request results
    OTP_REQUEST_SUCCESS=$(echo "$OTP_REQUEST_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$OTP_REQUEST_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ OTP request successful!"
        return 0
    else
        OTP_REQUEST_MESSAGE=$(echo "$OTP_REQUEST_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ OTP request failed: $OTP_REQUEST_MESSAGE"
        return 1
    fi
}

test_optimized_otp_login() {
    print_status "INFO" "🔐 Testing Optimized OTP Login Flow"
    print_status "INFO" "Username: $TEST_USERNAME"
    
    if [ "$TEST_OTP" = "123456" ]; then
        print_status "WARNING" "⚠️  Please provide the actual OTP for testing"
        print_status "INFO" "💡 You can request OTP first using the request_otp function"
    fi

    # Step 1: Request OTP (optional - for demonstration)
    print_status "INFO" "1️⃣ Requesting OTP (optional step)..."
    request_otp "$TEST_USERNAME"
    echo ""

    # Step 2: Test user search to verify Old SSO profile data
    print_status "INFO" "2️⃣ Testing user search to verify Old SSO profile data..."
    
    SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search-user" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\"}")

    echo "Search Response: $SEARCH_RESPONSE"

    # Extract user data from search
    SEARCH_SUCCESS=$(echo "$SEARCH_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$SEARCH_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ User search successful - Old SSO profile available"
        
        # Try to extract updated_at from search response
        UPDATED_AT=$(echo "$SEARCH_RESPONSE" | grep -o '"updated_at":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        MOBILE_VERIFIED=$(echo "$SEARCH_RESPONSE" | grep -o '"mobile_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "INFO" "Old SSO Profile Data:"
        echo "  - Updated At: $UPDATED_AT"
        echo "  - Mobile Verified: $MOBILE_VERIFIED"
    else
        print_status "ERROR" "❌ User search failed"
        return 1
    fi

    echo ""

    # Step 3: Perform optimized OTP login
    print_status "INFO" "3️⃣ Performing optimized OTP login with timestamp comparison..."
    
    START_TIME=$(date +%s.%N)
    
    OTP_LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"login_otp\": \"$TEST_OTP\"}")

    END_TIME=$(date +%s.%N)
    RESPONSE_TIME=$(echo "$END_TIME - $START_TIME" | bc)

    echo "OTP Login Response: $OTP_LOGIN_RESPONSE"
    print_status "INFO" "⏱️  Response Time: ${RESPONSE_TIME}s"

    # Extract login results
    LOGIN_SUCCESS=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    KEYCLOAK_SYNCED=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"keycloak_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    AUTH_TYPE=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"auth_type":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    LOGIN_METHOD=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"login_method":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    FALLBACK_PASSWORD_USED=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"fallback_password_used":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    # Extract sync information
    DETAILS_VERIFIED=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"details_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
    DETAILS_SYNCED=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"details_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    SYNC_METHOD=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"sync_method":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    SYNC_MESSAGE=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"sync_message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    print_status "INFO" "OTP Login Results:"
    echo "  - Login Success: $LOGIN_SUCCESS"
    echo "  - Auth Type: $AUTH_TYPE"
    echo "  - Login Method: $LOGIN_METHOD"
    echo "  - Fallback Password Used: $FALLBACK_PASSWORD_USED"
    echo "  - Keycloak Synced: $KEYCLOAK_SYNCED"
    echo "  - Details Verified: $DETAILS_VERIFIED"
    echo "  - Details Synced: $DETAILS_SYNCED"
    echo "  - Sync Method: $SYNC_METHOD"
    echo "  - Sync Message: $SYNC_MESSAGE"

    if [ "$LOGIN_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ OTP login successful!"
        
        # Verify OTP-specific features
        if [ "$AUTH_TYPE" = "hybrid_otp" ]; then
            print_status "SUCCESS" "🔐 Correct auth type: hybrid_otp"
        else
            print_status "WARNING" "⚠️  Unexpected auth type: $AUTH_TYPE"
        fi
        
        if [ "$FALLBACK_PASSWORD_USED" = "true" ]; then
            print_status "SUCCESS" "🔑 Fallback password used for Keycloak sync"
        fi
        
        if [ "$LOGIN_METHOD" = "otp_with_fallback_password" ]; then
            print_status "SUCCESS" "📱 Correct login method identified"
        fi
        
        # Evaluate response time
        if (( $(echo "$RESPONSE_TIME < 2.0" | bc -l) )); then
            print_status "SUCCESS" "🚀 Excellent response time: ${RESPONSE_TIME}s (< 2s)"
        elif (( $(echo "$RESPONSE_TIME < 3.0" | bc -l) )); then
            print_status "WARNING" "⚠️  Acceptable response time: ${RESPONSE_TIME}s (< 3s)"
        else
            print_status "ERROR" "❌ Slow response time: ${RESPONSE_TIME}s (> 3s)"
        fi
        
        # Evaluate sync method
        case $SYNC_METHOD in
            "timestamp_comparison")
                print_status "SUCCESS" "🎯 Optimal sync method: No sync needed based on timestamps"
                ;;
            "timestamp_based_update")
                print_status "SUCCESS" "🔄 Efficient sync method: Updated based on timestamp comparison"
                ;;
            "new_registration")
                print_status "SUCCESS" "🆕 New user registration completed"
                ;;
            *)
                print_status "WARNING" "⚠️  Unknown sync method: $SYNC_METHOD"
                ;;
        esac
        
        if [ "$DETAILS_VERIFIED" = "true" ]; then
            print_status "SUCCESS" "✅ User details verification performed!"
            
            if [ "$DETAILS_SYNCED" = "true" ]; then
                print_status "SUCCESS" "🔄 User details were synchronized!"
                
                # Extract updated fields
                UPDATED_FIELDS=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"updated_fields":\[[^]]*\]' | sed 's/"updated_fields":\[//g' | sed 's/\]//g' | tr -d '"')
                print_status "INFO" "Updated Fields: $UPDATED_FIELDS"
                
                # Check if mobile verification was updated
                if [[ "$UPDATED_FIELDS" == *"mobile_verified"* ]]; then
                    print_status "SUCCESS" "📱 Mobile verification status updated in Keycloak!"
                fi
                
            else
                print_status "SUCCESS" "✅ User details were already in sync (within threshold)!"
            fi
        else
            print_status "WARNING" "⚠️  User details verification was not performed"
        fi
    else
        LOGIN_MESSAGE=$(echo "$OTP_LOGIN_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ OTP login failed: $LOGIN_MESSAGE"
        
        # Check for specific error types
        if [[ "$AUTH_TYPE" == *"blocked_inactive"* ]]; then
            print_status "INFO" "🚫 User is inactive/unverified - this is expected behavior"
        fi
    fi
    
    echo ""
    return 0
}

print_status "INFO" "🧪 Testing Optimized OTP Login Flow"
echo "========================================"

test_optimized_otp_login

echo "========================================"
print_status "INFO" "📋 OTP Login Optimizations Applied:"
echo ""
print_status "INFO" "🔐 OTP-Specific Features:"
echo "  1. 📱 OTP authentication with Old SSO"
echo "  2. 🔑 Fallback password generation for Keycloak sync"
echo "  3. 🎯 Same timestamp-based sync decisions as password login"
echo "  4. 🚫 Inactive user detection and proper error handling"
echo "  5. 📊 Complete profile fetching for timestamp comparison"
echo ""
print_status "INFO" "⏱️  Performance Optimizations:"
echo "  - ✅ Timestamp-based sync decisions (faster than field comparison)"
echo "  - 🎯 Skip unnecessary API calls when data is up-to-date"
echo "  - 📱 Complete profile sync including mobile verification"
echo "  - 🔄 Single-pass authentication with intelligent sync"
echo ""
print_status "INFO" "🔧 Error Handling:"
echo "  - 🚫 Inactive user detection (blocked_inactive_otp)"
echo "  - 🔧 Account setup completion for incomplete Keycloak accounts"
echo "  - 📝 Detailed logging for debugging"
echo "  - 🎯 Specific error responses for different scenarios"
echo ""
print_status "INFO" "📊 Response Data:"
echo "  - 🔐 auth_type: hybrid_otp"
echo "  - 📱 login_method: otp_with_fallback_password"
echo "  - 🔑 fallback_password_used: true"
echo "  - 🔄 user_details_sync: Complete sync information"
echo "  - ⏱️  Same timestamp comparison logic as password login"
echo ""
print_status "INFO" "🎯 Target Response Time: < 2 seconds"
print_status "INFO" "Test Complete!"
