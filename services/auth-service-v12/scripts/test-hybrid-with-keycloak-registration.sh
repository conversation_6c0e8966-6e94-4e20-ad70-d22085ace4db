#!/bin/bash

# Test Hybrid Authentication with Actual Keycloak Registration
# This script tests the complete flow including actual Keycloak registration

# Configuration
BASE_URL="http://***********:8000/api/v2/hybrid-auth"
KEYCLOAK_AUTH_SERVER_URL="https://devsso.cubeone.in"
KEYCLOAK_REALM="futurescape"
KEYCLOAK_CLIENT_ID="sso_migration"
KEYCLOAK_CLIENT_SECRET="OmbOXL0E7I9HGyjvNb2UmyVkRVOHE8ag"

# Test user (known to exist in Old SSO but not in Keycloak)
TEST_USERNAME="918793644824"
TEST_PASSWORD="your-actual-password-here"  # You'll need to provide the real password

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

get_keycloak_admin_token() {
    curl -s -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=client_credentials" \
        -d "client_id=$KEYCLOAK_CLIENT_ID" \
        -d "client_secret=$KEYCLOAK_CLIENT_SECRET" \
        "$KEYCLOAK_AUTH_SERVER_URL/realms/$KEYCLOAK_REALM/protocol/openid-connect/token" | \
        grep -o '"access_token":"[^"]*"' | cut -d'"' -f4
}

check_user_in_keycloak() {
    local username=$1
    local token=$2
    
    local result=$(curl -s -H "Authorization: Bearer $token" \
        "$KEYCLOAK_AUTH_SERVER_URL/admin/realms/$KEYCLOAK_REALM/users?username=$username")
    
    if [ "$result" = "[]" ]; then
        echo "false"
    else
        echo "true"
    fi
}

get_keycloak_user_id() {
    local username=$1
    local token=$2
    
    curl -s -H "Authorization: Bearer $token" \
        "$KEYCLOAK_AUTH_SERVER_URL/admin/realms/$KEYCLOAK_REALM/users?username=$username" | \
        grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4
}

print_status "INFO" "🧪 Testing Hybrid Authentication with Actual Keycloak Registration"
echo "Test Username: $TEST_USERNAME"
echo "========================================"

# Step 1: Get Keycloak admin token
print_status "INFO" "1️⃣ Getting Keycloak admin token..."
ADMIN_TOKEN=$(get_keycloak_admin_token)

if [ -z "$ADMIN_TOKEN" ]; then
    print_status "ERROR" "Failed to get Keycloak admin token"
    exit 1
fi

print_status "SUCCESS" "Admin token obtained"

# Step 2: Check initial state in Keycloak
print_status "INFO" "2️⃣ Checking initial state in Keycloak..."
INITIAL_EXISTS=$(check_user_in_keycloak "$TEST_USERNAME" "$ADMIN_TOKEN")

if [ "$INITIAL_EXISTS" = "true" ]; then
    print_status "WARNING" "User already exists in Keycloak - test may not show registration"
    INITIAL_USER_ID=$(get_keycloak_user_id "$TEST_USERNAME" "$ADMIN_TOKEN")
    print_status "INFO" "Existing Keycloak User ID: $INITIAL_USER_ID"
else
    print_status "INFO" "✅ User does not exist in Keycloak (good for testing registration)"
fi

# Step 3: Test user search
print_status "INFO" "3️⃣ Testing user search..."
SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search-user" \
    -H "Content-Type: application/json" \
    -d "{\"username\": \"$TEST_USERNAME\"}")

echo "Search Response: $SEARCH_RESPONSE"

KEYCLOAK_EXISTS=$(echo "$SEARCH_RESPONSE" | grep -o '"keycloak_exists":[^,]*' | cut -d':' -f2 | tr -d ' ')
SYNC_STATUS=$(echo "$SEARCH_RESPONSE" | grep -o '"sync_status":"[^"]*"' | cut -d':' -f2 | tr -d '"')

print_status "INFO" "Search Results:"
echo "  - Keycloak Exists: $KEYCLOAK_EXISTS"
echo "  - Sync Status: $SYNC_STATUS"

# Step 4: Test login (this should trigger auto-registration)
print_status "INFO" "4️⃣ Testing login (should trigger auto-registration)..."

if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
    print_status "WARNING" "⚠️  Please provide the actual password for user $TEST_USERNAME"
    print_status "INFO" "Skipping login test - password not provided"
else
    print_status "INFO" "Attempting login with password..."
    
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")

    echo "Login Response: $LOGIN_RESPONSE"

    LOGIN_SUCCESS=$(echo "$LOGIN_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$LOGIN_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Login successful!"
        
        # Step 5: Check if user was actually registered in Keycloak
        print_status "INFO" "5️⃣ Verifying Keycloak registration..."
        
        # Get fresh admin token
        ADMIN_TOKEN=$(get_keycloak_admin_token)
        FINAL_EXISTS=$(check_user_in_keycloak "$TEST_USERNAME" "$ADMIN_TOKEN")
        
        if [ "$FINAL_EXISTS" = "true" ]; then
            FINAL_USER_ID=$(get_keycloak_user_id "$TEST_USERNAME" "$ADMIN_TOKEN")
            print_status "SUCCESS" "🎉 SUCCESS! User now exists in Keycloak"
            print_status "SUCCESS" "🆔 Keycloak User ID: $FINAL_USER_ID"
            
            if [ "$INITIAL_EXISTS" = "false" ]; then
                print_status "SUCCESS" "✅ Auto-registration worked! User was created during login"
            else
                print_status "INFO" "User already existed, but login was successful"
            fi
        else
            print_status "ERROR" "❌ FAILURE! User still does not exist in Keycloak after login"
            print_status "ERROR" "🚨 AUTO-REGISTRATION DID NOT WORK!"
        fi
    else
        print_status "ERROR" "❌ Login failed"
        LOGIN_MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "Error: $LOGIN_MESSAGE"
    fi
fi

# Step 6: Final verification
print_status "INFO" "6️⃣ Final verification..."
FINAL_SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search-user" \
    -H "Content-Type: application/json" \
    -d "{\"username\": \"$TEST_USERNAME\"}")

FINAL_KEYCLOAK_EXISTS=$(echo "$FINAL_SEARCH_RESPONSE" | grep -o '"keycloak_exists":[^,]*' | cut -d':' -f2 | tr -d ' ')
FINAL_SYNC_STATUS=$(echo "$FINAL_SEARCH_RESPONSE" | grep -o '"sync_status":"[^"]*"' | cut -d':' -f2 | tr -d '"')

print_status "INFO" "Final Search Results:"
echo "  - Keycloak Exists: $FINAL_KEYCLOAK_EXISTS"
echo "  - Sync Status: $FINAL_SYNC_STATUS"

echo "========================================"
print_status "INFO" "Test Complete!"

if [ "$FINAL_KEYCLOAK_EXISTS" = "true" ] && [ "$FINAL_SYNC_STATUS" = "synced" ]; then
    print_status "SUCCESS" "🎉 ALL TESTS PASSED! Hybrid authentication with Keycloak registration is working!"
else
    print_status "ERROR" "❌ TESTS FAILED! There are issues with the implementation."
fi
