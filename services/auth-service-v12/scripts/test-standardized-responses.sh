#!/bin/bash

# Test Standardized API Response Format
# Verifies that all endpoints return consistent response structure

# Configuration
BASE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"

# Test users
EXISTING_USER="918793644824"
NON_EXISTING_USER="919999999999"
TEST_PASSWORD="your-password-here"
TEST_OTP="123456"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        "TEST") echo -e "${CYAN}[TEST]${NC} $message" ;;
    esac
}

validate_response_format() {
    local response=$1
    local endpoint=$2
    
    # Check if response has required fields
    local has_success=$(echo "$response" | grep -o '"success":[^,]*' | wc -l)
    local has_data=$(echo "$response" | grep -o '"data":[^}]*}' | wc -l)
    local has_message=$(echo "$response" | grep -o '"message":"[^"]*"' | wc -l)
    
    if [ "$has_success" -eq 1 ] && [ "$has_data" -ge 1 ] && [ "$has_message" -eq 1 ]; then
        print_status "SUCCESS" "✅ $endpoint: Response format is standardized"
        
        # Extract values
        local success_value=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
        local message_value=$(echo "$response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        
        echo "  - success: $success_value"
        echo "  - message: $message_value"
        echo "  - data: present"
        
        return 0
    else
        print_status "ERROR" "❌ $endpoint: Response format is NOT standardized"
        echo "  - has_success: $has_success (expected: 1)"
        echo "  - has_data: $has_data (expected: ≥1)"
        echo "  - has_message: $has_message (expected: 1)"
        
        return 1
    fi
}

test_user_search_existing() {
    print_status "TEST" "🔍 Testing user search for existing user"
    
    local response=$(curl -s -X POST "$BASE_URL/search-user" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$EXISTING_USER\"}")
    
    echo "Response: $response"
    validate_response_format "$response" "search-user (existing)"
    echo ""
}

test_user_search_non_existing() {
    print_status "TEST" "🔍 Testing user search for non-existing user"
    
    local response=$(curl -s -X POST "$BASE_URL/search-user" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$NON_EXISTING_USER\"}")
    
    echo "Response: $response"
    validate_response_format "$response" "search-user (non-existing)"
    echo ""
}

test_login_success() {
    print_status "TEST" "🔐 Testing successful login"
    
    if [ "$TEST_PASSWORD" = "your-password-here" ]; then
        print_status "WARNING" "⚠️  Skipping login test - please provide actual password"
        return
    fi
    
    local response=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$EXISTING_USER\", \"password\": \"$TEST_PASSWORD\"}")
    
    echo "Response: $response"
    validate_response_format "$response" "login (success)"
    echo ""
}

test_login_failure() {
    print_status "TEST" "🔐 Testing failed login"
    
    local response=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$EXISTING_USER\", \"password\": \"wrong-password\"}")
    
    echo "Response: $response"
    validate_response_format "$response" "login (failure)"
    echo ""
}

test_otp_request() {
    print_status "TEST" "📱 Testing OTP request"
    
    local response=$(curl -s -X POST "$BASE_URL/request-otp" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$EXISTING_USER\"}")
    
    echo "Response: $response"
    validate_response_format "$response" "request-otp"
    echo ""
}

test_validation_error() {
    print_status "TEST" "❌ Testing validation error"
    
    local response=$(curl -s -X POST "$BASE_URL/search-user" \
        -H "Content-Type: application/json" \
        -d "{}")
    
    echo "Response: $response"
    validate_response_format "$response" "validation error"
    echo ""
}

test_invalid_endpoint() {
    print_status "TEST" "🚫 Testing invalid endpoint"
    
    local response=$(curl -s -X POST "$BASE_URL/invalid-endpoint" \
        -H "Content-Type: application/json" \
        -d "{\"test\": \"data\"}")
    
    echo "Response: $response"
    
    # For 404 responses, check if it follows Laravel's default format or our custom format
    local has_message=$(echo "$response" | grep -o '"message"' | wc -l)
    if [ "$has_message" -ge 1 ]; then
        print_status "SUCCESS" "✅ 404 response includes message field"
    else
        print_status "WARNING" "⚠️  404 response may not follow standardized format"
    fi
    echo ""
}

run_comprehensive_test() {
    print_status "INFO" "🧪 Testing Standardized API Response Format"
    echo "========================================"
    echo ""
    
    # Test various endpoints
    test_user_search_existing
    test_user_search_non_existing
    test_login_failure
    test_otp_request
    test_validation_error
    test_invalid_endpoint
    
    # Optional tests that require credentials
    if [ "$TEST_PASSWORD" != "your-password-here" ]; then
        test_login_success
    fi
    
    echo "========================================"
    print_status "INFO" "📋 Standardized Response Format Summary"
    echo ""
    print_status "INFO" "🎯 Expected Format:"
    echo '  {
    "success": boolean,
    "data": object|array,
    "message": string
  }'
    echo ""
    print_status "INFO" "✅ Benefits:"
    echo "  - Consistent structure across all endpoints"
    echo "  - Predictable success/error handling"
    echo "  - Always includes human-readable message"
    echo "  - Data field always present (empty {} if no data)"
    echo "  - Proper HTTP status codes"
    echo ""
    print_status "INFO" "🔧 Implementation:"
    echo "  - Uses ResponseHelper trait methods"
    echo "  - Preserves existing business logic"
    echo "  - Wraps service responses in standard format"
    echo "  - Maintains backward compatibility"
    echo ""
    print_status "INFO" "📊 Response Types:"
    echo "  - successResponse(): 200 OK"
    echo "  - createdResponse(): 201 Created"
    echo "  - errorResponse(): 400 Bad Request"
    echo "  - unauthorizedResponse(): 401 Unauthorized"
    echo "  - forbiddenResponse(): 403 Forbidden"
    echo "  - notFoundResponse(): 404 Not Found"
    echo "  - validationErrorResponse(): 422 Unprocessable Entity"
    echo "  - serverErrorResponse(): 500 Internal Server Error"
    echo ""
    print_status "INFO" "Test Complete!"
}

# Run the comprehensive test
run_comprehensive_test
