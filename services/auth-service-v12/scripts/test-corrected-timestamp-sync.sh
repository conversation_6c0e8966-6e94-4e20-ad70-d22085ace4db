#!/bin/bash

# Test Corrected Timestamp-Based Sync
# Now properly fetches complete Old SSO user profile for timestamp comparison

# Configuration
BASE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"

# Test user
TEST_USERNAME="918793644824"
TEST_PASSWORD="your-actual-password-here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
    esac
}

test_corrected_login_flow() {
    print_status "INFO" "🔧 Testing Corrected Login Flow with Proper Timestamp Comparison"
    print_status "INFO" "Username: $TEST_USERNAME"
    
    if [ "$TEST_PASSWORD" = "your-actual-password-here" ]; then
        print_status "WARNING" "⚠️  Please provide the actual password for testing"
        return 1
    fi

    # Step 1: Test user search to see Old SSO profile data
    print_status "INFO" "1️⃣ Testing user search to verify Old SSO profile data..."
    
    SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search-user" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\"}")

    echo "Search Response: $SEARCH_RESPONSE"

    # Extract user data from search
    SEARCH_SUCCESS=$(echo "$SEARCH_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$SEARCH_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ User search successful - Old SSO profile available"
        
        # Try to extract updated_at from search response
        UPDATED_AT=$(echo "$SEARCH_RESPONSE" | grep -o '"updated_at":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        MOBILE_VERIFIED=$(echo "$SEARCH_RESPONSE" | grep -o '"mobile_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "INFO" "Old SSO Profile Data:"
        echo "  - Updated At: $UPDATED_AT"
        echo "  - Mobile Verified: $MOBILE_VERIFIED"
    else
        print_status "ERROR" "❌ User search failed"
        return 1
    fi

    echo ""

    # Step 2: Perform corrected login with timestamp comparison
    print_status "INFO" "2️⃣ Performing corrected login with proper timestamp comparison..."
    
    START_TIME=$(date +%s.%N)
    
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")

    END_TIME=$(date +%s.%N)
    RESPONSE_TIME=$(echo "$END_TIME - $START_TIME" | bc)

    echo "Login Response: $LOGIN_RESPONSE"
    print_status "INFO" "⏱️  Response Time: ${RESPONSE_TIME}s"

    # Extract login results
    LOGIN_SUCCESS=$(echo "$LOGIN_RESPONSE" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    KEYCLOAK_SYNCED=$(echo "$LOGIN_RESPONSE" | grep -o '"keycloak_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    # Extract sync information
    DETAILS_VERIFIED=$(echo "$LOGIN_RESPONSE" | grep -o '"details_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
    DETAILS_SYNCED=$(echo "$LOGIN_RESPONSE" | grep -o '"details_synced":[^,]*' | cut -d':' -f2 | tr -d ' ')
    SYNC_METHOD=$(echo "$LOGIN_RESPONSE" | grep -o '"sync_method":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    SYNC_MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"sync_message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    print_status "INFO" "Login Results:"
    echo "  - Login Success: $LOGIN_SUCCESS"
    echo "  - Keycloak Synced: $KEYCLOAK_SYNCED"
    echo "  - Details Verified: $DETAILS_VERIFIED"
    echo "  - Details Synced: $DETAILS_SYNCED"
    echo "  - Sync Method: $SYNC_METHOD"
    echo "  - Sync Message: $SYNC_MESSAGE"

    if [ "$LOGIN_SUCCESS" = "true" ]; then
        print_status "SUCCESS" "✅ Login successful!"
        
        # Check if we have complete user data in response
        LOGIN_UPDATED_AT=$(echo "$LOGIN_RESPONSE" | grep -o '"updated_at":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        LOGIN_MOBILE_VERIFIED=$(echo "$LOGIN_RESPONSE" | grep -o '"mobile_verified":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "INFO" "User Data in Login Response:"
        echo "  - Updated At: $LOGIN_UPDATED_AT"
        echo "  - Mobile Verified: $LOGIN_MOBILE_VERIFIED"
        
        if [ -n "$LOGIN_UPDATED_AT" ] && [ "$LOGIN_UPDATED_AT" != "null" ]; then
            print_status "SUCCESS" "✅ Complete user profile with timestamp available in login response!"
        else
            print_status "WARNING" "⚠️  Updated timestamp not found in login response"
        fi
        
        # Evaluate response time
        if (( $(echo "$RESPONSE_TIME < 2.0" | bc -l) )); then
            print_status "SUCCESS" "🚀 Excellent response time: ${RESPONSE_TIME}s (< 2s)"
        elif (( $(echo "$RESPONSE_TIME < 3.0" | bc -l) )); then
            print_status "WARNING" "⚠️  Acceptable response time: ${RESPONSE_TIME}s (< 3s)"
        else
            print_status "ERROR" "❌ Slow response time: ${RESPONSE_TIME}s (> 3s)"
        fi
        
        # Evaluate sync method
        case $SYNC_METHOD in
            "timestamp_comparison")
                print_status "SUCCESS" "🎯 Optimal sync method: No sync needed based on timestamps"
                ;;
            "timestamp_based_update")
                print_status "SUCCESS" "🔄 Efficient sync method: Updated based on timestamp comparison"
                ;;
            "new_registration")
                print_status "SUCCESS" "🆕 New user registration completed"
                ;;
            *)
                print_status "WARNING" "⚠️  Unknown sync method: $SYNC_METHOD"
                ;;
        esac
        
        if [ "$DETAILS_VERIFIED" = "true" ]; then
            print_status "SUCCESS" "✅ User details verification performed!"
            
            if [ "$DETAILS_SYNCED" = "true" ]; then
                print_status "SUCCESS" "🔄 User details were synchronized!"
                
                # Extract updated fields
                UPDATED_FIELDS=$(echo "$LOGIN_RESPONSE" | grep -o '"updated_fields":\[[^]]*\]' | sed 's/"updated_fields":\[//g' | sed 's/\]//g' | tr -d '"')
                print_status "INFO" "Updated Fields: $UPDATED_FIELDS"
                
                # Check if mobile verification was updated
                if [[ "$UPDATED_FIELDS" == *"mobile_verified"* ]]; then
                    print_status "SUCCESS" "📱 Mobile verification status updated in Keycloak!"
                fi
                
            else
                print_status "SUCCESS" "✅ User details were already in sync (within threshold)!"
            fi
        else
            print_status "WARNING" "⚠️  User details verification was not performed"
        fi
    else
        LOGIN_MESSAGE=$(echo "$LOGIN_RESPONSE" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Login failed: $LOGIN_MESSAGE"
    fi
    
    echo ""
    return 0
}

print_status "INFO" "🧪 Testing Corrected Timestamp-Based Sync"
echo "========================================"

test_corrected_login_flow

echo "========================================"
print_status "INFO" "📋 Corrections Made:"
echo ""
print_status "INFO" "🔧 Fixed Issues:"
echo "  1. ✅ Now fetches complete Old SSO user profile during login"
echo "  2. ✅ Proper access to updated_at timestamp for comparison"
echo "  3. ✅ Complete user data available for sync decisions"
echo "  4. ✅ Mobile verification status properly accessible"
echo ""
print_status "INFO" "⏱️  Improved Timestamp Logic:"
echo "  - 🎯 5-minute sync threshold to avoid unnecessary updates"
echo "  - 📊 Detailed logging of timestamp comparison"
echo "  - 🔄 Smart sync decisions based on actual time differences"
echo ""
print_status "INFO" "🚀 Optimized Flow:"
echo "  1. 🔐 Old SSO Authentication"
echo "  2. 📋 Fetch Complete Old SSO Profile (with updated_at)"
echo "  3. ⏱️  Compare Timestamps with 5-minute threshold"
echo "  4. 🔄 Sync only if Old SSO data is significantly newer"
echo "  5. 🎯 Keycloak Authentication"
echo ""
print_status "INFO" "📱 Complete Data Sync:"
echo "  - ✅ Basic profile (name, email)"
echo "  - 🔐 Password synchronization"
echo "  - 📱 Mobile verification status"
echo "  - ✉️  Email verification status"
echo "  - 📞 Mobile number"
echo ""
print_status "INFO" "Test Complete!"
