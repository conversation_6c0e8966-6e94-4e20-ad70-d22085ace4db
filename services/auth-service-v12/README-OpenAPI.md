# OneFoodDialer 2025 - Authentication Service OpenAPI Specification

## Overview

This document describes the comprehensive OpenAPI 3.0.3 specification for the Authentication Service, featuring hybrid authentication with Old SSO and Keycloak integration.

## 📋 API Specification File

**File:** `openapi-auth-service.yaml`

## 🔐 Authentication & Security

### JWT Authentication
- **Protected Endpoints:** Use `jwt.auth` middleware
- **Bearer Token:** JWT tokens in Authorization header
- **Format:** `Authorization: Bearer <jwt_token>`

### Public Endpoints
- **No Authentication Required:** Login, register, forgot password flow, token refresh
- **Rate Limited:** All endpoints have appropriate rate limiting

## 🛣️ API Endpoints Summary

### 🔓 **Public Endpoints (No JWT Required)**

#### Authentication
- `POST /v2/hybrid-auth/login` - User login with password or OTP
- `POST /v2/hybrid-auth/register` - Register new user

#### OTP Management
- `POST /v2/hybrid-auth/request-otp` - Request OTP for login
- `POST /v2/hybrid-auth/verify-mobile-otp` - Verify mobile OTP

#### Forgot Password Flow
- `POST /v2/hybrid-auth/forgot-password/request-otp` - Request OTP for password reset
- `POST /v2/hybrid-auth/forgot-password/verify-otp` - Verify OTP and get auth code
- `POST /v2/hybrid-auth/forgot-password/reset-password` - Reset password with auth code

#### Token Management
- `POST /v2/hybrid-auth/refresh-token` - Refresh access token (uses refresh token)

### 🔒 **Protected Endpoints (JWT Required)**

#### User Management
- `POST /v2/hybrid-auth/search-user` - Search user by username
- `POST /v2/hybrid-auth/update-keycloak-credentials` - Update Keycloak credentials

#### Password Management
- `POST /v2/hybrid-auth/change-password` - Change password with token fallback

## 🎯 Key Features

### Hybrid Authentication System
- **Old SSO Integration:** Primary authentication system
- **Keycloak Integration:** Modern JWT-based authentication
- **Dual Token Response:** Returns both Old SSO and Keycloak tokens
- **Automatic Synchronization:** User data synced between systems

### Advanced Password Management
- **Smart Token Handling:** Automatic refresh on expiration
- **Error Detection:** Distinguishes between password mismatch and token expiration
- **Dual System Updates:** Password changes in both Old SSO and Keycloak

### Comprehensive OTP Support
- **Login OTP:** 6-digit OTP for authentication
- **Forgot Password OTP:** 4-digit OTP for password reset
- **Mobile Verification:** SMS-based OTP delivery

## 📊 Response Schemas

### Login Response
```json
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "user": { /* User profile */ },
    "tokens": { /* Keycloak JWT tokens */ },
    "old_sso_tokens": { /* Old SSO tokens */ },
    "token_type": "Bearer",
    "auth_type": "hybrid",
    "keycloak_synced": true,
    "expires_in": 3600
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error message",
  "data": {
    "errors": {
      "field": ["Validation error message"]
    }
  }
}
```

## 🔧 Rate Limiting

| Endpoint Type | Rate Limit |
|---------------|------------|
| Login/Register | 3-5 requests/minute |
| OTP Requests | 5 requests/minute |
| Token Refresh | 10 requests/minute |
| Password Change | 10 requests/minute |
| User Search | 10 requests/minute |

## 🏗️ Implementation Details

### Route Configuration
```php
// Public routes (no JWT)
Route::prefix('hybrid-auth')->group(function () {
    Route::post('login', [HybridAuthController::class, 'login']);
    Route::post('register', [HybridAuthController::class, 'register']);
    // ... other public routes
    
    // JWT Protected routes
    Route::middleware(['jwt.auth'])->group(function () {
        Route::post('search-user', [HybridAuthController::class, 'searchUser']);
        Route::post('change-password', [HybridAuthController::class, 'changePassword']);
        // ... other protected routes
    });
});
```

### Security Middleware
- **jwt.auth:** JWT authentication middleware for protected routes
- **throttle:** Rate limiting middleware on all routes
- **CORS:** Cross-origin resource sharing support

## 🧪 Testing

### Swagger UI
Access the interactive API documentation at:
- **Local:** `http://192.168.1.34:8000/api-docs`
- **Production:** `http://43.205.191.243:8000/api-docs`

### Example Requests

#### Login Request
```bash
curl -X POST "http://192.168.1.34:8000/api/v2/hybrid-auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "918793644824",
    "password": "MySecurePassword123!"
  }'
```

#### Protected Request (with JWT)
```bash
curl -X POST "http://192.168.1.34:8000/api/v2/hybrid-auth/search-user" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <jwt_token>" \
  -d '{
    "username": "918793644824"
  }'
```

## 📈 Monitoring & Logging

### Request Logging
- **Authentication attempts:** Success/failure tracking
- **Token operations:** Refresh and expiration events
- **Password changes:** Security audit trail
- **OTP requests:** Delivery and verification tracking

### Error Tracking
- **Validation errors:** Input validation failures
- **Authentication failures:** Invalid credentials
- **System errors:** Integration failures
- **Rate limiting:** Exceeded request limits

## 🔄 Integration

### Frontend Integration
1. **Login Flow:** Use login endpoint to get tokens
2. **Token Storage:** Store both JWT and Old SSO tokens
3. **API Calls:** Use JWT for protected endpoints
4. **Token Refresh:** Automatic refresh on expiration

### Backend Integration
1. **JWT Middleware:** Validate JWT tokens on protected routes
2. **Old SSO Client:** Handle Old SSO API communication
3. **Keycloak Client:** Manage Keycloak integration
4. **Database Sync:** Maintain user data consistency

## 📚 Documentation

- **OpenAPI Spec:** Complete API specification in YAML format
- **Postman Collection:** Available for API testing
- **Integration Guide:** Step-by-step integration instructions
- **Error Codes:** Comprehensive error code documentation

## 🚀 Deployment

### Environment Configuration
- **Database:** Old SSO and Keycloak connections
- **JWT Settings:** Secret keys and expiration times
- **Rate Limiting:** Request limits per endpoint
- **CORS Settings:** Allowed origins and headers

### Health Checks
- **API Status:** Endpoint availability monitoring
- **Database Connectivity:** Old SSO and Keycloak health
- **Token Validation:** JWT verification status
- **Rate Limiting:** Current usage statistics

---

**Version:** 2.0.0  
**Last Updated:** August 2025  
**Contact:** <EMAIL>
