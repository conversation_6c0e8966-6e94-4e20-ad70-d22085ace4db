<?php

/**
 * Complete Authentication Flow Test
 * Tests: Login → Get Old SSO Tokens → Refresh Token → Change Password
 */

// Test configuration
$baseUrl = 'http://192.168.1.34:8000/api/v2/hybrid-auth';
$username = '918793644824'; // Replace with actual test username
$password = 'dinesh@321'; // Replace with actual password
$newPassword = 'newPassword123!'; // New password to set

echo "=== Complete Authentication Flow Test ===\n\n";

// Helper function to make HTTP requests
function makeRequest($url, $data) {
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($data),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ],
    ]);

    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);

    return [
        'http_code' => $httpCode,
        'response' => $response,
        'data' => json_decode($response, true)
    ];
}

// Step 1: Login and get tokens (including Old SSO tokens)
echo "Step 1: Login to get tokens (including Old SSO tokens)\n";
echo "URL: {$baseUrl}/login\n";

$loginData = [
    'username' => $username,
    'password' => $password
];
echo "Request: " . json_encode(array_merge($loginData, ['password' => '***HIDDEN***']), JSON_PRETTY_PRINT) . "\n";

$result1 = makeRequest($baseUrl . '/login', $loginData);
echo "HTTP Code: {$result1['http_code']}\n";
echo "Response: " . $result1['response'] . "\n\n";

if ($result1['http_code'] !== 200 || !$result1['data']['success']) {
    echo "❌ Step 1 FAILED: Could not login\n";
    exit(1);
}

// Extract tokens
$keycloakTokens = $result1['data']['data']['tokens'] ?? null;
$oldSsoTokens = $result1['data']['data']['old_sso_tokens'] ?? null;
$accessToken = $oldSsoTokens['access_token'] ?? null;
$refreshToken = $oldSsoTokens['refresh_token'] ?? null;

if (!$accessToken || !$refreshToken) {
    echo "❌ Step 1 FAILED: access_token or refresh_token not found in Old SSO tokens\n";
    echo "Available Old SSO tokens: " . json_encode(array_keys($oldSsoTokens ?? []), JSON_PRETTY_PRINT) . "\n";
    exit(1);
}

echo "✅ Step 1 SUCCESS: Login successful\n";
echo "Keycloak Tokens: " . (isset($keycloakTokens['access_token']) ? 'Present' : 'Missing') . "\n";
echo "Old SSO Tokens: " . (isset($oldSsoTokens['access_token']) ? 'Present' : 'Missing') . "\n";
echo "Access Token: " . substr($accessToken, 0, 10) . "...\n";
echo "Refresh Token: " . substr($refreshToken, 0, 10) . "...\n\n";

// Step 2: Refresh token to get new access token
echo "Step 2: Refresh token to get new access token\n";
echo "URL: {$baseUrl}/refresh-token\n";

$refreshData = ['refresh_token' => $refreshToken];
echo "Request: " . json_encode(['refresh_token' => substr($refreshToken, 0, 10) . '...'], JSON_PRETTY_PRINT) . "\n";

$result2 = makeRequest($baseUrl . '/refresh-token', $refreshData);
echo "HTTP Code: {$result2['http_code']}\n";
echo "Response: " . $result2['response'] . "\n\n";

if ($result2['http_code'] !== 200 || !$result2['data']['success']) {
    echo "❌ Step 2 FAILED: Could not refresh token\n";
    exit(1);
}

$newAccessToken = $result2['data']['data']['access_token'] ?? null;
if (!$newAccessToken) {
    echo "❌ Step 2 FAILED: new access_token not found in refresh response\n";
    exit(1);
}

echo "✅ Step 2 SUCCESS: Token refreshed successfully\n";
echo "New Access Token: " . substr($newAccessToken, 0, 10) . "...\n\n";

// Step 3: Change password using access token with refresh token fallback
echo "Step 3: Change password using access token with refresh token fallback\n";
echo "URL: {$baseUrl}/change-password\n";

$changePasswordData = [
    'access_token' => $accessToken,
    'refresh_token' => $refreshToken,
    'old_password' => $password,
    'new_password' => $newPassword
];
echo "Request: " . json_encode([
    'access_token' => substr($accessToken, 0, 10) . '...',
    'refresh_token' => substr($refreshToken, 0, 10) . '...',
    'old_password' => '***HIDDEN***',
    'new_password' => '***HIDDEN***'
], JSON_PRETTY_PRINT) . "\n";

$result3 = makeRequest($baseUrl . '/change-password', $changePasswordData);
echo "HTTP Code: {$result3['http_code']}\n";
echo "Response: " . $result3['response'] . "\n\n";

if ($result3['http_code'] !== 200 || !$result3['data']['success']) {
    echo "❌ Step 3 FAILED: Could not change password\n";
    exit(1);
}

echo "✅ Step 3 SUCCESS: Password changed successfully\n";
echo "Old SSO Updated: " . ($result3['data']['data']['old_sso_updated'] ? 'Yes' : 'No') . "\n";
echo "Keycloak Updated: " . ($result3['data']['data']['keycloak_updated'] ? 'Yes' : 'No') . "\n\n";

// Summary
echo "=== COMPLETE AUTHENTICATION FLOW SUMMARY ===\n";
echo "✅ Step 1: Login with Old SSO tokens - SUCCESS\n";
echo "✅ Step 2: Refresh token - SUCCESS\n";
echo "✅ Step 3: Change password - SUCCESS\n\n";

echo "🎉 COMPLETE AUTHENTICATION FLOW - ALL STEPS SUCCESSFUL!\n\n";

echo "=== API ENDPOINTS TESTED ===\n";
echo "1. POST {$baseUrl}/login (with Old SSO tokens)\n";
echo "2. POST {$baseUrl}/refresh-token\n";
echo "3. POST {$baseUrl}/change-password\n\n";

echo "=== Test Complete ===\n";
