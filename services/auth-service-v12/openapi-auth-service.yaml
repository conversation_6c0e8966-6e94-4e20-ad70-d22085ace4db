openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - Authentication Service API
  description: |
    Authentication service for OneFoodDialer 2025 platform - Hybrid Authentication System.

    ## Features
    - Hybrid authentication (Old SSO + Keycloak integration)
    - Password and OTP-based login
    - User registration and profile management
    - Forgot password flow with OTP verification
    - Token management (access token refresh)
    - Password change with automatic token refresh
    - JWT authentication for secure access

    ## Authentication Methods
    1. **Password Login** - Traditional username/password authentication
    2. **OTP Login** - Mobile OTP-based authentication
    3. **Forgot Password** - OTP-based password reset flow
    4. **Token Refresh** - Refresh expired access tokens

    ## Security
    - JWT Bearer token authentication for protected endpoints
    - Rate limiting on all endpoints
    - Comprehensive logging and monitoring
    - Dual system integration (Old SSO + Keycloak)

    ## Database Configuration
    - Primary: Old SSO system integration
    - Secondary: Keycloak for modern authentication features
    - Automatic user synchronization between systems
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://*************:8000/api
    description: Proxy server
  - url: http://**************:8000/api
    description: Production server
  - url: http://************:8000/api
    description: Local server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # Authentication Responses
    LoginResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Authentication successful"
        data:
          type: object
          properties:
            user:
              $ref: '#/components/schemas/User'
            tokens:
              $ref: '#/components/schemas/KeycloakTokens'
            old_sso_tokens:
              $ref: '#/components/schemas/OldSsoTokens'
            token_type:
              type: string
              example: "Bearer"
            auth_type:
              type: string
              example: "hybrid"
            keycloak_synced:
              type: boolean
              example: true
            expires_in:
              type: integer
              example: 3600
            user_details_sync:
              type: object
              properties:
                details_verified:
                  type: boolean
                  example: true
                details_synced:
                  type: boolean
                  example: true
                sync_message:
                  type: string
                  example: "User details synchronized successfully"

    RegisterResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "User registered successfully"
        data:
          type: object
          properties:
            user:
              $ref: '#/components/schemas/User'
            registration_type:
              type: string
              example: "hybrid"
            keycloak_synced:
              type: boolean
              example: true

    OtpResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "OTP sent successfully"
        data:
          type: object
          properties:
            otp_sent:
              type: boolean
              example: true
            message:
              type: string
              example: "OTP has been sent to your registered mobile number"
            expires_in:
              type: integer
              example: 300
            type:
              type: string
              example: "login"

    ForgotPasswordOtpResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "OTP sent successfully for password reset"
        data:
          type: object
          properties:
            otp_sent:
              type: boolean
              example: true
            message:
              type: string
              example: "OTP has been sent to your registered mobile number for password reset"
            expires_in:
              type: integer
              example: 300
            type:
              type: string
              example: "forgot_password"

    ForgotPasswordVerifyResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "OTP verified successfully for password reset"
        data:
          type: object
          properties:
            user_id:
              type: string
              example: "92928"
            otp_type:
              type: string
              example: "mobile"
            value:
              type: string
              nullable: true
              example: null
            fp_auth_code:
              type: string
              example: "2XKhUjUyWGJ3G8SO4UFr5v59pTyedi40kQjXNyb6qYH5y"

    PasswordResetResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Password reset successfully"
        data:
          type: object
          properties:
            message:
              type: string
              example: "Your password has been reset successfully. You can now login with your new password."
            username:
              type: string
              example: "918793644824"
            reset_at:
              type: string
              format: date-time
              example: "2025-08-07T14:30:00.000000Z"

    RefreshTokenResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Access token refreshed successfully"
        data:
          type: object
          properties:
            access_token:
              type: string
              example: "newAccessToken123..."
            refresh_token:
              type: string
              example: "newRefreshToken456..."
            expires_in:
              type: integer
              example: 3600
            token_type:
              type: string
              example: "Bearer"

    ChangePasswordResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Password changed successfully"
        data:
          type: object
          properties:
            old_sso_updated:
              type: boolean
              example: true
            keycloak_updated:
              type: boolean
              example: true
            token_refreshed:
              type: boolean
              example: false
            changed_at:
              type: string
              format: date-time
              example: "2025-08-07T14:30:00.000000Z"

    User:
      type: object
      properties:
        user_id:
          type: string
          example: "92928"
        username:
          type: string
          example: "918793644824"
        email:
          type: string
          example: "<EMAIL>"
        mobile:
          type: string
          example: "918793644824"
        first_name:
          type: string
          example: "John"
        last_name:
          type: string
          example: "Doe"
        status:
          type: integer
          example: 1
        created_at:
          type: string
          format: date-time
          example: "2025-01-01T00:00:00.000000Z"
        updated_at:
          type: string
          format: date-time
          example: "2025-08-07T14:30:00.000000Z"

    KeycloakTokens:
      type: object
      properties:
        access_token:
          type: string
          example: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
        refresh_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        expires_in:
          type: integer
          example: 3600
        refresh_expires_in:
          type: integer
          example: 86400
        token_type:
          type: string
          example: "Bearer"

    OldSsoTokens:
      type: object
      properties:
        access_token:
          type: string
          example: "x6HreRlkU3I84Sa86KHkr6m7j1Ipixz4aAXy1d1a"
        refresh_token:
          type: string
          example: "RTeh18baI3sKFbBOtyLBJHixT1huBv8INwCJ01Gj"
        expires_in:
          type: integer
          example: 3600
        token_type:
          type: string
          example: "Bearer"

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        data:
          type: object
          nullable: true
          properties:
            errors:
              type: object
              additionalProperties: true
              example:
                username: ["The username field is required."]

security:
  - bearerAuth: []

tags:
  - name: Authentication
    description: User authentication and login operations
  - name: Registration
    description: User registration and account creation
  - name: OTP Management
    description: OTP request and verification operations
  - name: Forgot Password
    description: Password reset flow with OTP verification
  - name: Token Management
    description: Token refresh and management operations
  - name: Password Management
    description: Password change and update operations
  - name: User Management
    description: User search and profile operations

paths:
  /v2/hybrid-auth/login:
    post:
      summary: User login with password or OTP
      description: |
        Authenticate user using either password or OTP method.
        Returns both Keycloak tokens and Old SSO tokens for dual system compatibility.

        **Authentication Methods:**
        - **Password Login**: Provide username and password
        - **OTP Login**: Provide username and login_otp (obtained via request-otp)

        **Response includes:**
        - User profile information
        - Keycloak JWT tokens (for modern features)
        - Old SSO tokens (for legacy system compatibility)
        - Authentication type and sync status

        **Security:** Public endpoint - No JWT authentication required
      operationId: login
      tags: [Authentication]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "918793644824"
                password:
                  type: string
                  description: User password (required if login_otp not provided)
                  example: "MySecurePassword123!"
                login_otp:
                  type: string
                  description: 6-digit OTP (required if password not provided)
                  example: "123456"
                remember_me:
                  type: boolean
                  description: Whether to remember the user
                  example: true
            examples:
              password_login:
                summary: Password Login
                value:
                  username: "918793644824"
                  password: "MySecurePassword123!"
                  remember_me: true
              otp_login:
                summary: OTP Login
                value:
                  username: "918793644824"
                  login_otp: "123456"
                  remember_me: false
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/register:
    post:
      summary: Register new user
      description: |
        Register a new user in both Old SSO and Keycloak systems.
        Supports hybrid registration with automatic system synchronization.

        **Security:** Public endpoint - No JWT authentication required
      operationId: register
      tags: [Registration]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
                - email
                - mobile
              properties:
                username:
                  type: string
                  description: Unique username
                  example: "john_doe_2025"
                password:
                  type: string
                  description: User password (minimum 6 characters)
                  example: "SecurePassword123!"
                email:
                  type: string
                  format: email
                  description: User email address
                  example: "<EMAIL>"
                mobile:
                  type: string
                  description: Mobile number with country code
                  example: "918793644824"
                first_name:
                  type: string
                  description: User's first name
                  example: "John"
                last_name:
                  type: string
                  description: User's last name
                  example: "Doe"
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'
        '400':
          description: Registration failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/request-otp:
    post:
      summary: Request OTP for login
      description: |
        Request OTP for login authentication.
        OTP will be sent to the user's registered mobile number.

        **Security:** Public endpoint - No JWT authentication required
      operationId: requestOtp
      tags: [OTP Management]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "918793644824"
      responses:
        '200':
          description: OTP sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OtpResponse'
        '400':
          description: OTP request failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/verify-mobile-otp:
    post:
      summary: Verify mobile OTP
      description: |
        Verify the OTP received on mobile for login authentication.
        This is used in conjunction with request-otp for OTP-based login.

        **Security:** Public endpoint - No JWT authentication required
      operationId: verifyMobileOtp
      tags: [OTP Management]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - otp
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "918793644824"
                otp:
                  type: string
                  description: 4-digit OTP received on mobile
                  example: "1234"
      responses:
        '200':
          description: OTP verified successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "OTP verified successfully"
                  data:
                    type: object
                    properties:
                      verified:
                        type: boolean
                        example: true
                      username:
                        type: string
                        example: "918793644824"
        '400':
          description: OTP verification failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/forgot-password/request-otp:
    post:
      summary: Request OTP for forgot password
      description: |
        Request OTP for password reset flow.
        OTP will be sent to the user's registered mobile number.
        This is the first step in the forgot password process.

        **Security:** Public endpoint - No JWT authentication required
      operationId: requestForgotPasswordOtp
      tags: [Forgot Password]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "918793644824"
      responses:
        '200':
          description: Forgot password OTP sent successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForgotPasswordOtpResponse'
        '400':
          description: OTP request failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/forgot-password/verify-otp:
    post:
      summary: Verify OTP for forgot password
      description: |
        Verify the OTP received for password reset and get auth code.
        This is the second step in the forgot password process.
        Returns fp_auth_code needed for password reset.

        **Security:** Public endpoint - No JWT authentication required
      operationId: verifyForgotPasswordOtp
      tags: [Forgot Password]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - otp
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "918793644824"
                otp:
                  type: string
                  description: 4-digit OTP received on mobile
                  example: "1234"
      responses:
        '200':
          description: Forgot password OTP verified successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ForgotPasswordVerifyResponse'
        '400':
          description: OTP verification failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/forgot-password/reset-password:
    post:
      summary: Reset password using auth code
      description: |
        Reset user password using the auth code obtained from OTP verification.
        This is the final step in the forgot password process.
        Updates password in both Old SSO and Keycloak systems.

        **Security:** Public endpoint - No JWT authentication required
      operationId: resetPassword
      tags: [Forgot Password]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - fp_auth_code
                - password
              properties:
                username:
                  type: string
                  description: Username (email or mobile with country code)
                  example: "918793644824"
                fp_auth_code:
                  type: string
                  description: Auth code received from OTP verification
                  example: "W0E6OgwvotJPwCUqM3UoBXAEgcpBJ2X9JpK3U8gKUxRyp"
                password:
                  type: string
                  description: New password (minimum 6 characters)
                  example: "NewSecurePassword123!"
                otp_type:
                  type: string
                  description: OTP type used (email or mobile)
                  enum: [email, mobile]
                  example: "email"
      responses:
        '200':
          description: Password reset successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PasswordResetResponse'
        '400':
          description: Password reset failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/refresh-token:
    post:
      summary: Refresh access token
      description: |
        Refresh expired access token using refresh token.
        Returns new access token and refresh token from Old SSO system.

        **Security:** Public endpoint - Uses refresh token for authentication
      operationId: refreshToken
      tags: [Token Management]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refresh_token
              properties:
                refresh_token:
                  type: string
                  description: Valid refresh token
                  example: "RTeh18baI3sKFbBOtyLBJHixT1huBv8INwCJ01Gj"
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshTokenResponse'
        '400':
          description: Token refresh failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/change-password:
    post:
      summary: Change password with token fallback
      description: |
        Change user password using access token with automatic refresh fallback.

        **Flow Logic:**
        1. Try to change password with provided access_token
        2. If access_token expired, automatically refresh using refresh_token
        3. Retry password change with new access_token
        4. Update password in both Old SSO and Keycloak systems

        **Error Handling:**
        - Password mismatch errors return immediately (no token refresh)
        - Token expiration triggers automatic refresh and retry
        - Other errors return appropriate status codes

        **Security:** JWT authentication required
      operationId: changePassword
      tags: [Password Management]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - access_token
                - refresh_token
                - old_password
                - new_password
              properties:
                access_token:
                  type: string
                  description: Current access token
                  example: "x6HreRlkU3I84Sa86KHkr6m7j1Ipixz4aAXy1d1a"
                refresh_token:
                  type: string
                  description: Refresh token (used if access token expires)
                  example: "RTeh18baI3sKFbBOtyLBJHixT1huBv8INwCJ01Gj"
                old_password:
                  type: string
                  description: Current password
                  example: "CurrentPassword123!"
                new_password:
                  type: string
                  description: New password (minimum 6 characters)
                  example: "NewSecurePassword123!"
      responses:
        '200':
          description: Password changed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChangePasswordResponse'
        '400':
          description: Password change failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error (e.g., old password mismatch)
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Old password is incorrect. Please verify your current password."
                  data:
                    type: object
                    properties:
                      errors:
                        type: object
                        properties:
                          old_password:
                            type: array
                            items:
                              type: string
                            example: ["Old password did not match"]
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/search-user:
    post:
      summary: Search user by username
      description: |
        Search for user information by username.
        Returns user profile data from Old SSO system.
      operationId: searchUser
      tags: [User Management]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
              properties:
                username:
                  type: string
                  description: Username to search for
                  example: "918793644824"
      responses:
        '200':
          description: User found successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "User found successfully"
                  data:
                    $ref: '#/components/schemas/User'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/hybrid-auth/update-keycloak-credentials:
    post:
      summary: Update Keycloak credentials
      description: |
        Update user credentials in Keycloak system.
        Used for synchronizing credentials between Old SSO and Keycloak.
      operationId: updateKeycloakCredentials
      tags: [User Management]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: Username to update
                  example: "918793644824"
                password:
                  type: string
                  description: New password for Keycloak
                  example: "NewPassword123!"
      responses:
        '200':
          description: Keycloak credentials updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Keycloak credentials updated successfully"
                  data:
                    type: object
                    properties:
                      username:
                        type: string
                        example: "918793644824"
                      updated_at:
                        type: string
                        format: date-time
                        example: "2025-08-07T14:30:00.000000Z"
        '400':
          description: Update failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
