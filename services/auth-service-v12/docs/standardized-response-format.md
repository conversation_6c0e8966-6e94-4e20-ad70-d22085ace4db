# Standardized API Response Format

This document outlines the standardized response format for all API endpoints in the Auth Service.

## Response Structure

All API responses follow this consistent format:

```json
{
  "success": boolean,    // true for success, false for error
  "data": object|array,  // response data (always present, empty {} if no data)
  "message": string      // human-readable message
}
```

## HTTP Status Codes

| Code | Status | Usage |
|------|--------|-------|
| 200 | OK | Successful GET, PUT, PATCH operations |
| 201 | Created | Successful POST operations (resource created) |
| 202 | Accepted | Request accepted for processing |
| 204 | No Content | Successful DELETE operations |
| 400 | Bad Request | Client error (invalid request) |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Access denied (authenticated but not authorized) |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource conflict (duplicate, etc.) |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limiting |
| 500 | Internal Server Error | Server error |

## Response Examples

### Success Responses

#### User Found (200)
```json
{
  "success": true,
  "data": {
    "user_exists": true,
    "user_data": {
      "user_id": "12345",
      "email": "<EMAIL>",
      "mobile": "919988877766",
      "mobile_verified": true
    },
    "sync_status": "synced",
    "recommendations": {
      "can_login_password": true,
      "can_login_otp": true
    }
  },
  "message": "User found in old SSO system"
}
```

#### Login Success (200)
```json
{
  "success": true,
  "data": {
    "user": {
      "user_id": "12345",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "tokens": {
      "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
      "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
      "expires_in": 3600
    },
    "auth_type": "hybrid",
    "keycloak_synced": true,
    "user_details_sync": {
      "details_verified": true,
      "details_synced": false,
      "sync_method": "timestamp_comparison",
      "sync_message": "No sync needed - data already up to date"
    }
  },
  "message": "Authentication successful"
}
```

#### Resource Created (201)
```json
{
  "success": true,
  "data": {
    "user_id": "12346",
    "email": "<EMAIL>",
    "registration_status": "completed"
  },
  "message": "Registration successful"
}
```

### Error Responses

#### User Not Found (200 - Business Logic Success)
```json
{
  "success": true,
  "data": {
    "user_exists": false,
    "recommendations": {
      "can_register": true,
      "suggested_action": "register"
    }
  },
  "message": "User not found in old SSO system"
}
```

#### Authentication Failed (401)
```json
{
  "success": false,
  "data": {
    "auth_type": "old_sso",
    "error_details": "Invalid credentials"
  },
  "message": "Authentication failed"
}
```

#### Validation Error (422)
```json
{
  "success": false,
  "data": {
    "errors": {
      "username": ["The username field is required."],
      "password": ["The password must be at least 8 characters."]
    }
  },
  "message": "Validation failed"
}
```

#### Inactive User (403)
```json
{
  "success": false,
  "data": {
    "auth_type": "blocked_inactive",
    "user_data": {
      "user_id": "12345",
      "mobile_verified": false
    },
    "recommendations": {
      "required_action": "mobile_verification",
      "can_verify_mobile": true
    }
  },
  "message": "Login failed: User requires mobile verification"
}
```

#### Server Error (500)
```json
{
  "success": false,
  "data": {
    "error_code": "KEYCLOAK_CONNECTION_FAILED",
    "timestamp": "2025-01-31T23:30:00Z"
  },
  "message": "Authentication service temporarily unavailable"
}
```

## Implementation Guide

### Using ResponseHelper Methods

```php
// Success responses
return $this->successResponse('User found', $userData);
return $this->createdResponse('User registered', $registrationData);

// Error responses
return $this->errorResponse('User not found');
return $this->unauthorizedResponse('Invalid credentials');
return $this->validationErrorResponse($validationErrors);
return $this->forbiddenResponse('Access denied', $contextData);

// Custom responses
return $this->apiResponse(true, 'Custom success', $data, 200);
return $this->apiResponse(false, 'Custom error', $errorData, 400);
```

### Controller Method Example

```php
public function searchUser(HybridUserSearchRequest $request): JsonResponse
{
    try {
        $username = $request->input('username');
        $result = $this->hybridAuthService->userExistsBase($username);
        
        // Use standardized response format
        if ($result['success']) {
            $message = $result['exists'] ? 'User found' : 'User not found';
            return $this->successResponse($message, $result);
        } else {
            return $this->errorResponse($result['message'] ?? 'Search failed', $result);
        }
        
    } catch (Exception $e) {
        return $this->serverErrorResponse('Search failed: ' . $e->getMessage());
    }
}
```

## Benefits

1. **Consistency**: All endpoints return the same structure
2. **Predictability**: Clients know exactly what to expect
3. **Error Handling**: Standardized error information
4. **Status Codes**: Proper HTTP status codes for different scenarios
5. **Data Structure**: Always includes `success`, `data`, and `message`
6. **Flexibility**: `data` field can contain any relevant information
7. **Backward Compatibility**: Existing business logic preserved

## Migration Notes

- Existing business logic remains unchanged
- Service layer responses are wrapped in standardized format
- HTTP status codes properly reflect the response type
- Error details are preserved in the `data` field
- Success/failure is clearly indicated by the `success` field

This standardization improves API consistency while maintaining all existing functionality.
