# Hybrid Authentication System

## Overview

The Hybrid Authentication System integrates the legacy Old SSO system with Keycloak to provide a seamless authentication experience while maintaining backward compatibility. This system allows users to authenticate using either their existing Old SSO credentials or through the new Keycloak system.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │    │  Auth Service   │    │   Old SSO API   │
│                 │    │                 │    │                 │
│  - Web App      │◄──►│  - Validation   │◄──►│  - User Search  │
│  - Mobile App   │    │  - Token Mgmt   │    │  - Login        │
│  - API Client   │    │  - Sync Logic   │    │  - Registration │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │    Keycloak     │
                       │                 │
                       │  - SSO          │
                       │  - User Mgmt    │
                       │  - Token Issue  │
                       └─────────────────┘
```

## Features

### 🔍 Enhanced User Discovery
- Check if users exist in **both** Old SSO and Keycloak systems
- Determine synchronization status between systems
- Provide intelligent recommendations based on user status
- Automatic Keycloak registration for Old SSO users

### 🔐 Dual Authentication
- **Password-based**: Authenticate with Old SSO, sync with Keycloak
- **OTP-based**: SMS/Email OTP through Old SSO, fallback password for Key<PERSON>loak
- **Hybrid tokens**: Laravel Sanctum tokens for API access

### 🔄 Synchronization
- Automatic user creation in local database
- Password synchronization between systems
- Keycloak user management integration

### 📝 Registration
- Dual registration in both Old SSO and Keycloak
- Comprehensive validation and error handling
- Automatic user profile creation

## API Endpoints

### 1. Enhanced User Search
**POST** `/api/v2/hybrid-auth/search-user`

Check if a user exists in both Old SSO and Keycloak systems with intelligent recommendations.

```json
{
  "username": "<EMAIL>"
}
```

**Response Examples:**

**Fully Synchronized User:**
```json
{
  "success": true,
  "message": "User found in both Old SSO and Keycloak",
  "data": {
    "user_exists": true,
    "user_data": {
      "user_id": 123,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "old_sso_exists": true,
    "keycloak_exists": true,
    "sync_status": "synced",
    "recommendations": {
      "can_login_password": true,
      "can_login_otp": true,
      "needs_keycloak_registration": false
    },
    "can_login": true,
    "login_methods": ["password", "otp"]
  }
}
```

**User Needs Keycloak Registration:**
```json
{
  "success": true,
  "message": "User found in Old SSO but needs Keycloak registration",
  "data": {
    "user_exists": true,
    "user_data": {
      "user_id": 456,
      "email": "<EMAIL>",
      "first_name": "Jane",
      "last_name": "Smith"
    },
    "old_sso_exists": true,
    "keycloak_exists": false,
    "sync_status": "needs_keycloak_registration",
    "recommendations": {
      "can_login_password": true,
      "can_login_otp": true,
      "needs_keycloak_registration": true,
      "auto_register_on_login": true
    },
    "can_login": true,
    "login_methods": ["password", "otp"]
  }
}
```

**Keycloak Only User:**
```json
{
  "success": true,
  "message": "User found in Keycloak only",
  "data": {
    "user_exists": true,
    "user_data": null,
    "old_sso_exists": false,
    "keycloak_exists": true,
    "sync_status": "keycloak_only",
    "recommendations": {
      "can_login_password": true,
      "can_login_otp": false,
      "suggest_keycloak_login": true
    },
    "can_login": true,
    "login_methods": ["password"]
  }
}
```

### 2. Request OTP
**POST** `/api/v2/hybrid-auth/request-otp`

Request an OTP for authentication.

```json
{
  "username": "919988877766"
}
```

**Response:**
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "otp_sent": true,
    "message": "OTP has been sent to your registered mobile number",
    "expires_in": 300
  }
}
```

### 3. Login
**POST** `/api/v2/hybrid-auth/login`

Authenticate using password or OTP.

**Password Login:**
```json
{
  "username": "<EMAIL>",
  "password": "MySecurePassword123!",
  "remember_me": false
}
```

**OTP Login:**
```json
{
  "username": "919988877766",
  "login_otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Authentication successful",
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "token": "1|abc123...",
    "token_type": "Bearer",
    "auth_type": "hybrid",
    "keycloak_synced": true,
    "expires_in": 3600
  }
}
```

### 4. Registration
**POST** `/api/v2/hybrid-auth/register`

Register a new user in both systems.

```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "mobile": "919988877766",
  "password": "MySecurePassword123!",
  "password_confirmation": "MySecurePassword123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration successful",
  "data": {
    "user": {
      "id": 2,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe"
    },
    "auth_type": "hybrid",
    "keycloak_registered": true,
    "message": "Registration completed successfully. You can now login with your credentials."
  }
}
```

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# Old SSO Configuration
OLD_SSO_DOMAIN_1=https://company-domain-1
OLD_SSO_DOMAIN_2=https://company-domain-2
OLD_SSO_API_KEY=your-api-key-here
OLD_SSO_CLIENT_ID=your-client-id-here
OLD_SSO_CLIENT_SECRET=your-client-secret-here
OLD_SSO_SOURCE=your-source-here
OLD_SSO_REF_URL_DOMAIN=your-ref-url-domain-here
```

### Configuration File

The system uses `config/old_sso.php` for detailed configuration:

```php
return [
    'domain_1' => env('OLD_SSO_DOMAIN_1'),
    'domain_2' => env('OLD_SSO_DOMAIN_2'),
    'api_key' => env('OLD_SSO_API_KEY'),
    'client_id' => env('OLD_SSO_CLIENT_ID'),
    'client_secret' => env('OLD_SSO_CLIENT_SECRET'),
    'source' => env('OLD_SSO_SOURCE'),
    'ref_url_domain' => env('OLD_SSO_REF_URL_DOMAIN'),
    
    'endpoints' => [
        'user_search' => '/api/v2/users/search',
        'user_otp' => '/api/v2/users/otp',
        'user_login' => '/api/v2/users/login',
        'user_register' => '/api/v2/users',
    ],
    
    'password_sync' => [
        'enabled' => true,
        'update_on_login' => true,
        'fallback_password_pattern' => '1to9',
    ],
];
```

## Authentication Flow

### Enhanced Password Authentication Flow

1. **User Search**: Check if user exists in both Old SSO and Keycloak
2. **Old SSO Login**: Authenticate with Old SSO using password
3. **User Creation**: Create/update user in local database
4. **Keycloak Check**: Verify if user exists in Keycloak
5. **Auto-Registration**: If user doesn't exist in Keycloak, automatically register them
6. **Keycloak Sync**: Authenticate with Keycloak using same credentials
7. **Token Generation**: Create Laravel Sanctum token
8. **Response**: Return user data, token, and sync status

### Enhanced OTP Authentication Flow

1. **User Search**: Check if user exists in both Old SSO and Keycloak
2. **OTP Request**: Request OTP from Old SSO
3. **OTP Verification**: Verify OTP with Old SSO
4. **User Creation**: Create/update user in local database
5. **Keycloak Check**: Verify if user exists in Keycloak
6. **Auto-Registration**: If user doesn't exist in Keycloak, register with fallback password
7. **Keycloak Sync**: Use fallback password for Keycloak authentication
8. **Token Generation**: Create Laravel Sanctum token
9. **Response**: Return user data, token, and sync status

### Automatic Keycloak Registration

When a user exists in Old SSO but not in Keycloak:

**For Password Login:**
- User credentials are used to register in Keycloak
- Same password is maintained across both systems
- User can seamlessly use either system

**For OTP Login:**
- Fallback password is generated (configurable pattern)
- User is registered in Keycloak with fallback password
- Mobile number or pattern-based password is used

**Benefits:**
- ✅ Seamless migration from Old SSO to Keycloak
- ✅ No user intervention required
- ✅ Maintains backward compatibility
- ✅ Gradual system transition

## Error Handling

The system provides comprehensive error handling:

### Validation Errors (422)
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "username": ["The username field is required."],
    "password": ["The password field is required."]
  }
}
```

### Authentication Errors (401)
```json
{
  "success": false,
  "message": "Old SSO authentication failed: Invalid credentials"
}
```

### Server Errors (500)
```json
{
  "success": false,
  "message": "Authentication failed: Network timeout"
}
```

## Security Features

### Rate Limiting
- **User Search**: 10 requests per minute
- **OTP Request**: 5 requests per minute  
- **Login**: 5 requests per minute
- **Registration**: 3 requests per minute

### Input Validation
- Email format validation
- Mobile number format (91xxxxxxxxxx)
- Password strength requirements
- XSS and injection prevention

### Logging
All authentication events are logged with structured data:
- User identification
- IP address and user agent
- Success/failure status
- Error details
- Keycloak sync status

## Testing

### Unit Tests
```bash
php artisan test --filter=OldSsoApiClientTest
php artisan test --filter=OldSsoAuthenticationServiceTest
```

### Feature Tests
```bash
php artisan test --filter=HybridAuthControllerTest
```

### Manual Testing
Use the provided cURL examples or Postman collection to test the endpoints.

## Monitoring

### Metrics
- Authentication success/failure rates
- Old SSO vs Keycloak sync rates
- Response times
- Error rates by endpoint

### Alerts
- High failure rates
- Old SSO API unavailability
- Keycloak sync failures
- Rate limit violations

## Troubleshooting

### Common Issues

1. **Old SSO API Timeout**
   - Check network connectivity
   - Verify API endpoints
   - Review timeout settings

2. **Keycloak Sync Failures**
   - Verify Keycloak configuration
   - Check user permissions
   - Review password policies

3. **Token Issues**
   - Verify Sanctum configuration
   - Check token expiration
   - Review middleware setup

### Debug Mode
Enable debug logging by setting `LOG_LEVEL=debug` in your `.env` file.

## Migration Guide

### From Old SSO Only
1. Deploy the hybrid authentication system
2. Update client applications to use new endpoints
3. Test authentication flows
4. Monitor sync success rates

### To Keycloak Only
1. Ensure all users are synced to Keycloak
2. Update authentication endpoints
3. Disable Old SSO integration
4. Remove Old SSO dependencies

## Support

For issues and questions:
- Check the logs in `storage/logs/laravel.log`
- Review the OpenAPI documentation
- Contact the development team
- Submit issues through the project repository
