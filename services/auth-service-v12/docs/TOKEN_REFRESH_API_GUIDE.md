# Token Refresh API Guide for Auth Service v12

This guide explains the token refresh functionality in the auth-service-v12, which allows clients to obtain new access tokens using refresh tokens without requiring user re-authentication.

## Overview

The token refresh system provides:
- **Seamless Token Renewal**: Refresh expired access tokens without user interaction
- **Security**: Automatic refresh token rotation for enhanced security
- **Multi-Token Support**: Updates both Keycloak and Laravel Sanctum tokens
- **Rate Limiting**: Protection against token refresh abuse
- **Comprehensive Logging**: Full audit trail of token refresh activities

## API Endpoints

### 1. Refresh Token Endpoint

**Endpoint**: `POST /api/v2/auth/refresh-token`

**Purpose**: Generate new access and refresh tokens using a valid refresh token

**Request Format**:
```json
{
    "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response** (200):
```json
{
    "success": true,
    "message": "Token refreshed successfully",
    "data": {
        "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
        "token_type": "Bearer",
        "expires_in": 3600,
        "token": "laravel_sanctum_token_here",
        "user": {
            "id": "user-uuid",
            "username": "<EMAIL>",
            "email": "<EMAIL>",
            "name": "John Doe",
            "old_sso_user_id": "12345"
        }
    }
}
```

**Error Response** (401):
```json
{
    "success": false,
    "message": "Invalid or expired refresh token",
    "error_code": "INVALID_REFRESH_TOKEN"
}
```

### 2. Token Validation Endpoint

**Endpoint**: `POST /api/v2/auth/validate-token`

**Purpose**: Validate if an access token is still valid

**Request Format**:
```json
{
    "token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Success Response** (200):
```json
{
    "success": true,
    "data": {
        "valid": true,
        "expires_in": 1800,
        "user_id": "user-uuid",
        "username": "<EMAIL>"
    }
}
```

## Implementation Details

### Token Refresh Flow

```mermaid
sequenceDiagram
    participant Client
    participant AuthService
    participant Keycloak
    participant Database

    Client->>AuthService: POST /refresh-token
    AuthService->>AuthService: Validate refresh token
    AuthService->>Keycloak: Exchange refresh token
    Keycloak->>AuthService: New tokens
    AuthService->>Database: Update user tokens
    AuthService->>Client: Return new tokens
```

### Security Features

1. **Refresh Token Rotation**: Each refresh generates a new refresh token
2. **Rate Limiting**: Maximum 5 refresh attempts per minute per user
3. **Token Validation**: Comprehensive validation of refresh token integrity
4. **Audit Logging**: All refresh attempts are logged with user context
5. **Automatic Cleanup**: Expired tokens are automatically invalidated

### Token Lifecycle

| Token Type | Lifetime | Purpose | Rotation |
|------------|----------|---------|----------|
| **Access Token** | 1 hour | API authentication | Every refresh |
| **Refresh Token** | 30 days | Token renewal | Every refresh |
| **Laravel Token** | Session | Laravel Sanctum auth | Every refresh |

## Client Integration

### JavaScript/TypeScript Example

```javascript
class AuthTokenManager {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.accessToken = localStorage.getItem('access_token');
        this.refreshToken = localStorage.getItem('refresh_token');
    }

    async refreshTokens() {
        try {
            const response = await fetch(`${this.baseUrl}/api/v2/auth/refresh-token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    refresh_token: this.refreshToken
                })
            });

            const data = await response.json();

            if (data.success) {
                // Update stored tokens
                this.accessToken = data.data.access_token;
                this.refreshToken = data.data.refresh_token;
                
                localStorage.setItem('access_token', this.accessToken);
                localStorage.setItem('refresh_token', this.refreshToken);
                localStorage.setItem('laravel_token', data.data.token);

                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            // Redirect to login
            this.logout();
            throw error;
        }
    }

    async makeAuthenticatedRequest(url, options = {}) {
        try {
            // Try with current access token
            const response = await fetch(url, {
                ...options,
                headers: {
                    ...options.headers,
                    'Authorization': `Bearer ${this.accessToken}`
                }
            });

            if (response.status === 401) {
                // Token expired, try to refresh
                await this.refreshTokens();
                
                // Retry with new token
                return fetch(url, {
                    ...options,
                    headers: {
                        ...options.headers,
                        'Authorization': `Bearer ${this.accessToken}`
                    }
                });
            }

            return response;
        } catch (error) {
            console.error('Authenticated request failed:', error);
            throw error;
        }
    }

    logout() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('laravel_token');
        window.location.href = '/login';
    }
}

// Usage
const authManager = new AuthTokenManager('http://***********:8000');

// Make authenticated API calls
authManager.makeAuthenticatedRequest('/api/v2/admin/settings/dropdowns')
    .then(response => response.json())
    .then(data => console.log(data));
```

### PHP/Laravel Example

```php
<?php

class TokenRefreshService
{
    private $baseUrl;
    private $accessToken;
    private $refreshToken;

    public function __construct($baseUrl)
    {
        $this->baseUrl = $baseUrl;
        $this->accessToken = session('access_token');
        $this->refreshToken = session('refresh_token');
    }

    public function refreshTokens()
    {
        $response = Http::post($this->baseUrl . '/api/v2/auth/refresh-token', [
            'refresh_token' => $this->refreshToken
        ]);

        if ($response->successful() && $response->json('success')) {
            $data = $response->json('data');
            
            $this->accessToken = $data['access_token'];
            $this->refreshToken = $data['refresh_token'];
            
            session([
                'access_token' => $this->accessToken,
                'refresh_token' => $this->refreshToken,
                'laravel_token' => $data['token']
            ]);

            return $data;
        }

        throw new Exception('Token refresh failed: ' . $response->json('message'));
    }

    public function makeAuthenticatedRequest($url, $method = 'GET', $data = [])
    {
        try {
            $response = Http::withToken($this->accessToken)
                ->$method($url, $data);

            if ($response->status() === 401) {
                // Token expired, refresh and retry
                $this->refreshTokens();
                
                $response = Http::withToken($this->accessToken)
                    ->$method($url, $data);
            }

            return $response;
        } catch (Exception $e) {
            Log::error('Authenticated request failed', [
                'url' => $url,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
```

## Error Handling

### Common Error Scenarios

| Error Code | HTTP Status | Description | Client Action |
|------------|-------------|-------------|---------------|
| `INVALID_REFRESH_TOKEN` | 401 | Refresh token is invalid or expired | Redirect to login |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many refresh attempts | Wait and retry |
| `KEYCLOAK_ERROR` | 500 | Keycloak service error | Retry or fallback |
| `VALIDATION_ERROR` | 422 | Invalid request format | Fix request format |

### Error Response Format

```json
{
    "success": false,
    "message": "Human readable error message",
    "error_code": "MACHINE_READABLE_CODE",
    "errors": {
        "field_name": ["Validation error details"]
    }
}
```

## Rate Limiting

The refresh token endpoint is protected by rate limiting:

- **Limit**: 5 requests per minute per user
- **Identification**: Based on refresh token or IP address
- **Response**: HTTP 429 with retry-after header
- **Bypass**: Not available for security reasons

## Monitoring and Logging

### Logged Events

1. **Successful Refresh**: User ID, new token expiration, timestamp
2. **Failed Refresh**: User ID, error reason, IP address
3. **Rate Limit Hit**: User ID, attempt count, IP address
4. **Token Validation**: Token validity, expiration status

### Metrics to Monitor

- **Refresh Success Rate**: Should be > 95%
- **Average Refresh Time**: Should be < 500ms
- **Rate Limit Hits**: Should be minimal
- **Failed Refresh Attempts**: Monitor for suspicious activity

## Best Practices

### For Client Applications

1. **Automatic Refresh**: Implement automatic token refresh on 401 responses
2. **Token Storage**: Store tokens securely (HttpOnly cookies preferred)
3. **Error Handling**: Gracefully handle refresh failures
4. **Retry Logic**: Implement exponential backoff for failed requests
5. **Logout on Failure**: Clear tokens and redirect to login on refresh failure

### For Server Applications

1. **Token Validation**: Always validate tokens before processing requests
2. **Rate Limiting**: Implement appropriate rate limits
3. **Logging**: Log all authentication events for security monitoring
4. **Token Cleanup**: Regularly clean up expired tokens
5. **Security Headers**: Use appropriate security headers

## Testing

### Manual Testing

```bash
# 1. Login to get tokens
curl -X POST "http://***********:8000/api/v2/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password"}'

# 2. Extract refresh_token from response

# 3. Test token refresh
curl -X POST "http://***********:8000/api/v2/auth/refresh-token" \
  -H "Content-Type: application/json" \
  -d '{"refresh_token": "YOUR_REFRESH_TOKEN_HERE"}'

# 4. Test with new access token
curl -X GET "http://***********:8001/api/v2/admin/health" \
  -H "Authorization: Bearer NEW_ACCESS_TOKEN"
```

### Automated Testing

Use the provided test script:

```bash
# Run comprehensive token refresh tests
./services/auth-service-v12/scripts/test-token-refresh.sh
```

## Integration with Admin Service

The refreshed tokens work seamlessly with the admin-service-v12 JWT middleware:

```php
// Admin service automatically validates refreshed tokens
Route::middleware(\App\Http\Middleware\KeycloakJwtAuth::class)->group(function () {
    Route::get('/settings/dropdowns', [SettingsController::class, 'getDropdownSettings']);
});
```

The JWT middleware in admin-service-v12 will:
- ✅ Validate the refreshed access token
- ✅ Extract user information including Old SSO User ID
- ✅ Provide role-based access control
- ✅ Log authentication events

## Conclusion

The token refresh API provides a robust, secure solution for maintaining user sessions without requiring re-authentication. It integrates seamlessly with both Keycloak and Laravel Sanctum, ensuring compatibility across all services in the ecosystem.

Key benefits:
- **Enhanced Security**: Automatic token rotation and validation
- **Better UX**: Seamless token renewal without user interruption
- **Comprehensive Monitoring**: Full audit trail and metrics
- **Easy Integration**: Simple API with comprehensive error handling
- **Production Ready**: Rate limiting, logging, and security best practices
