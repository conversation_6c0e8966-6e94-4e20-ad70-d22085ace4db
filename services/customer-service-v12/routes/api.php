<?php

use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\CustomerAddressController;
use App\Http\Controllers\Api\DeliveryLocationController;
use App\Http\Controllers\Api\HealthController;
use App\Http\Controllers\Api\MenuController;
use App\Http\Controllers\Api\ParentController;
use App\Http\Controllers\Api\ProductMenuController;
use App\Http\Controllers\Api\WalletController;
use App\Http\Controllers\Api\V2\PaymentModeController;
use App\Http\Controllers\Api\V2\SettingsController;
use App\Http\Controllers\Api\StudentAddressController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Note: User authentication is handled by auth-service-v12
// Customer service handles customer-related operations only

// Health check endpoint
Route::get('/health', [HealthController::class, 'index']);

// Test routes for development (without authentication)
Route::prefix('test')->group(function () {
    Route::get('/parents/{parentId}/children', [ParentController::class, 'getChildren'])->where('parentId', '[0-9]+');
    Route::post('/parents/{parentId}/children', [ParentController::class, 'addChild'])->where('parentId', '[0-9]+');
    Route::put('/parents/{parentId}/children/{childId}', [ParentController::class, 'updateChild'])->where(['parentId' => '[0-9]+', 'childId' => '[0-9]+']);
    Route::delete('/parents/{parentId}/children/{childId}', [ParentController::class, 'removeChild'])->where(['parentId' => '[0-9]+', 'childId' => '[0-9]+']);
    Route::get('/schools/available', [ParentController::class, 'getAvailableSchools']);
    Route::get('/schools/summary', [ParentController::class, 'getSchoolsSummary']);
    Route::get('/customer/{userId}/{companyId}', [CustomerController::class, 'getCustomerByUserId'])->where(['userId' => '[0-9]+', 'companyId' => '[0-9]+']);
    
    // Debug routes
    Route::get('/debug/customers', [CustomerController::class, 'debugCustomers']);
    Route::get('/debug/children', [ParentController::class, 'debugChildren']);
});

// API v1 routes (for backward compatibility)
Route::prefix('v1')->group(function () {
    // Customer routes
    Route::prefix('customers')->group(function () {
        Route::get('/', [CustomerController::class, 'index']);
        Route::post('/', [CustomerController::class, 'store']);
        Route::get('/{id}', [CustomerController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/{id}', [CustomerController::class, 'update'])->where('id', '[0-9]+');
        Route::delete('/{id}', [CustomerController::class, 'destroy'])->where('id', '[0-9]+');

        // Customer address routes
        Route::post('/{id}/addresses', [CustomerController::class, 'addAddress'])->where('id', '[0-9]+');
        Route::put('/{id}/addresses/{addressId}', [CustomerController::class, 'updateAddress'])->where(['id' => '[0-9]+', 'addressId' => '[0-9]+']);
        Route::delete('/{id}/addresses/{addressId}', [CustomerController::class, 'deleteAddress'])->where(['id' => '[0-9]+', 'addressId' => '[0-9]+']);
    });
});

// API v2 routes (for new features)
Route::prefix('v2')->group(function () {
    // Customer routes
    Route::prefix('customers')->middleware(['auth.token'])->group(function () {
        // Core CRUD operations
        Route::get('/', [CustomerController::class, 'index']);
        Route::post('/', [CustomerController::class, 'store']);
        Route::get('/{id}', [CustomerController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/{id}', [CustomerController::class, 'update'])->where('id', '[0-9]+');
        Route::delete('/{id}', [CustomerController::class, 'destroy'])->where('id', '[0-9]+');

        // Customer lookup and search
        Route::get('/search', [CustomerController::class, 'search']);
        Route::get('/phone/{phone}', [CustomerController::class, 'getByPhone']);
        Route::get('/email/{email}', [CustomerController::class, 'getByEmail']);
        Route::get('/code/{code}', [CustomerController::class, 'getByCode']);
        Route::post('/lookup', [CustomerController::class, 'lookup']);
        Route::post('/verify', [CustomerController::class, 'verify']);

        // Customer profile management
        Route::post('/{id}/profile', [CustomerController::class, 'updateProfile'])->where('id', '[0-9]+');
        Route::post('/{id}/preferences', [CustomerController::class, 'updatePreferences'])->where('id', '[0-9]+');
        Route::get('/{id}/preferences', [CustomerController::class, 'getPreferences'])->where('id', '[0-9]+');
        Route::post('/{id}/avatar', [CustomerController::class, 'uploadAvatar'])->where('id', '[0-9]+');

        // Authentication and verification
        Route::post('/{id}/otp/send', [CustomerController::class, 'sendOtp'])->where('id', '[0-9]+');
        Route::post('/{id}/otp/verify', [CustomerController::class, 'verifyOtp'])->where('id', '[0-9]+');
        Route::post('/{id}/phone/verify', [CustomerController::class, 'verifyPhone'])->where('id', '[0-9]+');
        Route::post('/{id}/email/verify', [CustomerController::class, 'verifyEmail'])->where('id', '[0-9]+');
        Route::post('/{id}/password/change', [CustomerController::class, 'changePassword'])->where('id', '[0-9]+');
        Route::post('/password/reset', [CustomerController::class, 'resetPassword']);

        // Customer status management
        Route::post('/{id}/activate', [CustomerController::class, 'activate'])->where('id', '[0-9]+');
        Route::post('/{id}/deactivate', [CustomerController::class, 'deactivate'])->where('id', '[0-9]+');
        Route::post('/{id}/suspend', [CustomerController::class, 'suspend'])->where('id', '[0-9]+');
        Route::post('/{id}/unsuspend', [CustomerController::class, 'unsuspend'])->where('id', '[0-9]+');

        // Customer relationships
        Route::get('/{id}/orders', [CustomerController::class, 'getOrders'])->where('id', '[0-9]+');
        Route::get('/{id}/payments', [CustomerController::class, 'getPayments'])->where('id', '[0-9]+');
        Route::get('/{id}/subscriptions', [CustomerController::class, 'getSubscriptions'])->where('id', '[0-9]+');
        Route::get('/{id}/notifications', [CustomerController::class, 'getNotifications'])->where('id', '[0-9]+');
        Route::get('/{id}/activity', [CustomerController::class, 'getActivity'])->where('id', '[0-9]+');

        // Customer analytics
        Route::get('/{id}/statistics', [CustomerController::class, 'getStatistics'])->where('id', '[0-9]+');
        Route::get('/{id}/insights', [CustomerController::class, 'getInsights'])->where('id', '[0-9]+');
        Route::get('/analytics/summary', [CustomerController::class, 'getAnalyticsSummary']);
        Route::get('/analytics/demographics', [CustomerController::class, 'getDemographics']);

        // Bulk operations
        Route::post('/bulk/import', [CustomerController::class, 'bulkImport']);
        Route::post('/bulk/export', [CustomerController::class, 'bulkExport']);
        Route::post('/bulk/update', [CustomerController::class, 'bulkUpdate']);
        Route::post('/bulk/delete', [CustomerController::class, 'bulkDelete']);
        Route::post('/bulk/notify', [CustomerController::class, 'bulkNotify']);

        // Customer address routes
        Route::get('/{id}/addresses', [CustomerController::class, 'getAddresses'])->where('id', '[0-9]+');
        Route::post('/{id}/addresses', [CustomerController::class, 'addAddress'])->where('id', '[0-9]+');
        Route::put('/{id}/addresses/{addressId}', [CustomerController::class, 'updateAddress'])->where(['id' => '[0-9]+', 'addressId' => '[0-9]+']);
        Route::delete('/{id}/addresses/{addressId}', [CustomerController::class, 'deleteAddress'])->where(['id' => '[0-9]+', 'addressId' => '[0-9]+']);
        Route::post('/{id}/addresses/{addressId}/default', [CustomerController::class, 'setDefaultAddress'])->where(['id' => '[0-9]+', 'addressId' => '[0-9]+']);
        Route::get('/{id}/addresses/dynamic/default', [CustomerController::class, 'getDefaultAddress'])->where('id', '[0-9]+');

        // Customer wallet routes
        Route::middleware(['auth.token'])->group(function () {
            Route::get('/{id}/wallet', [WalletController::class, 'show'])->where('id', '[0-9]+');
            Route::post('/{id}/wallet/deposit', [WalletController::class, 'deposit'])->where('id', '[0-9]+');
            Route::post('/{id}/wallet/withdraw', [WalletController::class, 'withdraw'])->where('id', '[0-9]+');
            Route::get('/{id}/wallet/transactions', [WalletController::class, 'transactions'])->where('id', '[0-9]+');
            Route::get('/{id}/wallet/balance', [WalletController::class, 'getBalance'])->where('id', '[0-9]+');
            Route::post('/{id}/wallet/transfer', [WalletController::class, 'transfer'])->where('id', '[0-9]+');
            Route::post('/{id}/wallet/freeze', [WalletController::class, 'freeze'])->where('id', '[0-9]+');
            Route::post('/{id}/wallet/unfreeze', [WalletController::class, 'unfreeze'])->where('id', '[0-9]+');
            Route::get('/{id}/wallet/history', [WalletController::class, 'getHistory'])->where('id', '[0-9]+');
        });

        // Customer payment mode routes
        Route::get('/{id}/payment-mode', [PaymentModeController::class, 'getPaymentMode'])->where('id', '[0-9]+');
        Route::put('/{id}/payment-mode', [PaymentModeController::class, 'updatePaymentMode'])->where('id', '[0-9]+');
    });

    // Payment mode management routes
    Route::prefix('payment-modes')->middleware(['auth.token'])->group(function () {
        Route::get('/statistics', [PaymentModeController::class, 'getPaymentModeStatistics']);
        Route::post('/bulk-update', [PaymentModeController::class, 'bulkUpdatePaymentMode']);
    });

    // Settings routes
    Route::prefix('settings')->group(function () {
        Route::post('/dropdowns', [SettingsController::class, 'getDropdownSettings']);
        Route::get('/dropdowns', [SettingsController::class, 'getDropdownSettings']);
        Route::get('/{key}', [SettingsController::class, 'getSetting']);
    });

    // Direct wallet routes (for frontend integration)
    Route::prefix('wallet')->middleware(['auth.token'])->group(function () {
        Route::get('/{customerId}', [WalletController::class, 'show'])->where('customerId', '[0-9]+');
        Route::post('/add', [WalletController::class, 'deposit']);
        Route::post('/deduct', [WalletController::class, 'withdraw']);
        Route::get('/{customerId}/transactions', [WalletController::class, 'transactions'])->where('customerId', '[0-9]+');
        Route::get('/{customerId}/balance', [WalletController::class, 'getBalance'])->where('customerId', '[0-9]+');
        Route::post('/transfer', [WalletController::class, 'transfer']);
        Route::get('/history', [WalletController::class, 'getAllHistory']);
        Route::get('/statistics', [WalletController::class, 'getStatistics']);
    });

    // Parent routes for school tiffin system
    Route::prefix('parents')->group(function () {
        // Public routes (no authentication required)
        Route::post('/register', [ParentController::class, 'register']);
        Route::get('/schools/available', [ParentController::class, 'getAvailableSchools']);
        Route::get('/schools/summary', [ParentController::class, 'getSchoolsSummary']);

        // Protected routes (authentication required)
        Route::middleware(['auth.token'])->group(function () {
            Route::get('/profile', [ParentController::class, 'getProfile']);
            Route::get('/children', [ParentController::class, 'getChildren']);
            Route::post('/children', [ParentController::class, 'addChild']);
            Route::put('/children/{childId}', [ParentController::class, 'updateChild']);
            Route::delete('/children/{childId}', [ParentController::class, 'removeChild']);
            // Keep old routes for backward compatibility if needed
            Route::get('/{parentId}/profile', [ParentController::class, 'getProfile'])->where('parentId', '[0-9]+');
            Route::get('/{parentId}/dashboard', [ParentController::class, 'getDashboardSummary'])->where('parentId', '[0-9]+');
            Route::post('/{parentId}/verify', [ParentController::class, 'verifyParent'])->where('parentId', '[0-9]+');
            Route::get('/{parentId}/children', [ParentController::class, 'getChildren'])->where('parentId', '[0-9]+');
            Route::post('/{parentId}/children', [ParentController::class, 'addChild'])->where('parentId', '[0-9]+');
            Route::put('/{parentId}/children/{childId}', [ParentController::class, 'updateChild'])->where(['parentId' => '[0-9]+', 'childId' => '[0-9]+']);
            Route::delete('/{parentId}/children/{childId}', [ParentController::class, 'removeChild'])->where(['parentId' => '[0-9]+', 'childId' => '[0-9]+']);
        });
    });

    // New routes for updating and deleting a customer by user_id
    Route::get('customers/by-user', [\App\Http\Controllers\Api\V2\CustomerController::class, 'getByUserIdAndCompany']);
    Route::put('customers/by-user/{user_id}', [\App\Http\Controllers\Api\V2\CustomerController::class, 'updateByUserId']);
    Route::delete('customers/by-user/{user_id}', [\App\Http\Controllers\Api\V2\CustomerController::class, 'deleteByUserId']);

    // V2 Customer registration routes (direct customer registration)
    Route::post('customers/register-keycloak', [\App\Http\Controllers\Api\V2\CustomerController::class, 'registerFromKeycloak']);
    Route::post('customers/login-keycloak', [\App\Http\Controllers\Api\V2\CustomerController::class, 'loginFromKeycloak']);
    Route::post('customers/register', [\App\Http\Controllers\Api\V2\CustomerController::class, 'registerCustomer']);
    Route::post('customers/login', [\App\Http\Controllers\Api\V2\CustomerController::class, 'loginCustomer']);
    Route::post('customers/logout', [\App\Http\Controllers\Api\V2\CustomerController::class, 'logoutCustomer'])->middleware('auth:sanctum');

    // V2 Customer sync route (for auth service)
    Route::post('customers/sync', [\App\Http\Controllers\Api\V2\CustomerController::class, 'syncCustomer']);

    // Customer sync routes (called by auth service)
    Route::post('/customers/sync', [CustomerController::class, 'syncCustomer']);
    Route::put('/customers/sync/{userId}', [CustomerController::class, 'updateCustomerSync']);
    Route::get('/customers/user/{userId}/company/{companyId}', [CustomerController::class, 'getCustomerByUserId']);

    // Parent and Child management routes with dynamic parent_customer_id
    Route::prefix('parents')->group(function () {
        // New routes using user_id and company_id instead of static parent_customer_id
        Route::post('/children/by-user-company', [ParentController::class, 'addChildByUserAndCompany']);
        Route::get('/children/by-user-company', [ParentController::class, 'getChildrenByUserAndCompany']);
    });

    // Student Addresses management  
    Route::prefix('student-addresses')->middleware(['auth.token'])->group(function () {
        // Read operations
        Route::get('/', [StudentAddressController::class, 'index']);
        Route::get('/{id}', [StudentAddressController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/statistics/summary', [StudentAddressController::class, 'stats']);
        Route::get('/location/{locationCode}', [StudentAddressController::class, 'getByLocation'])->where('locationCode', '[0-9]+');
        
        // Write operations
        Route::post('/', [StudentAddressController::class, 'store']);
        Route::put('/{id}', [StudentAddressController::class, 'update'])->where('id', '[0-9]+');
        Route::delete('/{id}', [StudentAddressController::class, 'destroy'])->where('id', '[0-9]+');
    });

    // Customer Addresses management (includes student profiles for school addresses)
    Route::prefix('customer-addresses')->middleware(['auth.token'])->group(function () {
        // Read operations
        Route::get('/', [CustomerAddressController::class, 'index']);
        Route::get('/{id}', [CustomerAddressController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/statistics/summary', [CustomerAddressController::class, 'stats']);
        Route::get('/location/{locationCode}', [CustomerAddressController::class, 'getByLocation'])->where('locationCode', '[0-9]+');
        Route::get('/default', [CustomerAddressController::class, 'getDefault']);
        
        // Write operations
        Route::post('/', [CustomerAddressController::class, 'store']);
        Route::put('/{id}', [CustomerAddressController::class, 'update'])->where('id', '[0-9]+');
        Route::patch('/{id}/set-default', [CustomerAddressController::class, 'setDefault'])->where('id', '[0-9]+');
        Route::delete('/{id}', [CustomerAddressController::class, 'destroy'])->where('id', '[0-9]+');
    });

    // Menu Management (production endpoints with authentication)
    Route::prefix('menu')->middleware(['auth.token'])->group(function () {
        Route::get('/type', [MenuController::class, 'getMenuType']);
        Route::put('/type', [MenuController::class, 'updateMenuType']);
        Route::get('/types', [MenuController::class, 'listMenuTypes']);
    });

    // Product Menu (production endpoints with authentication)
    Route::prefix('product-menu')->middleware(['auth.token'])->group(function () {
        Route::get('/', [ProductMenuController::class, 'getProductMenu']);
    });

    // Public customer addresses endpoints (no authentication required)
    Route::prefix('public')->group(function () {
        Route::get('/customer-addresses', [CustomerAddressController::class, 'index']);
        Route::get('/customer-addresses/{id}', [CustomerAddressController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/customer-addresses/stats', [CustomerAddressController::class, 'stats']);
        Route::get('/customer-addresses/location/{locationCode}', [CustomerAddressController::class, 'getByLocation'])->where('locationCode', '[0-9]+');
        Route::get('/customer-addresses/default', [CustomerAddressController::class, 'getDefault']);
        
        // Also keep existing student-addresses for backward compatibility
        Route::get('/student-addresses', [StudentAddressController::class, 'index']);
        Route::get('/student-addresses/{id}', [StudentAddressController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/student-addresses/stats', [StudentAddressController::class, 'stats']);
        Route::get('/student-addresses/location/{locationCode}', [StudentAddressController::class, 'getByLocation'])->where('locationCode', '[0-9]+');
    });
});

/*
|--------------------------------------------------------------------------
| Delivery Locations API (Schools Management)
|--------------------------------------------------------------------------
|
| Complete CRUD API for managing delivery locations (schools) in the
| live_quickserve_8163 database. All operations are scoped to company_id
| from config. These APIs are compatible with the delivery_locations table.
|
*/

// Test routes for development (without authentication)
Route::prefix('test')->group(function () {
    // Delivery Locations (Test endpoints - no authentication)
    Route::get('/delivery-locations', [DeliveryLocationController::class, 'index']);
    Route::get('/delivery-locations/{id}', [DeliveryLocationController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/delivery-locations/stats', [DeliveryLocationController::class, 'stats']);
    
    // Write operations
    Route::post('/delivery-locations', [DeliveryLocationController::class, 'store']);
    Route::put('/delivery-locations/{id}', [DeliveryLocationController::class, 'update'])->where('id', '[0-9]+');
    Route::patch('/delivery-locations/{id}/toggle-status', [DeliveryLocationController::class, 'toggleStatus'])->where('id', '[0-9]+');
    Route::delete('/delivery-locations/{id}', [DeliveryLocationController::class, 'destroy'])->where('id', '[0-9]+');

    // Student Addresses (Test endpoints - no authentication)
    Route::get('/student-addresses', [StudentAddressController::class, 'index']);
    Route::get('/student-addresses/{id}', [StudentAddressController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/student-addresses/stats', [StudentAddressController::class, 'stats']);
    Route::get('/student-addresses/location/{locationCode}', [StudentAddressController::class, 'getByLocation'])->where('locationCode', '[0-9]+');
    
    // Write operations
    Route::post('/student-addresses', [StudentAddressController::class, 'store']);
    Route::put('/student-addresses/{id}', [StudentAddressController::class, 'update'])->where('id', '[0-9]+');
    Route::delete('/student-addresses/{id}', [StudentAddressController::class, 'destroy'])->where('id', '[0-9]+');

    // Customer Addresses (Test endpoints - no authentication)
    Route::get('/customer-addresses', [CustomerAddressController::class, 'index']);
    Route::get('/customer-addresses/{id}', [CustomerAddressController::class, 'show'])->where('id', '[0-9]+');
    Route::get('/customer-addresses/stats', [CustomerAddressController::class, 'stats']);
    Route::get('/customer-addresses/location/{locationCode}', [CustomerAddressController::class, 'getByLocation'])->where('locationCode', '[0-9]+');
    Route::get('/customer-addresses/default', [CustomerAddressController::class, 'getDefault']);
    
    // Write operations
    Route::post('/customer-addresses', [CustomerAddressController::class, 'store']);
    Route::put('/customer-addresses/{id}', [CustomerAddressController::class, 'update'])->where('id', '[0-9]+');
    Route::patch('/customer-addresses/{id}/set-default', [CustomerAddressController::class, 'setDefault'])->where('id', '[0-9]+');
    Route::delete('/customer-addresses/{id}', [CustomerAddressController::class, 'destroy'])->where('id', '[0-9]+');

    // Menu Management (Test endpoints - no authentication)
    Route::get('/menu/type', [MenuController::class, 'getMenuType']);
    Route::put('/menu/type', [MenuController::class, 'updateMenuType']);
    Route::get('/menu/types', [MenuController::class, 'listMenuTypes']);

    // Product Menu (Test endpoints - no authentication)
    Route::get('/product-menu', [ProductMenuController::class, 'getProductMenu']);
});

// V2 API routes (with authentication)
Route::prefix('v2')->group(function () {
    // Delivery Locations management
    Route::prefix('delivery-locations')->middleware(['auth.token'])->group(function () {
        // Read operations
        Route::get('/', [DeliveryLocationController::class, 'index']);
        Route::get('/{id}', [DeliveryLocationController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/statistics/summary', [DeliveryLocationController::class, 'stats']);
        
        // Write operations
        Route::post('/', [DeliveryLocationController::class, 'store']);
        Route::put('/{id}', [DeliveryLocationController::class, 'update'])->where('id', '[0-9]+');
        Route::patch('/{id}/toggle-status', [DeliveryLocationController::class, 'toggleStatus'])->where('id', '[0-9]+');
        Route::delete('/{id}', [DeliveryLocationController::class, 'destroy'])->where('id', '[0-9]+');
    });

    // Public delivery locations endpoints (no authentication required)
    Route::prefix('public')->group(function () {
        Route::get('/delivery-locations', [DeliveryLocationController::class, 'index']);
        Route::get('/delivery-locations/{id}', [DeliveryLocationController::class, 'show'])->where('id', '[0-9]+');
        Route::get('/delivery-locations/stats', [DeliveryLocationController::class, 'stats']);
    });
});

/*
|--------------------------------------------------------------------------
| WORKING APIs DOCUMENTATION
|--------------------------------------------------------------------------
|
| This section documents all the working and tested APIs in this service.
| Last Updated: July 2, 2025
| Database: live_quickserve_8163
| Company ID: 8163 (configured via .env and config/company.php)
|
*/

/**
 * ========================================================================
 * HEALTH CHECK API
 * ========================================================================
 * Base URL: /api
 */
// GET /api/health - Health check endpoint

/**
 * ========================================================================
 * CUSTOMER ADDRESSES APIs (Student Profiles Management) - FULLY TESTED ✅
 * ========================================================================
 * Base URL: /api
 * Description: Complete CRUD API for managing customer addresses with student profiles
 * Database Table: customer_address
 * Company Scope: All operations filtered by company_id (8163)
 * 
 * Student Profile Format in location_address:
 * "Child Name, Class, Division, Floor, Allergies"
 * Where allergies are separated by " - " (e.g., "Wheat - Lactose - Nuts")
 * 
 * OpenAPI Spec: customer-addresses-openapi.yaml
 * Test Script: test_crud_customer_addresses.sh
 */

// TEST ENDPOINTS (No Authentication Required)
// GET    /api/test/customer-addresses                    - List all customer addresses (paginated)
// POST   /api/test/customer-addresses                    - Create new customer address (student profile)
// GET    /api/test/customer-addresses/{id}               - Get specific customer address
// PUT    /api/test/customer-addresses/{id}               - Update customer address
// DELETE /api/test/customer-addresses/{id}               - Delete customer address
// GET    /api/test/customer-addresses/stats              - Get customer address statistics
// GET    /api/test/customer-addresses/location/{locationCode} - Get addresses by school/location

// PRODUCTION ENDPOINTS (Authentication Required)
// GET    /api/v2/customer-addresses                      - List all customer addresses (paginated)
// POST   /api/v2/customer-addresses                      - Create new customer address
// GET    /api/v2/customer-addresses/{id}                 - Get specific customer address
// PUT    /api/v2/customer-addresses/{id}                 - Update customer address
// DELETE /api/v2/customer-addresses/{id}                 - Delete customer address
// GET    /api/v2/customer-addresses/statistics/summary   - Get customer address statistics
// GET    /api/v2/customer-addresses/location/{locationCode} - Get addresses by school/location

// PUBLIC ENDPOINTS (No Authentication Required)
// GET    /api/v2/public/customer-addresses               - List all customer addresses (paginated)
// GET    /api/v2/public/customer-addresses/{id}          - Get specific customer address
// GET    /api/v2/public/customer-addresses/stats         - Get customer address statistics
// GET    /api/v2/public/customer-addresses/location/{locationCode} - Get addresses by school/location

/**
 * Query Parameters for Listing APIs:
 * - customer_id: Filter by customer ID (default: 3787)
 * - per_page: Number of items per page (default: 15, max: 100)
 * - menu_type: Filter by menu type (school for student addresses)
 * - location_code: Filter by delivery location/school ID
 * - search: Search in location name and location address
 * - city: Filter by city code
 * - show_all: Show all records regardless of customer filter (admin only)
 * 
 * Student Profile Fields (for menu_type='school'):
 * - child_name: Name of the student
 * - class: Student's class
 * - division: Student's division/section
 * - floor: Floor information
 * - allergies: Array of allergies (encoded as "Allergy1 - Allergy2 - Allergy3")
 * 
 * Response Format:
 * {
 *   "success": true,
 *   "data": {
 *     "pk_customer_address_code": 27221,
 *     "fk_customer_code": 3787,
 *     "menu_type": "school",
 *     "location_name": "Avalon Heights International School, Vashi",
 *     "location_address": "Priya Patel, 7, B, 1st Floor, Nuts - Shellfish",
 *     "student_profile": {
 *       "child_name": "Priya Patel",
 *       "class": "7",
 *       "division": "B",
 *       "floor": "1st Floor",
 *       "allergies": ["Nuts", "Shellfish"]
 *     },
 *     "child_name": "Priya Patel",
 *     "class": "7",
 *     "division": "B",
 *     "floor": "1st Floor",
 *     "allergies": ["Nuts", "Shellfish"],
 *     "has_allergies": true,
 *     ...
 *   },
 *   "meta": {
 *     "customer_id": 3787,
 *     "timestamp": "2025-07-03T07:27:36Z",
 *     "api_version": "v2"
 *   }
 * }
 */

// TEST ENDPOINTS (No Authentication Required)
// GET    /api/test/delivery-locations                    - List all delivery locations (paginated)
// POST   /api/test/delivery-locations                    - Create new delivery location
// GET    /api/test/delivery-locations/{id}               - Get specific delivery location
// PUT    /api/test/delivery-locations/{id}               - Update delivery location
// DELETE /api/test/delivery-locations/{id}               - Delete delivery location
// PATCH  /api/test/delivery-locations/{id}/toggle-status - Toggle active/inactive status
// GET    /api/test/delivery-locations/stats              - Get delivery locations statistics

// PRODUCTION ENDPOINTS (Authentication Required)
// GET    /api/v2/delivery-locations                      - List all delivery locations (paginated)
// POST   /api/v2/delivery-locations                      - Create new delivery location
// GET    /api/v2/delivery-locations/{id}                 - Get specific delivery location
// PUT    /api/v2/delivery-locations/{id}                 - Update delivery location
// DELETE /api/v2/delivery-locations/{id}                 - Delete delivery location
// PATCH  /api/v2/delivery-locations/{id}/toggle-status   - Toggle active/inactive status
// GET    /api/v2/delivery-locations/statistics/summary   - Get delivery locations statistics

// PUBLIC ENDPOINTS (No Authentication Required)
// GET    /api/v2/public/delivery-locations               - List all delivery locations (paginated)
// GET    /api/v2/public/delivery-locations/{id}          - Get specific delivery location
// GET    /api/v2/public/delivery-locations/stats         - Get delivery locations statistics

/**
 * Query Parameters for Listing APIs:
 * - per_page: Number of items per page (default: 15, max: 100)
 * - status: Filter by status (0=Inactive, 1=Active)
 * - search: Search in location name
 * - city: Filter by city name
 * 
 * Response Format:
 * {
 *   "success": true,
 *   "data": [...],
 *   "meta": {
 *     "current_page": 1,
 *     "per_page": 15,
 *     "total": 50,
 *     "last_page": 4,
 *     "company_id": 8163,
 *     "timestamp": "2025-07-02T10:30:00Z",
 *     "api_version": "v2"
 *   }
 * }
 */

/**
 * ========================================================================
 * PARENT APIs (School Tiffin System) - REFACTORED ✅
 * ========================================================================
 * Base URL: /api
 * Description: Parent management for school tiffin system
 * Database Tables: customers, school_registrations
 * Company Scope: All operations filtered by company_id (8163)
 */

// TEST ENDPOINTS (No Authentication Required)
// GET    /api/test/parents/{parentId}/children           - Get children for a parent
// POST   /api/test/parents/{parentId}/children           - Add child for a parent
// PUT    /api/test/parents/{parentId}/children/{childId} - Update child information
// DELETE /api/test/parents/{parentId}/children/{childId} - Remove child
// GET    /api/test/schools/available                     - Get available schools (uses delivery_locations)
// GET    /api/test/schools/summary                       - Get schools summary (uses delivery_locations)
// GET    /api/test/customer/{userId}/{companyId}         - Get customer by user_id and company_id

// V2 ENDPOINTS
// Public (No Authentication)
// POST   /api/v2/parents/register                        - Register new parent
// GET    /api/v2/parents/schools/available               - Get available schools
// GET    /api/v2/parents/schools/summary                 - Get schools summary

// Protected (Authentication Required)
// GET    /api/v2/parents/profile                         - Get parent profile
// GET    /api/v2/parents/children                        - Get parent's children
// POST   /api/v2/parents/children                        - Add child
// PUT    /api/v2/parents/children/{childId}              - Update child
// DELETE /api/v2/parents/children/{childId}              - Remove child

// Backward Compatibility Routes
// GET    /api/v2/parents/{parentId}/profile              - Get parent profile
// GET    /api/v2/parents/{parentId}/dashboard            - Get dashboard summary
// POST   /api/v2/parents/{parentId}/verify               - Verify parent
// GET    /api/v2/parents/{parentId}/children             - Get children
// POST   /api/v2/parents/{parentId}/children             - Add child
// PUT    /api/v2/parents/{parentId}/children/{childId}   - Update child
// DELETE /api/v2/parents/{parentId}/children/{childId}   - Remove child

/**
 * ========================================================================
 * CUSTOMER APIs (Core Customer Management) - REFACTORED ✅
 * ========================================================================
 * Base URL: /api
 * Description: Complete customer management system
 * Database Table: customers
 * Company Scope: All operations filtered by company_id (8163)
 */

// V1 ENDPOINTS (Backward Compatibility)
// GET    /api/v1/customers                               - List customers
// POST   /api/v1/customers                               - Create customer
// GET    /api/v1/customers/{id}                          - Get customer
// PUT    /api/v1/customers/{id}                          - Update customer
// DELETE /api/v1/customers/{id}                          - Delete customer
// POST   /api/v1/customers/{id}/addresses                - Add customer address
// PUT    /api/v1/customers/{id}/addresses/{addressId}    - Update customer address
// DELETE /api/v1/customers/{id}/addresses/{addressId}    - Delete customer address

// V2 ENDPOINTS (Full Featured)
// Core CRUD
// GET    /api/v2/customers                               - List customers
// POST   /api/v2/customers                               - Create customer
// GET    /api/v2/customers/{id}                          - Get customer
// PUT    /api/v2/customers/{id}                          - Update customer
// DELETE /api/v2/customers/{id}                          - Delete customer

// Search & Lookup
// GET    /api/v2/customers/search                        - Search customers
// GET    /api/v2/customers/phone/{phone}                 - Get by phone
// GET    /api/v2/customers/email/{email}                 - Get by email
// GET    /api/v2/customers/code/{code}                   - Get by code
// POST   /api/v2/customers/lookup                        - Lookup customer
// POST   /api/v2/customers/verify                        - Verify customer

// Profile Management
// POST   /api/v2/customers/{id}/profile                  - Update profile
// POST   /api/v2/customers/{id}/preferences              - Update preferences
// GET    /api/v2/customers/{id}/preferences              - Get preferences
// POST   /api/v2/customers/{id}/avatar                   - Upload avatar

// Authentication & Verification
// POST   /api/v2/customers/{id}/otp/send                 - Send OTP
// POST   /api/v2/customers/{id}/otp/verify               - Verify OTP
// POST   /api/v2/customers/{id}/phone/verify             - Verify phone
// POST   /api/v2/customers/{id}/email/verify             - Verify email
// POST   /api/v2/customers/{id}/password/change          - Change password
// POST   /api/v2/customers/password/reset                - Reset password

// Status Management
// POST   /api/v2/customers/{id}/activate                 - Activate customer
// POST   /api/v2/customers/{id}/deactivate               - Deactivate customer
// POST   /api/v2/customers/{id}/suspend                  - Suspend customer
// POST   /api/v2/customers/{id}/unsuspend                - Unsuspend customer

// Relationships
// GET    /api/v2/customers/{id}/orders                   - Get customer orders
// GET    /api/v2/customers/{id}/payments                 - Get customer payments
// GET    /api/v2/customers/{id}/subscriptions            - Get customer subscriptions
// GET    /api/v2/customers/{id}/notifications            - Get customer notifications
// GET    /api/v2/customers/{id}/activity                 - Get customer activity

// Analytics
// GET    /api/v2/customers/{id}/statistics               - Get customer statistics
// GET    /api/v2/customers/{id}/insights                 - Get customer insights
// GET    /api/v2/customers/analytics/summary             - Get analytics summary
// GET    /api/v2/customers/analytics/demographics        - Get demographics

// Bulk Operations
// POST   /api/v2/customers/bulk/import                   - Bulk import customers
// POST   /api/v2/customers/bulk/export                   - Bulk export customers
// POST   /api/v2/customers/bulk/update                   - Bulk update customers
// POST   /api/v2/customers/bulk/delete                   - Bulk delete customers
// POST   /api/v2/customers/bulk/notify                   - Bulk notify customers

// Address Management
// GET    /api/v2/customers/{id}/addresses                - Get customer addresses
// POST   /api/v2/customers/{id}/addresses                - Add customer address
// PUT    /api/v2/customers/{id}/addresses/{addressId}    - Update customer address
// DELETE /api/v2/customers/{id}/addresses/{addressId}    - Delete customer address
// POST   /api/v2/customers/{id}/addresses/{addressId}/default - Set default address
// GET    /api/v2/customers/{id}/addresses/dynamic/default - Get default address

// User ID Based Operations
// GET    /api/v2/customers/by-user                       - Get customer by user_id and company_id
// PUT    /api/v2/customers/by-user/{user_id}             - Update customer by user_id
// DELETE /api/v2/customers/by-user/{user_id}             - Delete customer by user_id

// Sync Operations (Called by Auth Service)
// POST   /api/v2/customers/sync                          - Sync customer data
// PUT    /api/v2/customers/sync/{userId}                 - Update customer sync
// GET    /api/v2/customers/user/{userId}/company/{companyId} - Get customer by user and company

/**
 * ========================================================================
 * WALLET APIs (Customer Wallet Management)
 * ========================================================================
 * Base URL: /api
 * Description: Wallet management for customers
 * Database Table: customer_wallets
 * Company Scope: All operations filtered by company_id (8163)
 */

// Customer Wallet Routes
// GET    /api/v2/customers/{id}/wallet                   - Get customer wallet
// POST   /api/v2/customers/{id}/wallet/deposit           - Deposit to wallet
// POST   /api/v2/customers/{id}/wallet/withdraw          - Withdraw from wallet
// GET    /api/v2/customers/{id}/wallet/transactions      - Get wallet transactions
// GET    /api/v2/customers/{id}/wallet/balance           - Get wallet balance
// POST   /api/v2/customers/{id}/wallet/transfer          - Transfer funds
// POST   /api/v2/customers/{id}/wallet/freeze            - Freeze wallet
// POST   /api/v2/customers/{id}/wallet/unfreeze          - Unfreeze wallet
// GET    /api/v2/customers/{id}/wallet/history           - Get wallet history

// Direct Wallet Routes
// GET    /api/v2/wallet/{customerId}                     - Get wallet details
// POST   /api/v2/wallet/add                              - Add funds to wallet
// POST   /api/v2/wallet/deduct                           - Deduct funds from wallet
// GET    /api/v2/wallet/{customerId}/transactions        - Get wallet transactions
// GET    /api/v2/wallet/{customerId}/balance             - Get wallet balance
// POST   /api/v2/wallet/transfer                         - Transfer between wallets
// GET    /api/v2/wallet/history                          - Get all wallet history
// GET    /api/v2/wallet/statistics                       - Get wallet statistics

/**
 * ========================================================================
 * PAYMENT MODE APIs (Customer Payment Management)
 * ========================================================================
 * Base URL: /api
 * Description: Payment mode management for customers
 * Database Table: customer_payment_modes
 * Company Scope: All operations filtered by company_id (8163)
 */

// Customer Payment Mode Routes
// GET    /api/v2/customers/{id}/payment-mode             - Get customer payment mode
// PUT    /api/v2/customers/{id}/payment-mode             - Update customer payment mode

// Payment Mode Management
// GET    /api/v2/payment-modes/statistics                - Get payment mode statistics
// POST   /api/v2/payment-modes/bulk-update               - Bulk update payment modes

/**
 * ========================================================================
 * DEBUG & DEVELOPMENT APIs
 * ========================================================================
 * Base URL: /api/test
 * Description: Debug and development endpoints
 */

// Debug Routes
// GET    /api/test/debug/customers                       - Debug customers data
// GET    /api/test/debug/children                        - Debug children data

/**
 * ========================================================================
 * CONFIGURATION & ENVIRONMENT
 * ========================================================================
 * 
 * Database Configuration:
 * - Host: 127.0.0.1
 * - Port: 3306
 * - Database: live_quickserve_8163
 * - Username: root
 * - Password: Automation@321
 * 
 * Company Configuration:
 * - Company ID: 8163 (from config/company.php)
 * - Unit ID: 1 (from config/company.php)
 * 
 * Authentication:
 * - Test endpoints: No authentication required
 * - V1/V2 endpoints: Token-based authentication (JWT from auth-service-v12)
 * - Public endpoints: No authentication required
 * 
 * Models:
 * - DeliveryLocation: app/Models/DeliveryLocation.php (NEW)
 * - Customer: app/Models/Customer.php
 * - Other models as per existing structure
 * 
 * Controllers:
 * - DeliveryLocationController: app/Http/Controllers/Api/DeliveryLocationController.php (NEW)
 * - ParentController: app/Http/Controllers/Api/ParentController.php (REFACTORED)
 * - CustomerController: app/Http/Controllers/Api/CustomerController.php
 * - WalletController: app/Http/Controllers/Api/WalletController.php
 * - PaymentModeController: app/Http/Controllers/Api/V2/PaymentModeController.php
 * 
 * Testing:
 * - CRUD Test Script: test_crud_delivery_locations.sh
 * - Manual Testing: All delivery locations endpoints tested and working
 * 
 * Documentation:
 * - OpenAPI Spec: docs/openapi-delivery-locations.yaml
 * - API Documentation: docs/README.md
 * 
 * Last Updated: July 2, 2025
 * Status: All delivery locations APIs tested and working ✅
 * Next Steps: Apply same pattern to other v12 services if required
 */

// JWT Authentication Examples for Customer Service v12
use App\Http\Controllers\Api\V1\CustomerController as JwtCustomerController;

// Routes requiring JWT authentication (no specific roles)
Route::middleware(['jwt.auth'])->group(function () {
    Route::get('/v1/customer/profile', [JwtCustomerController::class, 'profile']);
    Route::get('/v1/customer/token-info', [JwtCustomerController::class, 'tokenInfo']);
    Route::get('/v1/customer/old-sso-mapping', [JwtCustomerController::class, 'oldSsoMapping']);
    Route::get('/v1/customer/health', [JwtCustomerController::class, 'healthCheck']);
});

// Routes requiring admin role
Route::middleware(['jwt.auth:admin'])->group(function () {
    Route::get('/v1/customer/dashboard', [JwtCustomerController::class, 'dashboard']);
});

// Routes requiring specific roles
Route::middleware(['jwt.auth:manage-customers'])->group(function () {
    Route::get('/v1/customer/manage-customers', [JwtCustomerController::class, 'manageCustomers']);
});
