openapi: 3.0.3
info:
  title: Product Menu API
  description: |
    API for retrieving active products/meals based on kitchen's menu type and product categories.
    
    **Logic Flow:**
    1. Determine kitchen and its active menu type (using menu management fallback logic)
    2. Get active product categories of type "meal"
    3. Filter active products that match both the kitchen's menu type and product categories
    4. Bifurcate products by category name for organized display
    
    **Key Features:**
    - Kitchen-based menu type filtering (breakfast, lunch, dinner)
    - Product category organization (Subscription Meals, etc.)
    - Active product filtering (status = 1)
    - Category-wise product bifurcation
    - Comprehensive product information including pricing and food types
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: http://localhost:8010/api
    description: Local development server
  - url: https://api.onefooddialer.com/api
    description: Production server

paths:
  /test/product-menu:
    get:
      summary: Get product menu for a location
      description: |
        Retrieve active products/meals based on the kitchen's menu type and organized by categories.
        
        **Menu Type Filtering:**
        - If kitchen has menu type "breakfast,lunch", only products with category containing "breakfast" or "lunch" are shown
        - If kitchen has menu type "breakfast", only breakfast products are shown
        - Uses the same fallback logic as menu management (kitchen-specific → global → null)
        
        **Category Organization:**
        - Products are grouped by meal types (breakfast, lunch, dinner) from their category field
        - Only active categories (status = 1) of type "meal" are included
        - Each meal type shows only products that match that specific meal category
      tags:
        - Product Menu (Test)
      parameters:
        - name: city
          in: query
          description: City name to filter delivery locations
          schema:
            type: string
            maxLength: 100
            example: "Mumbai"
        - name: delivery_location
          in: query
          description: Delivery location name (partial match supported)
          schema:
            type: string
            maxLength: 255
            example: "School A"
        - name: delivery_location_id
          in: query
          description: Exact delivery location ID
          schema:
            type: integer
            minimum: 1
            example: 1
      responses:
        '200':
          description: Product menu retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Product menu retrieved successfully"
                  data:
                    type: object
                    properties:
                      delivery_location:
                        type: object
                        properties:
                          id:
                            type: integer
                            example: 1
                          name:
                            type: string
                            example: "School A"
                          city:
                            type: string
                            example: "Mumbai"
                          fk_kitchen_code:
                            type: integer
                            example: 101
                      kitchen:
                        type: object
                        properties:
                          code:
                            type: integer
                            example: 101
                          name:
                            type: string
                            example: "Central Kitchen"
                          alias:
                            type: string
                            example: "K1"
                      menu_type:
                        type: string
                        description: Active menu type for this kitchen (comma-separated)
                        example: "breakfast,lunch"
                      meal_types:
                        type: array
                        description: Products organized by meal types (breakfast, lunch, dinner)
                        items:
                          type: object
                          properties:
                            meal_type:
                              type: string
                              example: "breakfast"
                            meal_type_display:
                              type: string
                              example: "Breakfast"
                            product_count:
                              type: integer
                              description: Number of products for this meal type
                              example: 4
                            products:
                              type: array
                              items:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    description: Product ID
                                    example: 597
                                  name:
                                    type: string
                                    description: Product name
                                    example: "Jain Breakfast"
                                  description:
                                    type: string
                                    description: Product description
                                    example: "Jain Meals Exclude Underground Roots"
                                  unit_price:
                                    type: string
                                    format: decimal
                                    description: Product price
                                    example: "75.00"
                                  category:
                                    type: string
                                    description: Meal categories (comma-separated)
                                    example: "breakfast"
                                  product_type:
                                    type: string
                                    description: Type of product
                                    example: "Meal"
                                  food_type:
                                    type: string
                                    enum: ["veg", "nonveg", "egg"]
                                    description: Food type classification
                                    example: "veg"
                                  image_path:
                                    type: string
                                    nullable: true
                                    description: Full S3 URL of the product image
                                    example: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/jain_breakfast.png"
                                  is_custom:
                                    type: integer
                                    description: Whether product is customizable (0=no, 1=yes)
                                    example: 0
                                  sequence:
                                    type: integer
                                    nullable: true
                                    description: Display order within category
                                    example: 1
                                  product_category_name:
                                    type: string
                                    description: Original product category name
                                    example: "Subscription Meals"
                      summary:
                        type: object
                        properties:
                          total_meal_types:
                            type: integer
                            description: Total number of meal types with products
                            example: 1
                          total_products:
                            type: integer
                            description: Total number of products across all meal types
                            example: 4
              examples:
                breakfast_only:
                  summary: Response with breakfast-only menu (K1_MENU_TYPE = breakfast)
                  value:
                    success: true
                    message: "Product menu retrieved successfully"
                    data:
                      delivery_location:
                        id: 1
                        name: "School A"
                        city: "Mumbai"
                        fk_kitchen_code: 1
                      kitchen:
                        code: 1
                        name: "Kitchen 1"
                        alias: "K1"
                      menu_type: "breakfast"
                      meal_types:
                        - meal_type: "breakfast"
                          meal_type_display: "Breakfast"
                          product_count: 4
                          products:
                            - id: 597
                              name: "Jain Breakfast"
                              description: "Jain Meals Exclude Underground Roots"
                              unit_price: "75.00"
                              category: "breakfast"
                              product_type: "Meal"
                              food_type: "veg"
                              image_path: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/jain_breakfast.png"
                              is_custom: 0
                              sequence: null
                              product_category_name: "Subscription Meals"
                      summary:
                        total_meal_types: 1
                        total_products: 4
                full_menu:
                  summary: Response with breakfast and lunch menu
                  value:
                    success: true
                    message: "Product menu retrieved successfully"
                    data:
                      delivery_location:
                        id: 1
                        name: "School A"
                        city: "Mumbai"
                        fk_kitchen_code: 101
                      kitchen:
                        code: 101
                        name: "Central Kitchen"
                        alias: "K1"
                      menu_type: "breakfast,lunch"
                      meal_types:
                        - meal_type: "breakfast"
                          meal_type_display: "Breakfast"
                          product_count: 4
                          products:
                            - id: 597
                              name: "Jain Breakfast"
                              description: "Jain Meals Exclude Underground Roots"
                              unit_price: "75.00"
                              category: "breakfast"
                              product_type: "Meal"
                              food_type: "veg"
                              image_path: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/jain_breakfast.png"
                              is_custom: 0
                              sequence: null
                              product_category_name: "Subscription Meals"
                        - meal_type: "lunch"
                          meal_type_display: "Lunch"
                          product_count: 3
                          products:
                            - id: 598
                              name: "Jain Lunch"
                              description: "Jain Lunch Menu"
                              unit_price: "125.00"
                              category: "lunch"
                              product_type: "Meal"
                              food_type: "veg"
                              image_path: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/jain_lunch.png"
                              is_custom: 0
                              sequence: null
                              product_category_name: "Subscription Meals"
                      summary:
                        total_meal_types: 2
                        total_products: 7
        '404':
          description: No active delivery location found or no menu type configured
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/product-menu:
    get:
      summary: Get product menu for a location (Production)
      description: |
        Production endpoint for retrieving product menu.
        Requires authentication token.
      tags:
        - Product Menu (Production)
      security:
        - BearerAuth: []
      parameters:
        - name: city
          in: query
          description: City name to filter delivery locations
          schema:
            type: string
            maxLength: 100
        - name: delivery_location
          in: query
          description: Delivery location name
          schema:
            type: string
            maxLength: 255
        - name: delivery_location_id
          in: query
          description: Exact delivery location ID
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Product menu retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/paths/~1test~1product-menu/get/responses/200/content/application~1json/schema'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: No active delivery location found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token from auth-service-v12

  schemas:
    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Operation failed"
        error:
          type: string
          example: "Detailed error message"
        data:
          type: object
          nullable: true
          description: Additional error context

    ValidationErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Validation failed"
        errors:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          example:
            delivery_location_id: ["The delivery location id field must be an integer."]
            city: ["The city may not be greater than 100 characters."]

tags:
  - name: Product Menu (Test)
    description: Test endpoints for product menu (no authentication required)
  - name: Product Menu (Production)
    description: Production endpoints for product menu (authentication required)
