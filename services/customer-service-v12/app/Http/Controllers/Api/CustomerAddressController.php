<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CustomerAddress;
use App\Models\DeliveryLocation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

/**
 * Customer Address Controller
 * 
 * Handles customer addresses including student profiles stored in the customer_address table.
 * Student profiles are stored with menu_type='school' and student details in location_address field.
 * 
 * Student profile format in location_address:
 * "Child Name, Class, Division, Floor, Allergies"
 * Where allergies are separated by " - " (e.g., "Wheat - Lactose - Nuts")
 */
class CustomerAddressController extends Controller
{
    /**
     * Get all customer addresses for a specific customer
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            $perPage = $request->input('per_page', 15);
            $perPage = min($perPage, 100); // Limit max results
            
            $query = CustomerAddress::query();
            
            // Apply customer filter unless show_all is true and user has admin access
            if (!$request->has('show_all') || $request->input('show_all') !== 'true') {
                $query->where('fk_customer_code', $customerId);
            }

            // Apply filters
            if ($request->has('menu_type')) {
                $query->where('menu_type', $request->input('menu_type'));
            }

            if ($request->has('location_code')) {
                $query->where('location_code', $request->input('location_code'));
            }

            // Search functionality (only in existing columns)
            if ($request->has('search')) {
                $search = $request->input('search');
                $query->where(function ($q) use ($search) {
                    $q->where('location_name', 'LIKE', "%{$search}%")
                      ->orWhere('location_address', 'LIKE', "%{$search}%");
                });
            }

            if ($request->has('city')) {
                $query->where('city', $request->input('city'));
            }

            if ($request->has('default')) {
                $query->where('default', (bool)$request->input('default'));
            }
            
            $addresses = $query->orderBy('default', 'desc')
                              ->orderBy('pk_customer_address_code', 'desc')
                              ->paginate($perPage);

            // The student profile transformation is now handled automatically by the model's accessors
            $transformedAddresses = $addresses->items();

            return response()->json([
                'success' => true,
                'data' => $transformedAddresses,
                'meta' => [
                    'current_page' => $addresses->currentPage(),
                    'per_page' => $addresses->perPage(),
                    'total' => $addresses->total(),
                    'last_page' => $addresses->lastPage(),
                    'customer_id' => $customerId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get customer addresses', $e);
        }
    }

    /**
     * Create a new customer address
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // All addresses are student addresses (school tiffin system)
            return $this->storeStudentAddress($request);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create customer address', $e);
        }
    }

    /**
     * Store a student address (school type)
     */
    private function storeStudentAddress(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'customer_id' => 'required|integer',
            'location_code' => 'required|integer|exists:delivery_locations,pk_location_code',
            'child_name' => 'required|string|max:100',
            'class' => 'required|string|max:10',
            'division' => 'required|string|max:10',
            'floor' => 'required|string|max:50',
            'allergies' => 'nullable|array',
            'allergies.*' => 'string|max:50',
            'menu_type' => 'nullable|string|in:school',
            'city' => 'nullable|string',
            'default' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $validator->validated();
        $customerId = $data['customer_id'];
        
        // Get delivery location details
        $deliveryLocation = DeliveryLocation::where('pk_location_code', $data['location_code'])
            ->where('company_id', config('company.id'))
            ->first();
            
        if (!$deliveryLocation) {
            return response()->json([
                'success' => false,
                'message' => 'Delivery location not found or not accessible',
            ], 404);
        }

        // Build location_address string from student profile
        $locationAddressParts = [
            $data['child_name'],
            $data['class'],
            $data['division'],
            $data['floor'],
        ];

        // Add allergies if provided
        if (!empty($data['allergies']) && is_array($data['allergies'])) {
            $locationAddressParts[] = implode(' - ', $data['allergies']);
        }

        $locationAddress = implode(', ', array_filter($locationAddressParts));

        // Prepare address data
        $addressData = [
            'fk_customer_code' => $customerId,
            'company_id' => config('company.id'),
            'unit_id' => config('company.unit_id'),
            'menu_type' => 'school',
            'city' => $data['city'] ?? '9',
            'location_code' => $data['location_code'],
            'location_name' => $deliveryLocation->location,
            'location_address' => $locationAddress,
            'location_zipcode' => $deliveryLocation->pin,
            'default' => $data['default'] ?? false,
        ];
        
        $address = CustomerAddress::create($addressData);

        // Transform response to include student profile
        $transformedAddress = $this->transformStudentAddress($address);

        return response()->json([
            'success' => true,
            'message' => 'Student address created successfully',
            'data' => $transformedAddress,
            'meta' => [
                'customer_id' => $customerId,
                'timestamp' => now()->toISOString(),
                'api_version' => 'v2',
            ]
        ], 201);
    }

    /**
     * Get a specific customer address
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            
            $address = CustomerAddress::where('pk_customer_address_code', $id)
                ->where('fk_customer_code', $customerId)
                ->first();

            if (!$address) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer address not found',
                ], 404);
            }

            // Transform if it's a student address (company_id=8163 with comma-separated location_address)
            if (isset($address->company_id) && $address->company_id == 8163 && 
                isset($address->location_address) && strpos($address->location_address, ',') !== false) {
                $address = $this->transformStudentAddress($address);
            }

            return response()->json([
                'success' => true,
                'data' => $address,
                'meta' => [
                    'customer_id' => $customerId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get customer address', $e);
        }
    }

    /**
     * Update a customer address
     */
    public function update(Request $request, int $id): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            
            $address = CustomerAddress::where('pk_customer_address_code', $id)
                ->where('fk_customer_code', $customerId)
                ->first();

            if (!$address) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer address not found',
                ], 404);
            }

            // All addresses are student addresses in this system
            return $this->updateStudentAddress($request, $address);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update customer address', $e);
        }
    }

    /**
     * Update a student address
     */
    private function updateStudentAddress(Request $request, CustomerAddress $address): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'location_code' => 'nullable|integer|exists:delivery_locations,pk_location_code',
            'child_name' => 'nullable|string|max:100',
            'class' => 'nullable|string|max:10',
            'division' => 'nullable|string|max:10',
            'floor' => 'nullable|string|max:50',
            'allergies' => 'nullable|array',
            'allergies.*' => 'string|max:50',
            'default' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $validator->validated();

        // Update location if provided
        if (isset($data['location_code'])) {
            $deliveryLocation = DeliveryLocation::where('pk_location_code', $data['location_code'])
                ->where('company_id', config('company.id'))
                ->first();
                
            if (!$deliveryLocation) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery location not found or not accessible',
                ], 404);
            }

            $address->location_code = $data['location_code'];
            $address->location_name = $deliveryLocation->location;
            $address->location_zipcode = $deliveryLocation->pin;
        }

        // Update student profile if any student fields are provided
        if (isset($data['child_name']) || isset($data['class']) || isset($data['division']) || isset($data['floor']) || isset($data['allergies'])) {
            // Parse current location_address
            $currentProfile = $this->parseStudentProfile($address->location_address);
            
            // Update with new values
            $newProfile = [
                'child_name' => $data['child_name'] ?? $currentProfile['child_name'],
                'class' => $data['class'] ?? $currentProfile['class'],
                'division' => $data['division'] ?? $currentProfile['division'],
                'floor' => $data['floor'] ?? $currentProfile['floor'],
                'allergies' => $data['allergies'] ?? $currentProfile['allergies'],
            ];

            // Rebuild location_address
            $locationAddressParts = [
                $newProfile['child_name'],
                $newProfile['class'],
                $newProfile['division'],
                $newProfile['floor'],
            ];

            if (!empty($newProfile['allergies']) && is_array($newProfile['allergies'])) {
                $locationAddressParts[] = implode(' - ', $newProfile['allergies']);
            }

            $address->location_address = implode(', ', array_filter($locationAddressParts));
        }

        // Update other fields
        if (isset($data['default'])) {
            $address->default = $data['default'];
        }

        $address->save();

        // Transform response to include student profile
        $transformedAddress = $this->transformStudentAddress($address);

        return response()->json([
            'success' => true,
            'message' => 'Student address updated successfully',
            'data' => $transformedAddress,
            'meta' => [
                'customer_id' => $address->fk_customer_code,
                'timestamp' => now()->toISOString(),
                'api_version' => 'v2',
            ]
        ]);
    }

    /**
     * Delete a customer address
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            
            $address = CustomerAddress::where('pk_customer_address_code', $id)
                ->where('fk_customer_code', $customerId)
                ->first();

            if (!$address) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer address not found',
                ], 404);
            }

            $address->delete();

            return response()->json([
                'success' => true,
                'message' => 'Customer address deleted successfully',
                'meta' => [
                    'customer_id' => $customerId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to delete customer address', $e);
        }
    }

    /**
     * Get addresses by location/school
     */
    public function getByLocation(Request $request, int $locationCode): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            $perPage = $request->input('per_page', 15);
            
            $query = CustomerAddress::where('location_code', $locationCode);
            
            // Apply customer filter unless show_all is true
            if (!$request->has('show_all') || $request->input('show_all') !== 'true') {
                $query->where('fk_customer_code', $customerId);
            }

            $addresses = $query->orderBy('created_on', 'desc')->paginate($perPage);

            // Transform student addresses
            $transformedAddresses = $addresses->items();
            foreach ($transformedAddresses as &$address) {
                // Transform all addresses with company_id=8163 regardless of menu_type
                // as long as location_address follows the expected format
                if (isset($address->company_id) && $address->company_id == 8163 && 
                    isset($address->location_address) && strpos($address->location_address, ',') !== false) {
                    $address = $this->transformStudentAddress($address);
                }
            }

            return response()->json([
                'success' => true,
                'data' => $transformedAddresses,
                'meta' => [
                    'current_page' => $addresses->currentPage(),
                    'per_page' => $addresses->perPage(),
                    'total' => $addresses->total(),
                    'last_page' => $addresses->lastPage(),
                    'location_code' => $locationCode,
                    'customer_id' => $customerId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get addresses by location', $e);
        }
    }

    /**
     * Get customer address statistics
     */
    public function stats(Request $request): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            
            $baseQuery = CustomerAddress::query();
            
            // Apply customer filter unless show_all is true
            if (!$request->has('show_all') || $request->input('show_all') !== 'true') {
                $baseQuery->where('fk_customer_code', $customerId);
            }

            $totalAddresses = $baseQuery->count();
            $defaultAddresses = (clone $baseQuery)->where('default', true)->count();
            
            // Student addresses stats
            $studentAddresses = (clone $baseQuery)->where('menu_type', 'school')->count();
            $regularAddresses = $totalAddresses - $studentAddresses;
            
            // Location distribution
            $locationDistribution = (clone $baseQuery)->select('location_code', 'location_name')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('location_code', 'location_name')
                ->get()
                ->pluck('count', 'location_name')
                ->toArray();

            // Students with allergies
            $studentsWithAllergies = (clone $baseQuery)
                ->where('menu_type', 'school')
                ->where('location_address', 'LIKE', '%-%')
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_addresses' => $totalAddresses,
                    'default_addresses' => $defaultAddresses,
                    'student_addresses' => $studentAddresses,
                    'regular_addresses' => $regularAddresses,
                    'students_with_allergies' => $studentsWithAllergies,
                    'location_distribution' => $locationDistribution,
                ],
                'meta' => [
                    'customer_id' => $customerId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get customer address statistics', $e);
        }
    }

    /**
     * Set default address
     */
    public function setDefault(Request $request, int $id): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            
            $address = CustomerAddress::where('pk_customer_address_code', $id)
                ->where('fk_customer_code', $customerId)
                ->first();

            if (!$address) {
                return response()->json([
                    'success' => false,
                    'message' => 'Customer address not found',
                ], 404);
            }

            DB::transaction(function () use ($address, $customerId) {
                // Remove default from all other addresses
                CustomerAddress::where('fk_customer_code', $customerId)
                    ->where('pk_customer_address_code', '!=', $address->pk_customer_address_code)
                    ->update(['default' => false]);
                
                // Set this address as default
                $address->update(['default' => true]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Default address updated successfully',
                'data' => $address,
                'meta' => [
                    'customer_id' => $customerId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to set default address', $e);
        }
    }

    /**
     * Get default address
     */
    public function getDefault(Request $request): JsonResponse
    {
        try {
            // Validate required customer_id
            $validator = Validator::make($request->all(), [
                'customer_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $customerId = $request->input('customer_id');
            
            $address = CustomerAddress::where('fk_customer_code', $customerId)
                ->where('default', true)
                ->first();

            if (!$address) {
                return response()->json([
                    'success' => false,
                    'message' => 'No default address found',
                ], 404);
            }

            // Transform if it's a student address (company_id=8163 with comma-separated location_address)
            if (isset($address->company_id) && $address->company_id == 8163 && 
                isset($address->location_address) && strpos($address->location_address, ',') !== false) {
                $address = $this->transformStudentAddress($address);
            }

            return response()->json([
                'success' => true,
                'data' => $address,
                'meta' => [
                    'customer_id' => $customerId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get default address', $e);
        }
    }

    /**
     * Transform a student address to include parsed student profile
     */
    private function transformStudentAddress($address)
    {
        // Convert to array if it's a model instance
        $addressArray = is_object($address) ? $address->toArray() : $address;

        // Parse student profile from location_address
        if (isset($addressArray['location_address']) && strpos($addressArray['location_address'], ',') !== false) {
            $profile = $this->parseStudentProfile($addressArray['location_address']);
            $addressArray['student_profile'] = $profile;
        }

        return $addressArray;
    }

    /**
     * Parse student profile from location_address string
     */
    private function parseStudentProfile(string $locationAddress): array
    {
        $parts = array_map('trim', explode(',', $locationAddress));

        $profile = [
            'child_name' => $parts[0] ?? '',
            'class' => $parts[1] ?? '',
            'division' => $parts[2] ?? '',
            'floor' => $parts[3] ?? '',
            'allergies' => []
        ];

        // Parse allergies if present (5th part, separated by ' - ')
        if (isset($parts[4]) && !empty($parts[4])) {
            $profile['allergies'] = array_map('trim', explode(' - ', $parts[4]));
        }

        return $profile;
    }

    /**
     * Return error response
     */
    private function errorResponse(string $message, \Exception $e): JsonResponse
    {
        Log::error($message, [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
        ], 500);
    }
}
