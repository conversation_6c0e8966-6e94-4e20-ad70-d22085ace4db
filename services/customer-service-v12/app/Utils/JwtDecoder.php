<?php

namespace App\Utils;

use Exception;
use Illuminate\Support\Facades\Log;

/**
 * JWT Decoder Utility for Keycloak Tokens
 * 
 * This utility class provides JWT token decoding and validation functionality
 * specifically designed for Keycloak tokens without requiring external JWT packages.
 */
class JwtDecoder
{
    /**
     * Decode and validate JWT token
     *
     * @param string $token The JWT token to decode
     * @return array|null Returns decoded token data or null if invalid
     */
    public static function decode(string $token): ?array
    {
        try {
            // Split the JWT token into its three parts
            $parts = explode('.', $token);
            
            if (count($parts) !== 3) {
                Log::warning('JWT token does not have 3 parts', ['token_preview' => substr($token, 0, 50)]);
                return null;
            }

            [$header, $payload, $signature] = $parts;

            // Decode header
            $decodedHeader = self::base64UrlDecode($header);
            $headerData = json_decode($decodedHeader, true);

            if (!$headerData) {
                Log::warning('Failed to decode JWT header');
                return null;
            }

            // Decode payload
            $decodedPayload = self::base64UrlDecode($payload);
            $payloadData = json_decode($decodedPayload, true);

            if (!$payloadData) {
                Log::warning('Failed to decode JWT payload');
                return null;
            }

            // Basic validation
            if (!self::validateToken($payloadData)) {
                return null;
            }

            // Extract user information
            $userInfo = self::extractUserInfo($payloadData);

            return [
                'header' => $headerData,
                'payload' => $payloadData,
                'user' => $userInfo,
                'raw_token' => $token
            ];

        } catch (Exception $e) {
            Log::error('JWT decoding error', [
                'error' => $e->getMessage(),
                'token_preview' => substr($token, 0, 50)
            ]);
            return null;
        }
    }

    /**
     * Base64 URL decode
     *
     * @param string $data
     * @return string
     */
    private static function base64UrlDecode(string $data): string
    {
        $remainder = strlen($data) % 4;
        if ($remainder) {
            $data .= str_repeat('=', 4 - $remainder);
        }
        return base64_decode(strtr($data, '-_', '+/'));
    }

    /**
     * Validate token timing and basic structure
     *
     * @param array $payload
     * @return bool
     */
    private static function validateToken(array $payload): bool
    {
        $now = time();

        // Check expiration
        if (isset($payload['exp']) && $payload['exp'] < $now) {
            Log::warning('JWT token has expired', [
                'exp' => $payload['exp'],
                'now' => $now,
                'expired_seconds_ago' => $now - $payload['exp']
            ]);
            return false;
        }

        // Check not before
        if (isset($payload['nbf']) && $payload['nbf'] > $now) {
            Log::warning('JWT token not yet valid', [
                'nbf' => $payload['nbf'],
                'now' => $now
            ]);
            return false;
        }

        // Check issued at (allow some clock skew)
        if (isset($payload['iat']) && $payload['iat'] > ($now + 300)) {
            Log::warning('JWT token issued in the future', [
                'iat' => $payload['iat'],
                'now' => $now
            ]);
            return false;
        }

        return true;
    }

    /**
     * Extract user information from JWT payload
     *
     * @param array $payload
     * @return array
     */
    private static function extractUserInfo(array $payload): array
    {
        $userInfo = [
            'user_id' => $payload['sub'] ?? null,
            'username' => $payload['preferred_username'] ?? $payload['username'] ?? null,
            'email' => $payload['email'] ?? null,
            'email_verified' => $payload['email_verified'] ?? false,
            'first_name' => $payload['given_name'] ?? null,
            'last_name' => $payload['family_name'] ?? null,
            'full_name' => $payload['name'] ?? null,
            'client_id' => $payload['aud'] ?? $payload['azp'] ?? null,
            'realm' => null,
            'session_id' => $payload['session_state'] ?? null,
            'issued_at' => $payload['iat'] ?? null,
            'expires_at' => $payload['exp'] ?? null,
        ];

        // Extract realm from issuer
        if (isset($payload['iss'])) {
            $issuerParts = explode('/realms/', $payload['iss']);
            if (count($issuerParts) > 1) {
                $userInfo['realm'] = $issuerParts[1];
            }
        }

        // Extract Old SSO User ID from various possible locations
        $userInfo['old_sso_user_id'] = self::extractOldSsoUserId($payload);

        return $userInfo;
    }

    /**
     * Extract Old SSO User ID from JWT payload
     * Searches multiple possible locations for the Old SSO User ID
     *
     * @param array $payload
     * @return string|null
     */
    private static function extractOldSsoUserId(array $payload): ?string
    {
        // List of possible field names for Old SSO User ID
        $possibleFields = [
            'oldSsoUserId',
            'old_sso_user_id',
            'legacy_user_id',
            'external_user_id',
            'custom_user_id',
        ];

        // Check direct payload fields
        foreach ($possibleFields as $field) {
            if (isset($payload[$field]) && !empty($payload[$field])) {
                return (string) $payload[$field];
            }
        }

        // Check custom attributes
        if (isset($payload['custom_attributes'])) {
            foreach ($possibleFields as $field) {
                if (isset($payload['custom_attributes'][$field]) && !empty($payload['custom_attributes'][$field])) {
                    return (string) $payload['custom_attributes'][$field];
                }
            }
        }

        // Check other nested structures
        $nestedPaths = [
            'user_attributes',
            'attributes',
            'custom_claims',
            'user_metadata'
        ];

        foreach ($nestedPaths as $path) {
            if (isset($payload[$path]) && is_array($payload[$path])) {
                foreach ($possibleFields as $field) {
                    if (isset($payload[$path][$field]) && !empty($payload[$path][$field])) {
                        return (string) $payload[$path][$field];
                    }
                }
            }
        }

        return null;
    }

    /**
     * Extract roles from JWT payload
     *
     * @param array $payload
     * @return array
     */
    public static function extractRoles(array $payload): array
    {
        $roles = [];

        // Extract realm roles
        if (isset($payload['realm_access']['roles']) && is_array($payload['realm_access']['roles'])) {
            $roles = array_merge($roles, $payload['realm_access']['roles']);
        }

        // Extract resource/client roles
        if (isset($payload['resource_access']) && is_array($payload['resource_access'])) {
            foreach ($payload['resource_access'] as $resource => $access) {
                if (isset($access['roles']) && is_array($access['roles'])) {
                    $roles = array_merge($roles, $access['roles']);
                }
            }
        }

        return array_unique($roles);
    }

    /**
     * Check if user has specific role
     *
     * @param array $payload
     * @param string $role
     * @param string|null $resource
     * @return bool
     */
    public static function hasRole(array $payload, string $role, ?string $resource = null): bool
    {
        // Check realm roles
        if (isset($payload['realm_access']['roles']) && in_array($role, $payload['realm_access']['roles'])) {
            return true;
        }

        // Check resource-specific roles
        if ($resource && isset($payload['resource_access'][$resource]['roles'])) {
            return in_array($role, $payload['resource_access'][$resource]['roles']);
        }

        // Check all resource roles if no specific resource provided
        if (!$resource && isset($payload['resource_access'])) {
            foreach ($payload['resource_access'] as $resourceRoles) {
                if (isset($resourceRoles['roles']) && in_array($role, $resourceRoles['roles'])) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Get token expiration information
     *
     * @param array $payload
     * @return array
     */
    public static function getExpirationInfo(array $payload): array
    {
        $now = time();
        $exp = $payload['exp'] ?? null;
        $iat = $payload['iat'] ?? null;

        return [
            'expires_at' => $exp,
            'issued_at' => $iat,
            'current_time' => $now,
            'is_expired' => $exp ? ($exp < $now) : null,
            'expires_in_seconds' => $exp ? max(0, $exp - $now) : null,
            'expires_in_minutes' => $exp ? max(0, round(($exp - $now) / 60)) : null,
            'age_in_seconds' => $iat ? ($now - $iat) : null,
        ];
    }
}
