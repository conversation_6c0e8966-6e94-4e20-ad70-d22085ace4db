# JWT Middleware Guide for Admin Service v12

This guide explains how to use the custom JWT authentication middleware for Keycloak tokens in the admin-service-v12.

## Overview

The JWT middleware provides:
- **Token Validation**: Validates Keycloak JWT tokens without external packages
- **User Information Extraction**: Extracts user details including Old SSO User ID
- **Role-Based Access Control**: Supports role-based route protection
- **Request Enhancement**: Adds convenient methods to access JWT data

## Components

### 1. JwtDecoder Utility (`app/Utils/JwtDecoder.php`)
- Decodes and validates JWT tokens
- Extracts user information and roles
- Handles token expiration checks
- Extracts Old SSO User ID from custom claims

### 2. KeycloakJwtAuth Middleware (`app/Http/Middleware/KeycloakJwtAuth.php`)
- Main authentication middleware
- Validates tokens and extracts user data
- Adds authentication data to request
- Provides role-based access control

### 3. JwtAuthHelper Trait (`app/Traits/JwtAuthHelper.php`)
- Helper methods for controllers
- Easy access to user information
- Role checking utilities
- Audit logging helpers

## Usage Examples

### Basic Authentication (No Role Required)

```php
// In routes/api.php
Route::middleware(['jwt.auth'])->group(function () {
    Route::get('/profile', [AdminController::class, 'profile']);
});

// In controller
public function profile(Request $request): JsonResponse
{
    $userInfo = $this->getAuthUser($request);
    $oldSsoUserId = $this->getOldSsoUserId($request);
    
    return response()->json([
        'success' => true,
        'data' => [
            'user' => $userInfo,
            'old_sso_user_id' => $oldSsoUserId
        ]
    ]);
}
```

### Role-Based Authentication

```php
// Require admin role
Route::middleware(['jwt.auth:admin'])->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard']);
});

// Require specific role
Route::middleware(['jwt.auth:manage-users'])->group(function () {
    Route::get('/users', [AdminController::class, 'manageUsers']);
});

// Require any of multiple roles
Route::middleware(['jwt.auth:admin,manage-users,super-admin'])->group(function () {
    Route::get('/advanced', [AdminController::class, 'advanced']);
});
```

### Controller Implementation

```php
<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\JwtAuthHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminController extends Controller
{
    use JwtAuthHelper;

    public function profile(Request $request): JsonResponse
    {
        // Get user information
        $userId = $this->getCurrentUserId($request);
        $username = $this->getCurrentUsername($request);
        $email = $this->getCurrentUserEmail($request);
        $oldSsoUserId = $this->getOldSsoUserId($request);
        
        // Check roles
        $isAdmin = $this->isAdmin($request);
        $hasManageUsers = $this->hasRole($request, 'manage-users');
        
        // Get token info
        $expiresInMinutes = $this->getTokenExpiresInMinutes($request);
        $isExpired = $this->isTokenExpired($request);
        
        return response()->json([
            'success' => true,
            'data' => [
                'user_id' => $userId,
                'username' => $username,
                'email' => $email,
                'old_sso_user_id' => $oldSsoUserId,
                'is_admin' => $isAdmin,
                'has_manage_users' => $hasManageUsers,
                'token_expires_in_minutes' => $expiresInMinutes,
                'token_expired' => $isExpired
            ]
        ]);
    }
}
```

## Token Structure

The middleware expects Keycloak JWT tokens with the following structure:

```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "key-id"
  },
  "payload": {
    "sub": "user-uuid",
    "preferred_username": "username",
    "email": "<EMAIL>",
    "email_verified": true,
    "given_name": "John",
    "family_name": "Doe",
    "name": "John Doe",
    "realm_access": {
      "roles": ["admin", "user"]
    },
    "resource_access": {
      "client-name": {
        "roles": ["manage-users"]
      }
    },
    "oldSsoUserId": "12345",
    "iss": "https://keycloak.example.com/realms/realm-name",
    "aud": "client-id",
    "exp": 1640995200,
    "iat": 1640991600,
    "session_state": "session-uuid"
  }
}
```

## Old SSO User ID Extraction

The middleware automatically extracts Old SSO User ID from various possible locations:

1. Direct payload fields: `oldSsoUserId`, `old_sso_user_id`, `legacy_user_id`
2. Custom attributes: `custom_attributes.oldSsoUserId`
3. Other variations: `external_user_id`, `custom_user_id`

## Request Enhancement

After successful authentication, the middleware adds these methods to the request:

```php
// Direct access methods
$request->getAuthUser()           // Get user information
$request->getUserRoles()          // Get all roles
$request->hasRole($role)          // Check specific role
$request->isAdmin()               // Check admin status
$request->getOldSsoUserId()       // Get Old SSO User ID
$request->getTokenExpiration()    // Get expiration info
```

## Helper Trait Methods

The `JwtAuthHelper` trait provides these methods:

### User Information
- `getAuthUser($request)` - Get authenticated user info
- `getCurrentUserId($request)` - Get current user ID
- `getCurrentUsername($request)` - Get current username
- `getCurrentUserEmail($request)` - Get current user email
- `getOldSsoUserId($request)` - Get Old SSO User ID

### Role Management
- `getUserRoles($request)` - Get all user roles
- `hasRole($request, $role, $resource)` - Check specific role
- `isAdmin($request)` - Check admin status
- `getAllRoles($request)` - Get flat array of all roles
- `getRealmRoles($request)` - Get realm roles only
- `getResourceRoles($request, $resource)` - Get client/resource roles

### Token Information
- `getTokenExpiration($request)` - Get expiration info
- `isTokenExpired($request)` - Check if token is expired
- `getTokenExpiresInMinutes($request)` - Get minutes until expiration
- `getClientId($request)` - Get client ID
- `getRealm($request)` - Get realm name
- `getSessionId($request)` - Get session ID

### Utilities
- `getUserProfile($request)` - Get complete user profile
- `createAuditContext($request, $action, $data)` - Create audit log context

## Error Responses

The middleware returns standardized error responses:

### Unauthorized (401)
```json
{
  "success": false,
  "data": [],
  "message": "No authentication token provided",
  "error_code": "UNAUTHORIZED"
}
```

### Forbidden (403)
```json
{
  "success": false,
  "data": [],
  "message": "Insufficient permissions. Required roles: admin",
  "error_code": "FORBIDDEN"
}
```

### Server Error (500)
```json
{
  "success": false,
  "data": [],
  "message": "Authentication service error",
  "error_code": "SERVER_ERROR"
}
```

## Token Sources

The middleware checks for tokens in this order:

1. **Authorization Header**: `Authorization: Bearer <token>`
2. **X-Auth-Token Header**: `X-Auth-Token: <token>`
3. **Query Parameter**: `?token=<token>` (not recommended for production)

## Logging

The middleware provides comprehensive logging:

- **Successful Authentication**: User ID, username, roles, expiration
- **Failed Authentication**: Error details, token preview
- **Permission Denied**: User info, required vs actual roles
- **Errors**: Full error details and stack traces

## Testing

Example test requests:

```bash
# With Authorization header
curl -H "Authorization: Bearer <jwt-token>" \
     http://localhost:8000/api/v1/admin/profile

# With X-Auth-Token header
curl -H "X-Auth-Token: <jwt-token>" \
     http://localhost:8000/api/v1/admin/profile

# With query parameter (not recommended)
curl "http://localhost:8000/api/v1/admin/profile?token=<jwt-token>"
```

## Security Considerations

1. **No Signature Verification**: This implementation focuses on token parsing and validation without cryptographic signature verification
2. **Token Expiration**: Always check token expiration
3. **Role Validation**: Implement proper role-based access control
4. **Logging**: Monitor authentication attempts and failures
5. **HTTPS**: Always use HTTPS in production
6. **Token Storage**: Secure token storage on client side

## Integration with Auth Service

This middleware is designed to work with tokens generated by the auth-service-v12 Keycloak integration, ensuring seamless user authentication across services while maintaining access to Old SSO User IDs for backward compatibility.

## Quick Start

1. **Register Middleware**: Already registered as `jwt.auth` in `app/Http/Kernel.php`
2. **Use in Routes**: Apply middleware to route groups or individual routes
3. **Access User Data**: Use the `JwtAuthHelper` trait in controllers
4. **Handle Errors**: Implement proper error handling for authentication failures

## Example Implementation

```php
// routes/api.php
Route::middleware(['jwt.auth'])->group(function () {
    Route::get('/profile', [AdminController::class, 'profile']);
});

// AdminController.php
class AdminController extends Controller
{
    use JwtAuthHelper;

    public function profile(Request $request): JsonResponse
    {
        $oldSsoUserId = $this->getOldSsoUserId($request);
        // ... rest of implementation
    }
}
```
