openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - Admin Service API
  description: |
    Comprehensive admin service for OneFoodDialer 2025 platform management.

    ## Features
    - Dashboard & Analytics with real-time metrics
    - User & Role Management with granular permissions
    - Cloud Kitchen Management with approval workflows
    - Order Management & Tracking with status updates
    - System Settings & Configuration management
    - Reports & Business Intelligence with AI insights
    - Real-time Monitoring & Health checks
    - Backup & Maintenance operations

    ## Authentication
    All endpoints require JWT authentication via Bearer token.

    ## Rate Limiting
    API requests are rate limited to 1000 requests per minute per user.

    ## Error Handling
    All errors follow RFC 7807 Problem Details format.
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
servers:
  - url: http://*************:8000/api
    description: Proxy server
  - url: http://**************:8000/api
    description: Development server
  - url: http://*************:8004/api
    description: Local server
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  parameters:
    Page:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number for pagination
    Limit:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
      description: Number of items per page
    Search:
      name: search
      in: query
      schema:
        type: string
      description: Search query string
    UserId:
      name: userId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Unique identifier for the user
    RoleId:
      name: roleId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Unique identifier for the role
    KitchenId:
      name: kitchenId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Unique identifier for the cloud kitchen
    OrderId:
      name: orderId
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Unique identifier for the order

  responses:
    BadRequest:
      description: Bad request - Invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Forbidden - Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Conflict:
      description: Resource conflict - Duplicate or constraint violation
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    ValidationError:
      description: Validation error - Invalid data format
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationErrorResponse'

  schemas:
    # Common Response Schemas
    ErrorResponse:
      type: object
      required: [status, message]
      properties:
        status:
          type: string
          enum: [error]
          example: error
        message:
          type: string
          example: An error occurred
        code:
          type: string
          example: VALIDATION_ERROR
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"
        path:
          type: string
          example: "/v2/admin-service-v12/users"

    ValidationErrorResponse:
      type: object
      required: [status, message, errors]
      properties:
        status:
          type: string
          enum: [error]
          example: error
        message:
          type: string
          example: Validation failed
        errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                example: email
              message:
                type: string
                example: The email field is required
              code:
                type: string
                example: REQUIRED

    SuccessResponse:
      type: object
      required: [status, message]
      properties:
        status:
          type: string
          enum: [success]
          example: success
        message:
          type: string
          example: Operation completed successfully
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"

    PaginationMeta:
      type: object
      properties:
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 20
        total:
          type: integer
          example: 150
        totalPages:
          type: integer
          example: 8
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false

    # Dashboard Schemas
    DashboardResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success]
          example: success
        data:
          type: object
          properties:
            overview:
              $ref: '#/components/schemas/OverviewMetrics'
            quickStats:
              $ref: '#/components/schemas/QuickStats'
            charts:
              type: array
              items:
                $ref: '#/components/schemas/ChartData'
            systemHealth:
              $ref: '#/components/schemas/SystemHealth'
            alerts:
              type: array
              items:
                $ref: '#/components/schemas/Alert'
            recentActivity:
              type: array
              items:
                $ref: '#/components/schemas/ActivityItem'

    OverviewMetrics:
      type: object
      properties:
        totalUsers:
          type: integer
          example: 1250
          description: Total number of registered users
        totalOrders:
          type: integer
          example: 5420
          description: Total number of orders
        totalRevenue:
          type: number
          format: float
          example: 125000.50
          description: Total revenue in base currency
        growthRate:
          type: number
          format: float
          example: 12.5
          description: Growth rate percentage
        activeKitchens:
          type: integer
          example: 45
          description: Number of active cloud kitchens
        pendingApprovals:
          type: integer
          example: 8
          description: Number of pending kitchen approvals

    QuickStats:
      type: object
      properties:
        activeUsers:
          type: integer
          example: 320
          description: Currently active users
        pendingOrders:
          type: integer
          example: 25
          description: Orders pending processing
        systemAlerts:
          type: integer
          example: 3
          description: Active system alerts
        revenue24h:
          type: number
          format: float
          example: 8500.75
          description: Revenue in last 24 hours
        orderSuccess:
          type: number
          format: float
          example: 98.5
          description: Order success rate percentage
        avgResponseTime:
          type: number
          format: float
          example: 150.5
          description: Average API response time in milliseconds

    # System Health & Monitoring Schemas
    SystemHealth:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, warning, critical]
          example: healthy
          description: Overall system health status
        uptime:
          type: integer
          example: 86400
          description: System uptime in seconds
        cpu:
          type: object
          properties:
            usage:
              type: number
              format: float
              example: 45.2
              description: CPU usage percentage
            cores:
              type: integer
              example: 8
              description: Number of CPU cores
        memory:
          type: object
          properties:
            usage:
              type: number
              format: float
              example: 65.8
              description: Memory usage percentage
            used:
              type: integer
              example: **********
              description: Used memory in bytes
            total:
              type: integer
              example: 17179869184
              description: Total memory in bytes
        disk:
          type: object
          properties:
            usage:
              type: number
              format: float
              example: 35.4
              description: Disk usage percentage
            used:
              type: integer
              example: 107374182400
              description: Used disk space in bytes
            total:
              type: integer
              example: 322122547200
              description: Total disk space in bytes
        services:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: database
              status:
                type: string
                enum: [healthy, warning, critical]
                example: healthy
              responseTime:
                type: number
                format: float
                example: 25.5
                description: Service response time in milliseconds

    ChartData:
      type: object
      properties:
        type:
          type: string
          enum: [line, bar, pie, doughnut, area, scatter]
          example: line
          description: Chart visualization type
        title:
          type: string
          example: Revenue Trend
          description: Chart title
        data:
          type: object
          description: Chart data in format specific to chart type
          example:
            labels: ["Jan", "Feb", "Mar", "Apr", "May"]
            datasets:
              - label: Revenue
                data: [12000, 15000, 18000, 16000, 20000]
                borderColor: "#3B82F6"
        options:
          type: object
          description: Chart configuration options
          example:
            responsive: true
            maintainAspectRatio: false

    Alert:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        type:
          type: string
          enum: [info, warning, error, success]
          example: warning
        title:
          type: string
          example: High CPU Usage
        message:
          type: string
          example: CPU usage has exceeded 80% for the last 10 minutes
        severity:
          type: string
          enum: [low, medium, high, critical]
          example: high
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"
        acknowledged:
          type: boolean
          example: false
        acknowledgedBy:
          type: string
          example: admin-user-123
        acknowledgedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:05:00Z"

    ActivityItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        type:
          type: string
          enum: [user_login, order_created, kitchen_approved, system_alert]
          example: order_created
        description:
          type: string
          example: New order #12345 created by customer John Doe
        userId:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        userName:
          type: string
          example: John Doe
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"
        metadata:
          type: object
          description: Additional activity-specific data
          example:
            orderId: "order-123"
            amount: 25.50

    # User Management Schemas
    AdminUser:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          example: John Doe
          description: Full name of the admin user
        email:
          type: string
          format: email
          example: <EMAIL>
          description: Email address (unique)
        role:
          $ref: '#/components/schemas/AdminRole'
        status:
          type: string
          enum: [active, inactive, suspended]
          example: active
          description: Current user status
        permissions:
          type: array
          items:
            type: string
          example: ["users.read", "users.write", "orders.read"]
          description: Direct permissions assigned to user
        lastLoginAt:
          type: string
          format: date-time
          example: "2024-01-15T09:30:00Z"
          description: Last login timestamp
        loginCount:
          type: integer
          example: 42
          description: Total number of logins
        createdAt:
          type: string
          format: date-time
          example: "2024-01-01T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"
        createdBy:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
          description: ID of user who created this account

    AdminRole:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        name:
          type: string
          example: admin
          description: Role name (unique)
        displayName:
          type: string
          example: Administrator
          description: Human-readable role name
        description:
          type: string
          example: Full system administrator with all permissions
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
        isSystem:
          type: boolean
          example: true
          description: Whether this is a system-defined role
        userCount:
          type: integer
          example: 5
          description: Number of users with this role
        createdAt:
          type: string
          format: date-time
          example: "2024-01-01T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"

    Permission:
      type: object
      properties:
        id:
          type: string
          example: users.read
          description: Permission identifier
        name:
          type: string
          example: View Users
          description: Human-readable permission name
        description:
          type: string
          example: Allows viewing user information and lists
        resource:
          type: string
          example: users
          description: Resource this permission applies to
        action:
          type: string
          example: read
          description: Action allowed on the resource
        module:
          type: string
          example: user_management
          description: Module this permission belongs to
        isSystem:
          type: boolean
          example: true
          description: Whether this is a system-defined permission

    UsersResponse:
      type: object
      properties:
        status:
          type: string
          enum: [success]
          example: success
        message:
          type: string
          example: Users retrieved successfully
        data:
          type: array
          items:
            $ref: '#/components/schemas/AdminUser'
        meta:
          $ref: '#/components/schemas/PaginationMeta'

    CreateUserRequest:
      type: object
      required:
        - name
        - email
        - role
      properties:
        name:
          type: string
          example: John Doe
          description: Full name of the user
        email:
          type: string
          format: email
          example: <EMAIL>
          description: Email address (must be unique)
        role:
          type: string
          example: admin
          description: Role name to assign to the user
        password:
          type: string
          format: password
          example: SecurePassword123
          description: User password (if not provided, will be auto-generated)
        permissions:
          type: array
          items:
            type: string
          example: ["users.read", "users.write"]
          description: Additional permissions to assign
        status:
          type: string
          enum: [active, inactive]
          default: active
          description: Initial user status

    UpdateUserRequest:
      type: object
      properties:
        name:
          type: string
          example: John Doe
          description: Full name of the user
        email:
          type: string
          format: email
          example: <EMAIL>
          description: Email address (must be unique)
        role:
          type: string
          example: admin
          description: Role name to assign to the user
        permissions:
          type: array
          items:
            type: string
          example: ["users.read", "users.write"]
          description: Additional permissions to assign
        status:
          type: string
          enum: [active, inactive, suspended]
          description: User status

    # Settings API Schemas
    DropdownSettings:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Settings retrieved successfully
        data:
          type: object
          properties:
            classes:
              type: array
              items:
                type: object
                properties:
                  label:
                    type: string
                    example: Nursery
                  value:
                    type: string
                    example: Nursery
            divisions:
              type: array
              items:
                type: object
                properties:
                  label:
                    type: string
                    example: A
                  value:
                    type: string
                    example: A
            floors:
              type: array
              items:
                type: object
                properties:
                  label:
                    type: string
                    example: Ground Floor
                  value:
                    type: string
                    example: Ground Floor
            delivery_days:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: integer
                    example: 1
                  name:
                    type: string
                    example: Monday
                  short_name:
                    type: string
                    example: Mon
                  value:
                    type: string
                    example: monday
        meta:
          type: object
          properties:
            total_classes:
              type: integer
              example: 17
            total_divisions:
              type: integer
              example: 13
            total_floors:
              type: integer
              example: 10
            total_delivery_days:
              type: integer
              example: 6
            company_id:
              type: integer
              example: 8163

    WorkingDaysRequest:
      type: object
      required:
        - company_id
        - month_selected
      properties:
        company_id:
          type: integer
          example: 8163
          description: Company identifier
        month_selected:
          type: string
          pattern: '^\d{4}-\d{2}$'
          example: "2025-01"
          description: Month in format YYYY-MM
        days:
          type: array
          items:
            type: integer
            minimum: 0
            maximum: 6
          example: [1, 2, 3, 4, 5]
          description: Optional array of day numbers (0=Sunday, 1=Monday, ..., 6=Saturday)
        kitchen_id:
          type: integer
          example: 1
          default: 1
          description: Kitchen identifier for cut-off settings
        meal_type:
          type: string
          enum: [breakfast, lunch]
          example: lunch
          default: lunch
          description: Meal type for cut-off logic

    WorkingDaysResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Working days retrieved successfully
        data:
          type: object
          properties:
            working_days:
              type: array
              items:
                type: integer
                minimum: 1
                maximum: 31
              example: [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 29, 30, 31]
              description: Array of working day numbers in the month (excludes past dates and cut-off restricted dates)
            month_selected:
              type: string
              example: "2025-01"
              description: Selected month in YYYY-MM format
            company_id:
              type: integer
              example: 8163
              description: Company identifier
            kitchen_id:
              type: integer
              example: 1
              description: Kitchen identifier
            meal_type:
              type: string
              example: lunch
              description: Meal type used for cut-off logic
            total_working_days:
              type: integer
              example: 26
              description: Total count of working days

    SetupWizardStatus:
      type: object
      properties:
        completed:
          type: boolean
          example: false
        current_step:
          type: integer
          example: 1

    # Plan End Date Calculation Schemas
    CalculateEndDateRequest:
      type: object
      required:
        - company_id
        - start_date
        - plan_id
      properties:
        company_id:
          type: integer
          example: 8163
          description: Company identifier
        start_date:
          type: string
          format: date
          pattern: '^\d{4}-\d{2}-\d{2}$'
          example: "2025-01-15"
          description: Start date in YYYY-MM-DD format
        plan_id:
          type: integer
          example: 1
          description: Plan identifier from plan_master table
        unit_id:
          type: integer
          example: 1
          description: Unit identifier (optional, defaults to 1)

    CalculateEndDateResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: End date calculated successfully
        data:
          type: object
          properties:
            plan_id:
              type: integer
              example: 1
              description: Plan identifier
            plan_name:
              type: string
              example: "5 Day Meal Plan"
              description: Plan name
            plan_quantity:
              type: integer
              example: 5
              description: Number of meals/days in the plan
            plan_period:
              type: string
              example: "days"
              description: Period unit (days, weeks, months)
            plan_type:
              type: string
              example: "daily"
              description: Type of plan
            start_date:
              type: string
              format: date
              example: "2025-01-15"
              description: Start date in YYYY-MM-DD format
            end_date:
              type: string
              format: date
              example: "2025-01-21"
              description: Calculated end date in YYYY-MM-DD format
            total_days:
              type: integer
              example: 7
              description: Total number of days from start to end (including both dates)
            company_id:
              type: integer
              example: 8163
              description: Company identifier

    # Additional schemas for compatibility
    Role:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: admin
        display_name:
          type: string
          example: Administrator
        description:
          type: string
          example: System administrator with full access
        is_system:
          type: boolean
          example: true
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/Permission'

    Error:
      type: object
      properties:
        status:
          type: string
          example: error
        message:
          type: string
          example: Error message
        code:
          type: string
          example: VALIDATION_ERROR
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"

    Success:
      type: object
      properties:
        status:
          type: string
          example: success
        message:
          type: string
          example: Success message
        timestamp:
          type: string
          format: date-time
          example: "2024-01-15T10:00:00Z"

security:
  - bearerAuth: []

tags:
  - name: Dashboard
    description: Admin dashboard and overview operations
  - name: User Management
    description: Admin user management operations
  - name: Role Management
    description: Role and permission management
  - name: Cloud Kitchen Management
    description: Cloud kitchen approval and management
  - name: Order Management
    description: Order tracking and management
  - name: System Settings
    description: System configuration and settings
  - name: Reports & Analytics
    description: Reporting and analytics operations
  - name: Maintenance
    description: System maintenance and backup operations
  - name: Health
    description: Health check and monitoring

paths:
  # Dashboard & Overview Endpoints
  /dashboard:
    get:
      summary: Get admin dashboard overview
      description: Retrieve comprehensive dashboard data including metrics, charts, and system health
      tags: [Dashboard]
      parameters:
        - name: timeRange
          in: query
          schema:
            type: string
            enum: [24h, 7d, 30d, 90d]
            default: 7d
          description: Time range for dashboard metrics
      responses:
        '200':
          description: Dashboard data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'

  /dashboard/overview:
    get:
      summary: Get dashboard overview metrics
      description: Get high-level overview metrics for the dashboard
      tags: [Dashboard]
      responses:
        '200':
          description: Overview metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OverviewMetrics'

  /dashboard/quick-stats:
    get:
      summary: Get quick statistics
      description: Get real-time quick statistics for dashboard
      tags: [Dashboard]
      responses:
        '200':
          description: Quick stats retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuickStats'

  /dashboard/system-health:
    get:
      summary: Get system health status
      description: Get current system health and performance metrics
      tags: [Dashboard]
      responses:
        '200':
          description: System health retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemHealth'

  # User Management Endpoints
  /users:
    get:
      summary: List admin users
      description: Retrieve paginated list of admin users with filtering and search
      tags: [User Management]
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
        - name: role
          in: query
          schema:
            type: string
          description: Filter by role name
        - name: status
          in: query
          schema:
            type: string
            enum: [active, inactive, suspended]
          description: Filter by user status
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsersResponse'

    post:
      summary: Create new admin user
      description: Create a new admin user with specified role and permissions
      tags: [User Management]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminUser'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'

  /users/{userId}:
    get:
      summary: Get admin user by ID
      description: Retrieve detailed information about a specific admin user
      tags: [User Management]
      parameters:
        - $ref: '#/components/parameters/UserId'
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminUser'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      summary: Update admin user
      description: Update admin user information, role, or permissions
      tags: [User Management]
      parameters:
        - $ref: '#/components/parameters/UserId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminUser'

    delete:
      summary: Delete admin user
      description: Permanently delete an admin user account
      tags: [User Management]
      parameters:
        - $ref: '#/components/parameters/UserId'
      responses:
        '204':
          description: User deleted successfully
        '404':
          $ref: '#/components/responses/NotFound'

  /users/{userId}/suspend:
    post:
      summary: Suspend admin user
      description: Temporarily suspend an admin user account
      tags: [User Management]
      parameters:
        - $ref: '#/components/parameters/UserId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                reason:
                  type: string
                  description: Reason for suspension
                duration:
                  type: integer
                  description: Suspension duration in days
      responses:
        '200':
          description: User suspended successfully

  /users/{userId}/activate:
    post:
      summary: Activate admin user
      description: Activate a suspended or inactive admin user
      tags: [User Management]
      parameters:
        - $ref: '#/components/parameters/UserId'
      responses:
        '200':
          description: User activated successfully

  /v2/admin/user:
    get:
      summary: Get current user information
      description: Returns information about the authenticated user
      responses:
        '200':
          description: User information
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      name:
                        type: string
                        example: John Doe
                      email:
                        type: string
                        example: <EMAIL>
                      roles:
                        type: array
                        items:
                          type: string
                        example: [admin, manager]
                      permissions:
                        type: array
                        items:
                          type: string
                        example: [view_settings, edit_settings]
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/health:
    get:
      summary: Health check endpoint
      description: Returns the health status of the service
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Service is healthy
                  timestamp:
                    type: string
                    format: date-time
                    example: 2023-05-20T12:34:56Z
  /v2/admin/config:
    get:
      summary: Get all configuration values
      description: Returns all configuration values
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration values
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    additionalProperties:
                      type: string
                    example:
                      MERCHANT_COMPANY_NAME: Demo Company
                      GLOBAL_CURRENCY: USD
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/config/{key}:
    get:
      summary: Get a specific configuration value
      description: Returns a specific configuration value
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: Configuration key
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration value
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      key:
                        type: string
                        example: MERCHANT_COMPANY_NAME
                      value:
                        type: string
                        example: Demo Company
        '404':
          description: Configuration key not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update a configuration value
      description: Updates a specific configuration value
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: Configuration key
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                value:
                  type: string
                  example: New Company Name
                type:
                  type: string
                  enum: [string, boolean, integer, float, json, array]
                  example: string
                group:
                  type: string
                  example: company
                is_system:
                  type: boolean
                  example: false
                is_public:
                  type: boolean
                  example: true
                description:
                  type: string
                  example: Company name
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - value
      responses:
        '200':
          description: Configuration value updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Configuration key 'MERCHANT_COMPANY_NAME' updated successfully.
                  data:
                    type: object
                    properties:
                      key:
                        type: string
                        example: MERCHANT_COMPANY_NAME
                      value:
                        type: string
                        example: New Company Name
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      value: [The value field is required.]
    delete:
      summary: Delete a configuration value
      description: Deletes a specific configuration value
      parameters:
        - name: key
          in: path
          required: true
          schema:
            type: string
          description: Configuration key
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration value deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Configuration key 'MERCHANT_COMPANY_NAME' deleted successfully.
        '404':
          description: Configuration key not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/config/group/{group}:
    get:
      summary: Get settings by group
      description: Returns configuration values for a specific group
      parameters:
        - name: group
          in: path
          required: true
          schema:
            type: string
          description: Setting group
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Configuration values for the group
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    additionalProperties:
                      type: string
                    example:
                      MERCHANT_COMPANY_NAME: Demo Company
                      MERCHANT_POSTAL_ADDRESS: 123 Main St
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/roles:
    get:
      summary: Get all roles
      description: Returns all roles
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Roles
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Role'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Create a new role
      description: Creates a new role
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: editor
                display_name:
                  type: string
                  example: Editor
                description:
                  type: string
                  example: Can edit content
                permissions:
                  type: array
                  items:
                    type: integer
                  example: [1, 2, 3]
                is_system:
                  type: boolean
                  example: false
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - name
      responses:
        '201':
          description: Role created
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Role created successfully
                  data:
                    $ref: '#/components/schemas/Role'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      name: [The name field is required.]
  /v2/admin/roles/{id}:
    get:
      summary: Get a specific role
      description: Returns a specific role
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Role ID
      responses:
        '200':
          description: Role
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/Role'
        '404':
          description: Role not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update a role
      description: Updates a specific role
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Role ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: editor
                display_name:
                  type: string
                  example: Content Editor
                description:
                  type: string
                  example: Can edit content
                permissions:
                  type: array
                  items:
                    type: integer
                  example: [1, 2, 3, 4]
                is_system:
                  type: boolean
                  example: false
      responses:
        '200':
          description: Role updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Role updated successfully
                  data:
                    $ref: '#/components/schemas/Role'
        '404':
          description: Role not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      name: [The name has already been taken.]
    delete:
      summary: Delete a role
      description: Deletes a specific role
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Role ID
      responses:
        '200':
          description: Role deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Role deleted successfully
        '403':
          description: Cannot delete system role
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Cannot delete system role.
        '404':
          description: Role not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/permissions:
    get:
      summary: Get all permissions
      description: Returns all permissions
      responses:
        '200':
          description: Permissions
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Permission'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/permissions/module/{module}:
    get:
      summary: Get permissions by module
      description: Returns permissions for a specific module
      parameters:
        - name: module
          in: path
          required: true
          schema:
            type: string
          description: Module name
      responses:
        '200':
          description: Permissions for the module
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Permission'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/setup-wizard/status:
    get:
      summary: Get setup wizard status
      description: Returns the current status of the setup wizard
      responses:
        '200':
          description: Setup wizard status
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    $ref: '#/components/schemas/SetupWizardStatus'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    put:
      summary: Update setup wizard status
      description: Updates the status of the setup wizard
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                completed:
                  type: boolean
                  example: true
                current_step:
                  type: integer
                  example: 3
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
      responses:
        '200':
          description: Setup wizard status updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Setup wizard status updated successfully
                  data:
                    $ref: '#/components/schemas/SetupWizardStatus'
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      current_step: [The current step must be between 1 and 5.]
  /v2/admin/setup-wizard/company-profile:
    post:
      summary: Setup company profile
      description: Sets up the company profile
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                company_name:
                  type: string
                  example: Demo Company
                postal_address:
                  type: string
                  example: 123 Main St, Anytown, CA 12345
                support_email:
                  type: string
                  example: <EMAIL>
                phone:
                  type: string
                  example: 555-1234
                sender_id:
                  type: string
                  example: DEMO
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - company_name
                - postal_address
                - support_email
                - phone
                - sender_id
      responses:
        '200':
          description: Company profile setup completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Company profile setup completed successfully
                  data:
                    type: object
                    properties:
                      current_step:
                        type: integer
                        example: 2
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      company_name: [The company name field is required.]
  /v2/admin/setup-wizard/system-settings:
    post:
      summary: Setup system settings
      description: Sets up the system settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                locale:
                  type: string
                  example: en_US
                currency:
                  type: string
                  example: USD
                currency_symbol:
                  type: string
                  example: $
                time_zone:
                  type: string
                  example: UTC
                company_id:
                  type: integer
                  example: 1
                unit_id:
                  type: integer
                  example: 1
              required:
                - locale
                - currency
                - currency_symbol
                - time_zone
      responses:
        '200':
          description: System settings setup completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: System settings setup completed successfully
                  data:
                    type: object
                    properties:
                      current_step:
                        type: integer
                        example: 3
        '422':
          description: Validation failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  message:
                    type: string
                    example: Validation failed
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      locale: [The locale field is required.]
  /v2/admin/setup-wizard/complete:
    post:
      summary: Complete setup wizard
      description: Completes the setup wizard
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
        - name: unit_id
          in: query
          schema:
            type: integer
          description: Unit ID
      responses:
        '200':
          description: Setup wizard completed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Setup wizard completed successfully
                  data:
                    type: object
                    properties:
                      completed:
                        type: boolean
                        example: true
                      current_step:
                        type: integer
                        example: 5
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  # Settings API Endpoints
  /v2/admin/settings/dropdowns:
    get:
      summary: Get dropdown settings
      description: Returns dropdown values for Classes, Divisions, Floors, and delivery days
      tags: [System Settings]
      parameters:
        - name: company_id
          in: query
          schema:
            type: integer
          description: Company ID
      responses:
        '200':
          description: Dropdown settings retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DropdownSettings'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      summary: Get dropdown settings
      description: Returns dropdown values for Classes, Divisions, Floors, and delivery days
      tags: [System Settings]
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                company_id:
                  type: integer
                  example: 8163
                  description: Company ID
      responses:
        '200':
          description: Dropdown settings retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DropdownSettings'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /v2/admin/settings/working-days:
    get:
      summary: Get working days for a month
      description: |
        Returns working day numbers for a specific month, excluding weekoffs, holidays, 
        and past dates. Also applies cut-off logic based on kitchen and meal type settings.
        
        **Cut-off Logic:**
        - Past dates are automatically excluded
        - For current date, checks cut-off settings (e.g., K1_BREAKFAST_ORDER_CUT_OFF_DAY/TIME)
        - If current time is past cut-off, current date is excluded from results
      tags: [System Settings]
      parameters:
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
          description: Company identifier
        - name: month_selected
          in: query
          required: true
          schema:
            type: string
            pattern: '^\d{4}-\d{2}$'
          description: Month in format YYYY-MM (e.g., 2025-01)
        - name: kitchen_id
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Kitchen identifier for cut-off settings
        - name: meal_type
          in: query
          required: false
          schema:
            type: string
            enum: [breakfast, lunch]
            default: lunch
          description: Meal type for cut-off logic
        - name: days
          in: query
          required: false
          schema:
            type: array
            items:
              type: integer
              minimum: 0
              maximum: 6
          description: Optional array of day numbers (0=Sunday, 1=Monday, ..., 6=Saturday)
          style: form
          explode: true
      responses:
        '200':
          description: Working days retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkingDaysResponse'
        '400':
          description: Bad request - Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      summary: Get working days for a month
      description: |
        Returns working day numbers for a specific month, excluding weekoffs, holidays, 
        and past dates. Also applies cut-off logic based on kitchen and meal type settings.
        
        **Cut-off Logic:**
        - Past dates are automatically excluded
        - For current date, checks cut-off settings (e.g., K1_BREAKFAST_ORDER_CUT_OFF_DAY/TIME)
        - If current time is past cut-off, current date is excluded from results
      tags: [System Settings]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkingDaysRequest'
      responses:
        '200':
          description: Working days retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkingDaysResponse'
        '400':
          description: Bad request - Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /v2/admin/settings/calculate-end-date:
    post:
      summary: Calculate plan end date
      description: |
        Calculates the end date for a meal plan based on the selected plan's quantity 
        and working days, excluding weekoffs and holidays.
        
        **Logic:**
        - If plan quantity is 1, end date equals start date
        - For quantity > 1, finds the required number of working days starting from start date
        - Excludes weekoffs (based on company settings) and holidays (from holiday_master table)
        - Returns the date when the plan will be completed
      tags: [System Settings]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateEndDateRequest'
      responses:
        '200':
          description: End date calculated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CalculateEndDateResponse'
        '400':
          description: Bad request - Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Plan not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
        summary: Calculate plan end date
        description: |
          Calculates the end date for a meal plan based on the selected plan's quantity 
          and working days, excluding weekoffs and holidays.
          
          **Logic:**
          - If plan quantity is 1, end date equals start date
          - For quantity > 1, finds the required number of working days starting from start date
          - Excludes weekoffs (based on company settings) and holidays (from holiday_master table)
          - If `days` query parameter is provided, only those weekdays are considered as working days (after weekoff/holiday exclusion)
          - Returns the date when the plan will be completed
        tags: [System Settings]
        parameters:
          - name: company_id
            in: query
            required: true
            schema:
              type: integer
            description: Company identifier
          - name: start_date
            in: query
            required: true
            schema:
              type: string
              format: date
              pattern: '^\d{4}-\d{2}-\d{2}$'
            description: Start date in YYYY-MM-DD format
          - name: plan_id
            in: query
            required: true
            schema:
              type: integer
            description: Plan identifier from plan_master table
          - name: unit_id
            in: query
            required: false
            schema:
              type: integer
              default: 1
            description: Unit identifier (optional, defaults to 1)
          - name: days
            in: query
            required: false
            style: form
            explode: true
            schema:
              type: array
              items:
                type: integer
                minimum: 0
                maximum: 6
            description: Optional array of day numbers (0=Sunday, 1=Monday, ..., 6=Saturday) to restrict working days
        responses:
          '200':
            description: End date calculated successfully
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/CalculateEndDateResponse'
          '400':
            description: Bad request - Invalid parameters
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
          '404':
            description: Plan not found
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'
          '500':
            description: Internal server error
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/Error'

  /v2/admin/settings/images:
    get:
      summary: Get all images
      description: |
        Retrieve all images including banner images and weekly planner image.
        Returns images with full S3 URLs for direct access.
      operationId: getAllImages
      tags:
        - System Settings
      responses:
        '200':
          description: Images retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Images retrieved successfully"
                  data:
                    type: object
                    properties:
                      banners:
                        type: array
                        description: List of banner images
                        items:
                          type: object
                          properties:
                            image_path:
                              type: string
                              description: Full S3 URL of the banner image
                              example: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/banner1.jpg"
                            image_title:
                              type: string
                              description: Title of the banner image
                              example: "Welcome Banner"
                            company_id:
                              type: integer
                              description: Company ID associated with the image
                              example: 8163
                      weekly-planner:
                        type: object
                        nullable: true
                        description: Weekly planner image (if available)
                        properties:
                          image_path:
                            type: string
                            description: Full S3 URL of the weekly planner image
                            example: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/weekly-planner.jpg"
                          image_title:
                            type: string
                            description: Title of the weekly planner image
                            example: "Weekly Meal Planner"
                          company_id:
                            type: integer
                            description: Company ID associated with the image
                            example: 8163
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Failed to retrieve images"
                  error:
                    type: string
                    example: "Database connection failed"

  /v2/admin/settings/payment-gateways:
    get:
      summary: Get enabled payment gateways
      description: |
        Returns enabled payment gateways and their configuration.
        Retrieves gateways enabled by admin (from ONLINE_PAYMENT_GATEWAY setting)
        and their related keys (key, secret, salt, etc.)
      operationId: getPaymentGateways
      tags:
        - System Settings
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Payment gateways retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Payment gateways retrieved successfully"
                  data:
                    type: object
                    properties:
                      enabled_gateways:
                        type: array
                        description: List of enabled gateway names
                        items:
                          type: string
                        example: ["razorpay", "payu", "phonepe"]
                      gateways:
                        type: object
                        description: Gateway configurations with their settings
                        additionalProperties:
                          type: object
                          description: Gateway-specific configuration
                        example:
                          razorpay:
                            key: "rzp_test_1234567890"
                            secret: "secret_key_here"
                            webhook_secret: "webhook_secret_here"
                          payu:
                            merchant_id: "merchant_id_here"
                            merchant_key: "merchant_key_here"
                            salt: "salt_here"
                          phonepe:
                            merchant_id: "merchant_id_here"
                            salt_key: "salt_key_here"
                            salt_index: "1"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Failed to retrieve payment gateways"
                  error:
                    type: string
                    example: "Database connection failed"

  /v2/admin/setup-wizard/payment-gateways:
    post:
      summary: Setup payment gateways
      description: |
        Setup payment gateways and payment modes during the setup wizard process.
        Configures multiple payment gateways with their credentials and settings.
      operationId: setupPaymentGateways
      tags:
        - Setup Wizard
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - gateways
              properties:
                gateways:
                  type: array
                  description: Array of payment gateway configurations
                  minItems: 1
                  items:
                    type: object
                    required:
                      - id
                      - name
                      - provider
                      - enabled
                      - sandbox_mode
                      - credentials
                      - settings
                    properties:
                      id:
                        type: string
                        description: Unique identifier for the gateway
                        example: "razorpay_main"
                      name:
                        type: string
                        minLength: 2
                        description: Display name for the gateway
                        example: "Razorpay Payment Gateway"
                      provider:
                        type: string
                        enum: [stripe, paypal, razorpay, square, payu, instamojo, paytm, cashfree, phonepe]
                        description: Payment gateway provider
                        example: "razorpay"
                      enabled:
                        type: boolean
                        description: Whether the gateway is enabled
                        example: true
                      sandbox_mode:
                        type: boolean
                        description: Whether to use sandbox/test mode
                        example: true
                      credentials:
                        type: object
                        description: Gateway-specific credentials
                        additionalProperties: true
                        example:
                          key: "rzp_test_1234567890"
                          secret: "secret_key_here"
                          webhook_secret: "webhook_secret_here"
                      settings:
                        type: object
                        description: Gateway-specific settings
                        additionalProperties: true
                        example:
                          currency: "INR"
                          auto_capture: true
                          webhook_url: "https://api.example.com/webhooks/razorpay"
                default_gateway:
                  type: string
                  description: ID of the default payment gateway
                  example: "razorpay_main"
                fallback_gateway:
                  type: string
                  description: ID of the fallback payment gateway
                  example: "payu_backup"
                company_id:
                  type: integer
                  description: Company ID (optional, defaults to 1)
                  example: 8163
                unit_id:
                  type: integer
                  description: Unit ID (optional, defaults to 1)
                  example: 1
            examples:
              razorpay_setup:
                summary: Razorpay Gateway Setup
                value:
                  gateways:
                    - id: "razorpay_main"
                      name: "Razorpay Payment Gateway"
                      provider: "razorpay"
                      enabled: true
                      sandbox_mode: true
                      credentials:
                        key: "rzp_test_1234567890"
                        secret: "secret_key_here"
                        webhook_secret: "webhook_secret_here"
                      settings:
                        currency: "INR"
                        auto_capture: true
                        webhook_url: "https://api.example.com/webhooks/razorpay"
                  default_gateway: "razorpay_main"
                  company_id: 8163
              multiple_gateways:
                summary: Multiple Gateways Setup
                value:
                  gateways:
                    - id: "razorpay_main"
                      name: "Razorpay Payment Gateway"
                      provider: "razorpay"
                      enabled: true
                      sandbox_mode: false
                      credentials:
                        key: "rzp_live_1234567890"
                        secret: "live_secret_key_here"
                        webhook_secret: "webhook_secret_here"
                      settings:
                        currency: "INR"
                        auto_capture: true
                    - id: "payu_backup"
                      name: "PayU Backup Gateway"
                      provider: "payu"
                      enabled: true
                      sandbox_mode: true
                      credentials:
                        merchant_id: "merchant_id_here"
                        merchant_key: "merchant_key_here"
                        salt: "salt_here"
                      settings:
                        currency: "INR"
                        success_url: "https://example.com/success"
                        failure_url: "https://example.com/failure"
                  default_gateway: "razorpay_main"
                  fallback_gateway: "payu_backup"
                  company_id: 8163
      responses:
        '200':
          description: Payment gateways setup completed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Payment gateways setup completed successfully"
                  data:
                    type: object
                    properties:
                      configured_gateways:
                        type: array
                        items:
                          type: string
                        example: ["razorpay_main", "payu_backup"]
                      default_gateway:
                        type: string
                        example: "razorpay_main"
                      fallback_gateway:
                        type: string
                        example: "payu_backup"
                      total_gateways:
                        type: integer
                        example: 2
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Validation failed"
                  errors:
                    type: object
                    additionalProperties:
                      type: array
                      items:
                        type: string
                    example:
                      gateways: ["The gateways field is required."]
                      "gateways.0.provider": ["The selected provider is invalid."]
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Failed to setup payment gateways"
                  error:
                    type: string
                    example: "Database connection failed"