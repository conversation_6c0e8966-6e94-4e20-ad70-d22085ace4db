#!/usr/bin/env python3
"""
Detailed check for working days API endpoint
"""
import re

def check_working_days_endpoint():
    """Check working days endpoint in detail"""
    with open('openapi-settings.yaml', 'r') as f:
        content = f.read()
    
    print("Working Days API Endpoint Analysis")
    print("=" * 50)
    
    # Extract the working days section
    pattern = r'(/v2/admin/settings/working-days:.*?)(?=\n  /|\ncomponents:|\Z)'
    match = re.search(pattern, content, re.DOTALL)
    
    if not match:
        print("✗ Working days endpoint not found")
        return False
    
    endpoint_content = match.group(1)
    print("✓ Working days endpoint found")
    
    # Check required fields
    checks = [
        ('summary:', 'Summary'),
        ('description:', 'Description'),
        ('operationId:', 'Operation ID'),
        ('tags:', 'Tags'),
        ('parameters:', 'Parameters'),
        ('responses:', 'Responses'),
        ('200:', 'Success response'),
        ('400:', 'Bad request response'),
        ('500:', 'Server error response')
    ]
    
    for field, name in checks:
        if field in endpoint_content:
            print(f"✓ {name} present")
        else:
            print(f"✗ {name} missing")
    
    # Check parameters
    required_params = ['company_id', 'month_selected']
    optional_params = ['kitchen_id', 'meal_type', 'days']
    
    print(f"\nParameter Analysis:")
    for param in required_params:
        if f'name: {param}' in endpoint_content:
            if 'required: true' in endpoint_content[endpoint_content.find(f'name: {param}'):endpoint_content.find(f'name: {param}') + 200]:
                print(f"✓ Required parameter '{param}' properly defined")
            else:
                print(f"⚠ Parameter '{param}' found but not marked as required")
        else:
            print(f"✗ Required parameter '{param}' missing")
    
    for param in optional_params:
        if f'name: {param}' in endpoint_content:
            print(f"✓ Optional parameter '{param}' present")
        else:
            print(f"⚠ Optional parameter '{param}' missing")
    
    # Check schema references
    print(f"\nSchema References:")
    if 'WorkingDaysResponse' in endpoint_content:
        print("✓ WorkingDaysResponse schema referenced")
    else:
        print("✗ WorkingDaysResponse schema not referenced")
    
    if 'ErrorResponse' in endpoint_content:
        print("✓ ErrorResponse schema referenced")
    else:
        print("✗ ErrorResponse schema not referenced")
    
    # Check for common YAML issues
    print(f"\nYAML Structure:")
    lines = endpoint_content.split('\n')
    indent_issues = 0
    for i, line in enumerate(lines):
        if line.strip() and not line.startswith('  '):
            if not line.startswith('/v2/admin/settings/working-days:'):
                indent_issues += 1
    
    if indent_issues == 0:
        print("✓ Indentation appears correct")
    else:
        print(f"⚠ {indent_issues} potential indentation issues")
    
    # Check for tag consistency
    if 'tags: [Working Days]' in endpoint_content:
        print("✓ Tags format is correct (array format)")
    elif 'tags:\n        - Working Days' in endpoint_content:
        print("✓ Tags format is correct (list format)")
    else:
        print("⚠ Tags format may be inconsistent")
    
    print(f"\nEndpoint Content Preview:")
    print("-" * 30)
    preview_lines = endpoint_content.split('\n')[:15]
    for line in preview_lines:
        print(line)
    if len(endpoint_content.split('\n')) > 15:
        print("... (truncated)")
    
    return True

if __name__ == "__main__":
    check_working_days_endpoint()
