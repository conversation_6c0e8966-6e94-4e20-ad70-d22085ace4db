<?php

namespace App\Traits;

use Illuminate\Http\Request;

/**
 * JWT Authentication Helper Trait
 * 
 * Provides convenient methods for controllers to access JWT authentication data
 * that was set by the KeycloakJwtAuth middleware.
 */
trait JwtAuthHelper
{
    /**
     * Get authenticated user information from JWT
     * 
     * @param Request $request
     * @return array|null
     */
    protected function getAuthUser(Request $request): ?array
    {
        return $request->attributes->get('auth_user');
    }

    /**
     * Get user roles from JWT
     * 
     * @param Request $request
     * @return array|null
     */
    protected function getUserRoles(Request $request): ?array
    {
        return $request->attributes->get('user_roles');
    }

    /**
     * Get JWT payload
     * 
     * @param Request $request
     * @return array|null
     */
    protected function getJwtPayload(Request $request): ?array
    {
        return $request->attributes->get('jwt_payload');
    }

    /**
     * Get JWT header
     * 
     * @param Request $request
     * @return array|null
     */
    protected function getJwtHeader(Request $request): ?array
    {
        return $request->attributes->get('jwt_header');
    }

    /**
     * Get token expiration information
     * 
     * @param Request $request
     * @return array|null
     */
    protected function getTokenExpiration(Request $request): ?array
    {
        return $request->attributes->get('token_expiration');
    }

    /**
     * Get Old SSO User ID from JWT
     * 
     * @param Request $request
     * @return string|null
     */
    protected function getOldSsoUserId(Request $request): ?string
    {
        $authUser = $this->getAuthUser($request);
        return $authUser['old_sso_user_id'] ?? null;
    }

    /**
     * Get current user ID from JWT
     * 
     * @param Request $request
     * @return string|null
     */
    protected function getCurrentUserId(Request $request): ?string
    {
        $authUser = $this->getAuthUser($request);
        return $authUser['user_id'] ?? null;
    }

    /**
     * Get current username from JWT
     * 
     * @param Request $request
     * @return string|null
     */
    protected function getCurrentUsername(Request $request): ?string
    {
        $authUser = $this->getAuthUser($request);
        return $authUser['username'] ?? null;
    }

    /**
     * Get current user email from JWT
     * 
     * @param Request $request
     * @return string|null
     */
    protected function getCurrentUserEmail(Request $request): ?string
    {
        $authUser = $this->getAuthUser($request);
        return $authUser['email'] ?? null;
    }

    /**
     * Check if current user has specific role
     * 
     * @param Request $request
     * @param string $role
     * @param string|null $resource
     * @return bool
     */
    protected function hasRole(Request $request, string $role, ?string $resource = null): bool
    {
        if ($request->hasMethod('hasRole')) {
            return $request->hasRole($role, $resource);
        }

        $roles = $this->getUserRoles($request);
        if (!$roles) {
            return false;
        }

        if ($resource && isset($roles['resource_roles'][$resource])) {
            return in_array($role, $roles['resource_roles'][$resource]);
        }

        return in_array($role, $roles['all_roles'] ?? []);
    }

    /**
     * Check if current user is admin
     * 
     * @param Request $request
     * @return bool
     */
    protected function isAdmin(Request $request): bool
    {
        if ($request->hasMethod('isAdmin')) {
            return $request->isAdmin();
        }

        $roles = $this->getUserRoles($request);
        if (!$roles) {
            return false;
        }

        $adminRoles = [
            'realm-admin',
            'admin',
            'super-admin',
            'manage-users',
            'manage-realm'
        ];

        foreach ($adminRoles as $adminRole) {
            if (in_array($adminRole, $roles['all_roles'] ?? [])) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get all user roles as flat array
     * 
     * @param Request $request
     * @return array
     */
    protected function getAllRoles(Request $request): array
    {
        $roles = $this->getUserRoles($request);
        return $roles['all_roles'] ?? [];
    }

    /**
     * Get realm roles only
     * 
     * @param Request $request
     * @return array
     */
    protected function getRealmRoles(Request $request): array
    {
        $roles = $this->getUserRoles($request);
        return $roles['realm_roles'] ?? [];
    }

    /**
     * Get resource/client roles
     * 
     * @param Request $request
     * @param string|null $resource
     * @return array
     */
    protected function getResourceRoles(Request $request, ?string $resource = null): array
    {
        $roles = $this->getUserRoles($request);
        
        if (!$roles || !isset($roles['resource_roles'])) {
            return [];
        }

        if ($resource) {
            return $roles['resource_roles'][$resource] ?? [];
        }

        return $roles['resource_roles'];
    }

    /**
     * Check if token is expired
     * 
     * @param Request $request
     * @return bool
     */
    protected function isTokenExpired(Request $request): bool
    {
        $expiration = $this->getTokenExpiration($request);
        return $expiration['is_expired'] ?? true;
    }

    /**
     * Get token expiration time in minutes
     * 
     * @param Request $request
     * @return float|null
     */
    protected function getTokenExpiresInMinutes(Request $request): ?float
    {
        $expiration = $this->getTokenExpiration($request);
        return $expiration['expires_in_minutes'] ?? null;
    }

    /**
     * Get user's client ID from JWT
     * 
     * @param Request $request
     * @return string|null
     */
    protected function getClientId(Request $request): ?string
    {
        $authUser = $this->getAuthUser($request);
        return $authUser['client_id'] ?? null;
    }

    /**
     * Get user's realm from JWT
     * 
     * @param Request $request
     * @return string|null
     */
    protected function getRealm(Request $request): ?string
    {
        $authUser = $this->getAuthUser($request);
        return $authUser['realm'] ?? null;
    }

    /**
     * Get session ID from JWT
     * 
     * @param Request $request
     * @return string|null
     */
    protected function getSessionId(Request $request): ?string
    {
        $authUser = $this->getAuthUser($request);
        return $authUser['session_id'] ?? null;
    }

    /**
     * Get full user profile for logging/auditing
     * 
     * @param Request $request
     * @return array
     */
    protected function getUserProfile(Request $request): array
    {
        $authUser = $this->getAuthUser($request);
        $roles = $this->getUserRoles($request);
        $expiration = $this->getTokenExpiration($request);

        return [
            'user_info' => $authUser ?? [],
            'roles' => $roles ?? [],
            'token_expiration' => $expiration ?? [],
            'is_admin' => $this->isAdmin($request),
            'old_sso_user_id' => $this->getOldSsoUserId($request),
        ];
    }

    /**
     * Create audit log entry with user context
     * 
     * @param Request $request
     * @param string $action
     * @param array $additionalData
     * @return array
     */
    protected function createAuditContext(Request $request, string $action, array $additionalData = []): array
    {
        $authUser = $this->getAuthUser($request);

        return array_merge([
            'action' => $action,
            'user_id' => $authUser['user_id'] ?? null,
            'username' => $authUser['username'] ?? null,
            'old_sso_user_id' => $authUser['old_sso_user_id'] ?? null,
            'client_id' => $authUser['client_id'] ?? null,
            'realm' => $authUser['realm'] ?? null,
            'session_id' => $authUser['session_id'] ?? null,
            'is_admin' => $this->isAdmin($request),
            'timestamp' => now()->toISOString(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ], $additionalData);
    }
}
