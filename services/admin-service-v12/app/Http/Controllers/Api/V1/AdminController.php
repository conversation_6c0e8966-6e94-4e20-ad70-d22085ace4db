<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Traits\JwtAuthHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * Admin Controller
 * 
 * Example controller demonstrating JWT authentication middleware usage
 * with Keycloak tokens and Old SSO User ID extraction.
 */
class AdminController extends Controller
{
    use JwtAuthHelper;

    /**
     * Get current user profile
     * Requires JWT authentication
     */
    public function profile(Request $request): JsonResponse
    {
        try {
            $userProfile = $this->getUserProfile($request);
            
            Log::info('Admin profile accessed', $this->createAuditContext($request, 'profile_access'));

            return response()->json([
                'success' => true,
                'data' => $userProfile,
                'message' => 'User profile retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving user profile', [
                'error' => $e->getMessage(),
                'user_id' => $this->getCurrentUserId($request),
            ]);

            return response()->json([
                'success' => false,
                'data' => [],
                'message' => 'Failed to retrieve user profile'
            ], 500);
        }
    }

    /**
     * Admin dashboard data
     * Requires admin role
     */
    public function dashboard(Request $request): JsonResponse
    {
        try {
            if (!$this->isAdmin($request)) {
                return response()->json([
                    'success' => false,
                    'data' => [],
                    'message' => 'Admin access required'
                ], 403);
            }

            $dashboardData = [
                'user_info' => $this->getAuthUser($request),
                'admin_roles' => $this->getRealmRoles($request),
                'old_sso_user_id' => $this->getOldSsoUserId($request),
                'token_expires_in_minutes' => $this->getTokenExpiresInMinutes($request),
                'session_id' => $this->getSessionId($request),
                'realm' => $this->getRealm($request),
            ];

            Log::info('Admin dashboard accessed', $this->createAuditContext($request, 'dashboard_access', [
                'admin_roles' => $this->getRealmRoles($request)
            ]));

            return response()->json([
                'success' => true,
                'data' => $dashboardData,
                'message' => 'Admin dashboard data retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving admin dashboard', [
                'error' => $e->getMessage(),
                'user_id' => $this->getCurrentUserId($request),
            ]);

            return response()->json([
                'success' => false,
                'data' => [],
                'message' => 'Failed to retrieve dashboard data'
            ], 500);
        }
    }

    /**
     * User management endpoint
     * Requires specific role
     */
    public function manageUsers(Request $request): JsonResponse
    {
        try {
            // Check for specific user management role
            if (!$this->hasRole($request, 'manage-users') && !$this->isAdmin($request)) {
                return response()->json([
                    'success' => false,
                    'data' => [],
                    'message' => 'User management permission required'
                ], 403);
            }

            $managementData = [
                'current_user' => [
                    'user_id' => $this->getCurrentUserId($request),
                    'username' => $this->getCurrentUsername($request),
                    'email' => $this->getCurrentUserEmail($request),
                    'old_sso_user_id' => $this->getOldSsoUserId($request),
                ],
                'permissions' => [
                    'all_roles' => $this->getAllRoles($request),
                    'realm_roles' => $this->getRealmRoles($request),
                    'resource_roles' => $this->getResourceRoles($request),
                    'is_admin' => $this->isAdmin($request),
                ],
                'session_info' => [
                    'session_id' => $this->getSessionId($request),
                    'client_id' => $this->getClientId($request),
                    'realm' => $this->getRealm($request),
                    'expires_in_minutes' => $this->getTokenExpiresInMinutes($request),
                    'is_expired' => $this->isTokenExpired($request),
                ]
            ];

            Log::info('User management accessed', $this->createAuditContext($request, 'user_management_access', [
                'has_manage_users_role' => $this->hasRole($request, 'manage-users')
            ]));

            return response()->json([
                'success' => true,
                'data' => $managementData,
                'message' => 'User management data retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error in user management', [
                'error' => $e->getMessage(),
                'user_id' => $this->getCurrentUserId($request),
            ]);

            return response()->json([
                'success' => false,
                'data' => [],
                'message' => 'Failed to retrieve user management data'
            ], 500);
        }
    }

    /**
     * Token information endpoint
     * Shows JWT token details
     */
    public function tokenInfo(Request $request): JsonResponse
    {
        try {
            $tokenInfo = [
                'header' => $this->getJwtHeader($request),
                'user_info' => $this->getAuthUser($request),
                'roles' => $this->getUserRoles($request),
                'expiration' => $this->getTokenExpiration($request),
                'validation' => [
                    'is_expired' => $this->isTokenExpired($request),
                    'expires_in_minutes' => $this->getTokenExpiresInMinutes($request),
                    'is_admin' => $this->isAdmin($request),
                ]
            ];

            Log::info('Token info accessed', $this->createAuditContext($request, 'token_info_access'));

            return response()->json([
                'success' => true,
                'data' => $tokenInfo,
                'message' => 'Token information retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving token info', [
                'error' => $e->getMessage(),
                'user_id' => $this->getCurrentUserId($request),
            ]);

            return response()->json([
                'success' => false,
                'data' => [],
                'message' => 'Failed to retrieve token information'
            ], 500);
        }
    }

    /**
     * Old SSO User mapping endpoint
     * Demonstrates Old SSO User ID extraction
     */
    public function oldSsoMapping(Request $request): JsonResponse
    {
        try {
            $oldSsoUserId = $this->getOldSsoUserId($request);
            
            if (!$oldSsoUserId) {
                return response()->json([
                    'success' => false,
                    'data' => [],
                    'message' => 'Old SSO User ID not found in token'
                ], 404);
            }

            $mappingData = [
                'keycloak_user_id' => $this->getCurrentUserId($request),
                'keycloak_username' => $this->getCurrentUsername($request),
                'old_sso_user_id' => $oldSsoUserId,
                'mapping_status' => 'active',
                'token_info' => [
                    'client_id' => $this->getClientId($request),
                    'realm' => $this->getRealm($request),
                    'session_id' => $this->getSessionId($request),
                ]
            ];

            Log::info('Old SSO mapping accessed', $this->createAuditContext($request, 'old_sso_mapping_access', [
                'old_sso_user_id' => $oldSsoUserId
            ]));

            return response()->json([
                'success' => true,
                'data' => $mappingData,
                'message' => 'Old SSO mapping retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error retrieving Old SSO mapping', [
                'error' => $e->getMessage(),
                'user_id' => $this->getCurrentUserId($request),
            ]);

            return response()->json([
                'success' => false,
                'data' => [],
                'message' => 'Failed to retrieve Old SSO mapping'
            ], 500);
        }
    }
}
