openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - Admin Settings API
  description: |
    Admin settings service for OneFoodDialer 2025 platform configuration management.

    ## Features
    - System Settings & Configuration management
    - Dropdown values for forms (Classes, Divisions, Floors, Allergies)
    - Working days calculation with weekoff and holiday logic
    - Plan end date calculation with cut-off logic
    - Payment gateway configuration (enabled gateways only)
    - Image management (banners, weekly planner)

    ## Authentication
    All endpoints require JWT authentication via Bearer token.

    ## Rate Limiting
    API requests are rate limited to 1000 requests per minute per user.

    ## Error Handling
    All errors follow RFC 7807 Problem Details format.

    ## Security
    Sensitive configuration data (API keys, secrets) are not exposed through these endpoints for security reasons.
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
servers:
  - url: http://*************:8000
    description: Proxy server
  - url: http://**************:8000/api
    description: Production server
  - url: http://************:8000/api
    description: Local server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key

  schemas:
    SuccessResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Operation completed successfully"
        data:
          type: object
          description: Response data varies by endpoint

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Operation failed"
        error:
          type: string
          example: "Error details"

    DropdownSettings:
      type: object
      properties:
        classes:
          type: array
          items:
            type: string
          example: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]
        divisions:
          type: array
          items:
            type: string
          example: ["A", "B", "C", "D", "E"]
        floors:
          type: array
          items:
            type: string
          example: ["Ground Floor", "1st Floor", "2nd Floor", "3rd Floor"]
        delivery_days:
          type: array
          items:
            type: object
            properties:
              number:
                type: integer
              name:
                type: string
              date:
                type: string
                format: date
          example:
            - number: 1
              name: "Monday"
              date: "2025-01-06"
            - number: 2
              name: "Tuesday"
              date: "2025-01-07"
        allergies:
          type: array
          items:
            type: string
          example: ["Nuts", "Dairy", "Gluten", "Shellfish", "Eggs"]

    # WorkingDaysRequest:
    #   type: object
    #   required:
    #     - month
    #     - year
    #   properties:
    #     month:
    #       type: integer
    #       minimum: 1
    #       maximum: 12
    #       description: Month number (1-12)
    #       example: 1
    #     year:
    #       type: integer
    #       minimum: 2020
    #       maximum: 2030
    #       description: Year
    #       example: 2025
    #     kitchen_id:
    #       type: integer
    #       description: Kitchen ID for cut-off logic
    #       example: 1
    #     meal_type:
    #       type: string
    #       description: Meal type for cut-off logic
    #       example: "lunch"

    # WorkingDay:
    #   type: object
    #   properties:
    #     number:
    #       type: integer
    #       description: Day number of the month
    #       example: 15
    #     name:
    #       type: string
    #       description: Day name
    #       example: "Wednesday"
    #     date:
    #       type: string
    #       format: date
    #       description: Full date
    #       example: "2025-01-15"
    #     is_working_day:
    #       type: boolean
    #       description: Whether this is a working day
    #       example: true
    #     is_past:
    #       type: boolean
    #       description: Whether this date is in the past
    #       example: false
    #     is_cutoff_passed:
    #       type: boolean
    #       description: Whether cut-off time has passed for this date
    #       example: false

    WorkingDaysResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: Working days retrieved successfully
        data:
          type: object
          properties:
            working_days:
              type: array
              items:
                type: integer
                minimum: 1
                maximum: 31
              example: [2, 3, 4, 5, 6, 8, 9, 10, 11, 12, 13, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 29, 30, 31]
              description: Array of working day numbers in the month (excludes past dates and cut-off restricted dates)
            month_selected:
              type: string
              example: "2025-01"
              description: Selected month in YYYY-MM format
            company_id:
              type: integer
              example: 8163
              description: Company identifier
            kitchen_id:
              type: integer
              example: 1
              description: Kitchen identifier
            meal_type:
              type: string
              example: lunch
              description: Meal type used for cut-off logic
            total_working_days:
              type: integer
              example: 26
              description: Total count of working days

    CalculateEndDateRequest:
      type: object
      required:
        - plan_id
        - start_date
      properties:
        company_id:
          type: integer
          example: 8163
          description: Company identifier
        plan_id:
          type: integer
          description: Plan ID to calculate end date for
          example: 1
        start_date:
          type: string
          format: date
          description: Plan start date
          example: "2025-01-15"
        kitchen_id:
          type: integer
          description: Kitchen ID for working days calculation
          example: 1
        meal_type:
          type: string
          description: Meal type for working days calculation
          example: "lunch"

    PaymentGateways:
      type: object
      properties:
        enabled_gateways:
          type: array
          description: List of enabled gateway names only (no configuration keys for security)
          items:
            type: string
          example: ["razorpay", "payu", "phonepe"]

    ImageData:
      type: object
      properties:
        banners:
          type: array
          description: List of banner images
          items:
            type: object
            properties:
              image_path:
                type: string
                description: Full S3 URL of the banner image
                example: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/banner1.jpg"
              image_title:
                type: string
                description: Title of the banner image
                example: "Welcome Banner"
              company_id:
                type: integer
                description: Company ID associated with the image
                example: 8163
        weekly-planner:
          type: object
          nullable: true
          description: Weekly planner image (if available)
          properties:
            image_path:
              type: string
              description: Full S3 URL of the weekly planner image
              example: "https://s3.ap-south-1.amazonaws.com/onefooddialer-assets/8163/cms/weekly-planner.jpg"
            image_title:
              type: string
              description: Title of the weekly planner image
              example: "Weekly Meal Planner"
            company_id:
              type: integer
              description: Company ID associated with the image
              example: 8163

security:
  - bearerAuth: []

tags:
  - name: Dropdown Settings
    description: Dropdown values for forms and UI components
  - name: Working Days
    description: Working days calculation with weekoff and holiday logic
  - name: Date Calculations
    description: Plan end date and delivery date calculations
  - name: Payment Gateways
    description: Payment gateway configuration (enabled gateways only)
  - name: Image Management
    description: Banner and weekly planner image management

paths:
  /v2/admin/settings/dropdowns:
    get:
      summary: Get dropdown settings
      description: |
        Returns dropdown values for Classes, Divisions, Floors, delivery days, and allergies.
        Used to populate form dropdowns and UI components.
      operationId: getDropdownSettings
      tags:
        - Dropdown Settings
      parameters:
        - name: company_id
          in: query
          description: Company ID (optional, defaults to configured company)
          required: false
          schema:
            type: integer
            example: 8163
      responses:
        '200':
          description: Dropdown settings retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/DropdownSettings'
                      meta:
                        type: object
                        properties:
                          total_classes:
                            type: integer
                            example: 12
                          total_divisions:
                            type: integer
                            example: 5
                          total_floors:
                            type: integer
                            example: 4
                          total_delivery_days:
                            type: integer
                            example: 22
                          total_allergies:
                            type: integer
                            example: 8
                          company_id:
                            type: integer
                            example: 8163
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    # post:
    #   summary: Get dropdown settings (POST method)
    #   description: |
    #     Alternative POST method for getting dropdown settings.
    #     Supports the same functionality as GET method.
    #   operationId: getDropdownSettingsPost
    #   tags:
    #     - Dropdown Settings
    #   requestBody:
    #     required: false
    #     content:
    #       application/json:
    #         schema:
    #           type: object
    #           properties:
    #             company_id:
    #               type: integer
    #               description: Company ID (optional)
    #               example: 8163
    #   responses:
    #     '200':
    #       description: Dropdown settings retrieved successfully
    #       content:
    #         application/json:
    #           schema:
    #             allOf:
    #               - $ref: '#/components/schemas/SuccessResponse'
    #               - type: object
    #                 properties:
    #                   data:
    #                     $ref: '#/components/schemas/DropdownSettings'
    #     '500':
    #       description: Internal server error
    #       content:
    #         application/json:
    #           schema:
    #             $ref: '#/components/schemas/ErrorResponse'

  /v2/admin/settings/working-days:
    get:
      summary: Get working days for a month
      description: |
        Returns working day numbers for a specific month, excluding weekoffs, holidays, 
        and past dates. Also applies cut-off logic based on kitchen and meal type settings.
        
        **Cut-off Logic:**
        - Past dates are automatically excluded
        - For current date, checks cut-off settings (e.g., K1_BREAKFAST_ORDER_CUT_OFF_DAY/TIME)
        - If current time is past cut-off, current date is excluded from results
      operationId: getWorkingDays
      tags: [Working Days]
      parameters:
        - name: company_id
          in: query
          required: true
          schema:
            type: integer
          description: Company identifier
        - name: month_selected
          in: query
          required: true
          schema:
            type: string
            pattern: '^\d{4}-\d{2}$'
          description: Month in format YYYY-MM (e.g., 2025-01)
        - name: kitchen_id
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Kitchen identifier for cut-off settings
        - name: meal_type
          in: query
          required: false
          schema:
            type: string
            enum: [breakfast, lunch]
            default: lunch
          description: Meal type for cut-off logic
        - name: days
          in: query
          required: false
          schema:
            type: string
            items:
              type: integer
              minimum: 0
              maximum: 6
          description: Optional array of day numbers (0=Sunday, 1=Monday, ..., 6=Saturday)
          style: form
          explode: true
      responses:
        '200':
          description: Working days retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorkingDaysResponse'
        '400':
          description: Bad request - Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/admin/settings/calculate-end-date:
    post:
      summary: Calculate plan end date
      description: |
        Calculates the end date for a meal plan based on the selected plan's quantity
        and working days, excluding weekoffs and holidays.
      operationId: calculateEndDate
      tags:
        - Date Calculations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateEndDateRequest'
      responses:
        '200':
          description: End date calculated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          plan_id:
                            type: integer
                            example: 1
                          start_date:
                            type: string
                            format: date
                            example: "2025-01-15"
                          end_date:
                            type: string
                            format: date
                            example: "2025-02-15"
                          total_days:
                            type: integer
                            example: 20
                          working_days_used:
                            type: integer
                            example: 20
                          plan_quantity:
                            type: integer
                            example: 20
        '400':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/admin/settings/payment-gateways:
    get:
      summary: Get enabled payment gateways
      description: |
        Returns list of enabled payment gateways only.
        Retrieves gateways enabled by admin (from ONLINE_PAYMENT_GATEWAY setting).
        Gateway configuration keys are not returned for security reasons.
      operationId: getPaymentGateways
      tags:
        - Payment Gateways
      responses:
        '200':
          description: Payment gateways retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PaymentGateways'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/admin/settings/images:
    get:
      summary: Get all images
      description: |
        Retrieve all images including banner images and weekly planner image.
        Returns images with full S3 URLs for direct access.
      operationId: getAllImages
      tags:
        - Image Management
      responses:
        '200':
          description: Images retrieved successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ImageData'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
