<?php

use App\Http\Controllers\Api\TrackTiffinsController;
use App\Http\Controllers\Api\V2\ConfigController;
use App\Http\Controllers\Api\V2\RoleController;
use App\Http\Controllers\Api\V2\SetupWizardController;
use App\Http\Controllers\Api\V2\SettingsController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\KeycloakJwtAuth;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// API v2 routes
Route::prefix('v2/admin')->group(function () {
    // User information
    Route::get('/user', function (Request $request) {
        $user = $request->user();
        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'roles' => $user->roles->pluck('name'),
                'permissions' => $user->getAllPermissions()->pluck('name'),
            ],
        ]);
    });

    // Health check endpoint
    Route::get('/health', function () {
        return response()->json([
            'status' => 'success',
            'message' => 'Service is healthy',
            'timestamp' => now()->toIso8601String(),
        ]);
    });

    // Dabbawala Management Routes
    Route::prefix('track-tiffins')->middleware(['permission:view_delivery'])->group(function () {
        Route::get('/', [TrackTiffinsController::class, 'index']);
        Route::get('/filter', [TrackTiffinsController::class, 'filter']);
        Route::get('/{id}', [TrackTiffinsController::class, 'show']);
        Route::put('/{id}/update-status', [TrackTiffinsController::class, 'updateStatus'])->middleware('permission:edit_delivery');
        Route::post('/{id}/generate-code', [TrackTiffinsController::class, 'generateCode'])->middleware('permission:edit_delivery');
    });

    // Configuration Routes
    Route::prefix('config')->group(function () {
        Route::get('/', [ConfigController::class, 'index']);
        Route::get('/{key}', [ConfigController::class, 'show']);
        Route::put('/{key}', [ConfigController::class, 'update'])->middleware('permission:edit_settings');
        Route::delete('/{key}', [ConfigController::class, 'destroy'])->middleware('permission:edit_settings');
        Route::get('/group/{group}', [ConfigController::class, 'getSettingsByGroup']);
    });

    // Settings Routes
    // Route::middleware(['jwt.auth'])->group(function() {
    Route::prefix('settings')->group(function () {
        Route::post('/dropdowns', [SettingsController::class, 'getDropdownSettings']);
        Route::get('/dropdowns', [SettingsController::class, 'getDropdownSettings']);
        Route::post('/working-days', [SettingsController::class, 'getWorkingDays']);
        Route::get('/working-days', [SettingsController::class, 'getWorkingDays']);
        Route::post('/calculate-end-date', [SettingsController::class, 'calculateEndDate']);
        Route::get('/payment-gateways', [SettingsController::class, 'getPaymentGateways']);
        Route::get('/images', [SettingsController::class, 'getAllImages']);
    });
    // });

    // Role and Permission Routes
    Route::prefix('roles')->middleware(['permission:view_roles'])->group(function () {
        Route::get('/', [RoleController::class, 'index']);
        Route::post('/', [RoleController::class, 'store'])->middleware('permission:create_roles');
        Route::get('/{id}', [RoleController::class, 'show']);
        Route::put('/{id}', [RoleController::class, 'update'])->middleware('permission:edit_roles');
        Route::delete('/{id}', [RoleController::class, 'destroy'])->middleware('permission:delete_roles');
    });

    Route::prefix('permissions')->middleware(['permission:view_roles'])->group(function () {
        Route::get('/', [RoleController::class, 'getAllPermissions']);
        Route::get('/module/{module}', [RoleController::class, 'getPermissionsByModule']);
    });

    // Setup Wizard Routes - Only accessible by admin
    Route::prefix('setup-wizard')->middleware(['role:admin'])->group(function () {
        Route::get('/status', [SetupWizardController::class, 'getStatus']);
        Route::put('/status', [SetupWizardController::class, 'updateStatus']);
        Route::post('/company-profile', [SetupWizardController::class, 'setupCompanyProfile']);
        Route::post('/system-settings', [SetupWizardController::class, 'setupSystemSettings']);
        Route::post('/payment-gateways', [SetupWizardController::class, 'setupPaymentGateways']);
        Route::post('/complete', [SetupWizardController::class, 'completeSetup']);
    });
});

// JWT Authentication Examples
// use App\Http\Controllers\Api\V1\AdminController;

// // Routes requiring JWT authentication (no specific roles)
// Route::middleware(['jwt.auth'])->group(function () {
//     Route::get('/v1/admin/profile', [AdminController::class, 'profile']);
//     Route::get('/v1/admin/token-info', [AdminController::class, 'tokenInfo']);
//     Route::get('/v1/admin/old-sso-mapping', [AdminController::class, 'oldSsoMapping']);
// });

// // Routes requiring admin role
// Route::middleware(['jwt.auth:admin'])->group(function () {
//     Route::get('/v1/admin/dashboard', [AdminController::class, 'dashboard']);
// });

// // Routes requiring specific roles
// Route::middleware(['jwt.auth:manage-users'])->group(function () {
//     Route::get('/v1/admin/manage-users', [AdminController::class, 'manageUsers']);
// });

// // Routes requiring multiple possible roles (user needs at least one)
// Route::middleware(['jwt.auth:admin,manage-users,super-admin'])->group(function () {
//     Route::get('/v1/admin/advanced-management', [AdminController::class, 'manageUsers']);
// });
