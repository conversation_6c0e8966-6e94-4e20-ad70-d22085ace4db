#!/bin/bash

# Test JWT Middleware for Admin Service v12
# Tests custom JWT authentication middleware with Keycloak tokens

# Configuration
BASE_URL="http://192.168.1.7:8001/api"  # Admin service URL
AUTH_SERVICE_URL="http://192.168.1.7:8000/api/v2/hybrid-auth"  # Auth service URL

# Test credentials
TEST_USERNAME="918793644824"
TEST_PASSWORD="your-password-here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    local status=$1
    local message=$2
    case $status in
        "INFO") echo -e "${BLUE}[INFO]${NC} $message" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} $message" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} $message" ;;
        "TEST") echo -e "${CYAN}[TEST]${NC} $message" ;;
    esac
}

# Get JWT token from auth service
get_jwt_token() {
    print_status "INFO" "🔐 Getting JWT token from auth service..."
    
    if [ "$TEST_PASSWORD" = "your-password-here" ]; then
        print_status "WARNING" "⚠️  Please provide actual password for testing"
        return 1
    fi
    
    local login_response=$(curl -s -X POST "$AUTH_SERVICE_URL/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\": \"$TEST_USERNAME\", \"password\": \"$TEST_PASSWORD\"}")
    
    local login_success=$(echo "$login_response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$login_success" = "true" ]; then
        # Extract access token
        JWT_TOKEN=$(echo "$login_response" | grep -o '"access_token":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        
        if [ -n "$JWT_TOKEN" ]; then
            print_status "SUCCESS" "✅ JWT token obtained successfully"
            print_status "INFO" "Token preview: ${JWT_TOKEN:0:50}..."
            return 0
        else
            print_status "ERROR" "❌ Failed to extract JWT token from response"
            return 1
        fi
    else
        local error_message=$(echo "$login_response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Login failed: $error_message"
        return 1
    fi
}

# Test endpoint without authentication
test_no_auth() {
    print_status "TEST" "🚫 Testing endpoint without authentication"
    
    local response=$(curl -s -X GET "$BASE_URL/v1/admin/profile")
    
    echo "Response: $response"
    
    local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    local error_code=$(echo "$response" | grep -o '"error_code":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    if [ "$success" = "false" ] && [ "$error_code" = "UNAUTHORIZED" ]; then
        print_status "SUCCESS" "✅ Correctly rejected request without token"
    else
        print_status "ERROR" "❌ Should have rejected request without token"
    fi
    
    echo ""
}

# Test endpoint with valid JWT token
test_valid_jwt() {
    print_status "TEST" "✅ Testing endpoint with valid JWT token"
    
    local response=$(curl -s -X GET "$BASE_URL/v1/admin/profile" \
        -H "Authorization: Bearer $JWT_TOKEN")
    
    echo "Response: $response"
    
    local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$success" = "true" ]; then
        print_status "SUCCESS" "✅ Successfully authenticated with JWT token"
        
        # Extract user information
        local user_id=$(echo "$response" | grep -o '"user_id":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        local username=$(echo "$response" | grep -o '"username":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        local old_sso_user_id=$(echo "$response" | grep -o '"old_sso_user_id":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        
        print_status "INFO" "User Information:"
        echo "  - User ID: $user_id"
        echo "  - Username: $username"
        echo "  - Old SSO User ID: $old_sso_user_id"
        
        if [ -n "$old_sso_user_id" ] && [ "$old_sso_user_id" != "null" ]; then
            print_status "SUCCESS" "🎯 Old SSO User ID successfully extracted!"
        else
            print_status "WARNING" "⚠️  Old SSO User ID not found in token"
        fi
    else
        local error_message=$(echo "$response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Authentication failed: $error_message"
    fi
    
    echo ""
}

# Test token info endpoint
test_token_info() {
    print_status "TEST" "📊 Testing token info endpoint"
    
    local response=$(curl -s -X GET "$BASE_URL/v1/admin/token-info" \
        -H "Authorization: Bearer $JWT_TOKEN")
    
    echo "Response: $response"
    
    local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$success" = "true" ]; then
        print_status "SUCCESS" "✅ Token info retrieved successfully"
        
        # Extract token information
        local expires_in_minutes=$(echo "$response" | grep -o '"expires_in_minutes":[^,]*' | cut -d':' -f2 | tr -d ' ')
        local is_expired=$(echo "$response" | grep -o '"is_expired":[^,]*' | cut -d':' -f2 | tr -d ' ')
        local is_admin=$(echo "$response" | grep -o '"is_admin":[^,]*' | cut -d':' -f2 | tr -d ' ')
        
        print_status "INFO" "Token Information:"
        echo "  - Expires in minutes: $expires_in_minutes"
        echo "  - Is expired: $is_expired"
        echo "  - Is admin: $is_admin"
    else
        local error_message=$(echo "$response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Token info failed: $error_message"
    fi
    
    echo ""
}

# Test Old SSO mapping endpoint
test_old_sso_mapping() {
    print_status "TEST" "🔗 Testing Old SSO mapping endpoint"
    
    local response=$(curl -s -X GET "$BASE_URL/v1/admin/old-sso-mapping" \
        -H "Authorization: Bearer $JWT_TOKEN")
    
    echo "Response: $response"
    
    local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    
    if [ "$success" = "true" ]; then
        print_status "SUCCESS" "✅ Old SSO mapping retrieved successfully"
        
        # Extract mapping information
        local keycloak_user_id=$(echo "$response" | grep -o '"keycloak_user_id":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        local old_sso_user_id=$(echo "$response" | grep -o '"old_sso_user_id":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        local mapping_status=$(echo "$response" | grep -o '"mapping_status":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        
        print_status "INFO" "Mapping Information:"
        echo "  - Keycloak User ID: $keycloak_user_id"
        echo "  - Old SSO User ID: $old_sso_user_id"
        echo "  - Mapping Status: $mapping_status"
        
        if [ -n "$old_sso_user_id" ] && [ "$old_sso_user_id" != "null" ]; then
            print_status "SUCCESS" "🎯 Old SSO User ID mapping working correctly!"
        fi
    else
        local error_message=$(echo "$response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "WARNING" "⚠️  Old SSO mapping failed: $error_message"
    fi
    
    echo ""
}

# Test admin dashboard (requires admin role)
test_admin_dashboard() {
    print_status "TEST" "👑 Testing admin dashboard (requires admin role)"
    
    local response=$(curl -s -X GET "$BASE_URL/v1/admin/dashboard" \
        -H "Authorization: Bearer $JWT_TOKEN")
    
    echo "Response: $response"
    
    local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    local error_code=$(echo "$response" | grep -o '"error_code":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    if [ "$success" = "true" ]; then
        print_status "SUCCESS" "✅ Admin dashboard accessed successfully"
    elif [ "$error_code" = "FORBIDDEN" ]; then
        print_status "WARNING" "⚠️  Access denied - user doesn't have admin role"
    else
        local error_message=$(echo "$response" | grep -o '"message":"[^"]*"' | cut -d':' -f2 | tr -d '"')
        print_status "ERROR" "❌ Admin dashboard failed: $error_message"
    fi
    
    echo ""
}

# Test with invalid token
test_invalid_token() {
    print_status "TEST" "❌ Testing with invalid token"
    
    local response=$(curl -s -X GET "$BASE_URL/v1/admin/profile" \
        -H "Authorization: Bearer invalid.jwt.token")
    
    echo "Response: $response"
    
    local success=$(echo "$response" | grep -o '"success":[^,]*' | cut -d':' -f2 | tr -d ' ')
    local error_code=$(echo "$response" | grep -o '"error_code":"[^"]*"' | cut -d':' -f2 | tr -d '"')
    
    if [ "$success" = "false" ] && [ "$error_code" = "UNAUTHORIZED" ]; then
        print_status "SUCCESS" "✅ Correctly rejected invalid token"
    else
        print_status "ERROR" "❌ Should have rejected invalid token"
    fi
    
    echo ""
}

# Main test function
run_jwt_middleware_tests() {
    print_status "INFO" "🧪 Testing JWT Middleware for Admin Service v12"
    echo "========================================"
    echo ""
    
    # Get JWT token first
    if ! get_jwt_token; then
        print_status "ERROR" "❌ Cannot proceed without JWT token"
        return 1
    fi
    
    echo ""
    
    # Run tests
    test_no_auth
    test_valid_jwt
    test_token_info
    test_old_sso_mapping
    test_admin_dashboard
    test_invalid_token
    
    echo "========================================"
    print_status "INFO" "📋 JWT Middleware Test Summary"
    echo ""
    print_status "INFO" "✅ Features Tested:"
    echo "  - Token validation and parsing"
    echo "  - User information extraction"
    echo "  - Old SSO User ID extraction"
    echo "  - Role-based access control"
    echo "  - Token expiration handling"
    echo "  - Error response formatting"
    echo ""
    print_status "INFO" "🔧 Middleware Components:"
    echo "  - JwtDecoder utility class"
    echo "  - KeycloakJwtAuth middleware"
    echo "  - JwtAuthHelper trait"
    echo "  - AdminController examples"
    echo ""
    print_status "INFO" "🎯 Key Features:"
    echo "  - No external JWT packages required"
    echo "  - Keycloak token compatibility"
    echo "  - Old SSO User ID extraction"
    echo "  - Comprehensive role checking"
    echo "  - Standardized error responses"
    echo ""
    print_status "INFO" "Test Complete!"
}

# Run the tests
run_jwt_middleware_tests
