openapi: 3.0.3
info:
  title: OneFoodDialer 2025 - Catalogue Service API
  description: |
    Catalogue service for OneFoodDialer 2025 platform - Weekly Planner and Meal Plan Duration APIs.

    ## Features
    - Weekly Planner with meal categorization and date-wise planning
    - Meal Plan Duration management with pricing and promo code integration
    - Direct access to plan_master table for meal plan information

    ## Authentication
    All endpoints require JWT authentication via Bear<PERSON> token.

    ## Security
    All endpoints are protected with jwt.auth middleware for secure access.
  version: 2.0.0
  contact:
    name: OneFoodDialer 2025 API Support
    email: <EMAIL>
    url: https://docs.onefooddialer.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://*************:8000
    description: Proxy server
  - url: http://**************:9000/api
    description: Production server
  - url: http://************:8000/api
    description: Local server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    WeeklyPlannerResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Weekly planner retrieved successfully"
        data:
          type: object
          properties:
            week_dates:
              type: array
              items:
                type: object
                properties:
                  date:
                    type: string
                    format: date
                    example: "2025-01-06"
                  day_name:
                    type: string
                    example: "Monday"
            meal_planner:
              type: object
              properties:
                breakfast:
                  type: object
                  additionalProperties:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealData'
                lunch:
                  type: object
                  additionalProperties:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealData'
                jain:
                  type: object
                  additionalProperties:
                    type: array
                    items:
                      $ref: '#/components/schemas/MealData'
            summary:
              type: object
              properties:
                total_days:
                  type: integer
                  example: 5
                categories:
                  type: array
                  items:
                    type: string
                  example: ["breakfast", "lunch", "jain"]
                date_range:
                  type: object
                  properties:
                    from:
                      type: string
                      format: date
                      example: "2025-01-06"
                    to:
                      type: string
                      format: date
                      example: "2025-01-10"

    MealData:
      type: object
      properties:
        meal_id:
          type: integer
          example: 123
        meal_name:
          type: string
          example: "Breakfast Combo"
        date:
          type: string
          format: date
          example: "2025-01-06"
        day_name:
          type: string
          example: "Monday"
        items:
          type: array
          items:
            $ref: '#/components/schemas/MealItem'

    MealItem:
      type: object
      properties:
        item_code:
          type: string
          example: "604"
        item_name:
          type: string
          example: "Poha"
        quantity:
          type: string
          example: "1"
        source:
          type: string
          enum: ["quickserve_planner_table", "quickserve_products_table_fallback", "not_found"]
          example: "quickserve_planner_table"
        date:
          type: string
          format: date
          example: "2025-01-06"
        category:
          type: string
          example: "breakfast"

    MealPlanDurationListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Meal plans retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/MealPlanDuration'
        meta:
          type: object
          properties:
            total:
              type: integer
              example: 5
            filtered_by:
              type: object
              properties:
                plan_type:
                  type: string
                  example: "weekly"
                plan_quantity:
                  type: integer
                  example: 7

    MealPlanDurationResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Meal plan retrieved successfully"
        data:
          $ref: '#/components/schemas/MealPlanDuration'

    MealPlanDuration:
      type: object
      properties:
        id:
          type: integer
          example: 1
        plan_name:
          type: string
          example: "Weekly Lunch Plan"
        plan_quantity:
          type: integer
          example: 7
        plan_period:
          type: string
          example: "weekly"
        plan_type:
          type: string
          example: "lunch"
        plan_start_date:
          type: string
          format: date
          example: "2025-01-01"
        plan_end_date:
          type: string
          format: date
          example: "2025-12-31"
        plan_status:
          type: integer
          example: 1
        show_to_customer:
          type: string
          example: "yes"
        kitchen_id:
          type: integer
          example: 1
        promo_code_id:
          type: integer
          nullable: true
          example: 5
        promo_code:
          type: string
          nullable: true
          example: "SAVE20"
        discount_percentage:
          type: number
          format: float
          nullable: true
          example: 20.0
        discount_type:
          type: string
          nullable: true
          example: "percentage"
        duration_days:
          type: integer
          example: 7
        duration_description:
          type: string
          example: "Weekly"
        meal_count:
          type: integer
          example: 7
        price_per_meal:
          type: number
          format: float
          example: 150.00
        original_price:
          type: number
          format: float
          example: 1050.00
        discounted_price:
          type: number
          format: float
          example: 840.00
        total_savings:
          type: number
          format: float
          example: 210.00
        currency:
          type: string
          example: "INR"
        price_display:
          type: string
          example: "₹1,050 ₹840"
        product_count:
          type: integer
          example: 3

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message"
        error:
          type: string
          example: "Detailed error information"

security:
  - bearerAuth: []

tags:
  - name: Weekly Planner
    description: Weekly meal planning with categorization and date-wise organization
  - name: Meal Plan Duration
    description: Meal plan duration management with pricing and promo code integration

paths:
  /v2/catalogue/weekly-planner:
    get:
      summary: Get weekly meal planner
      description: |
        Returns organized meal planner data with categorization (breakfast, lunch, jain) 
        and date-wise meal planning. Includes product details from planner and products tables.
        
        **Features:**
        - Date range support (defaults to current week Monday-Friday)
        - Meal categorization with Jain meal separation
        - Product planner integration with fallback to products table
        - Excludes items starting with "None" from planner
      operationId: getWeeklyPlanner
      tags: [Weekly Planner]
      parameters:
        - name: from_date
          in: query
          required: false
          schema:
            type: string
            format: date
            pattern: '^\d{4}-\d{2}-\d{2}$'
          description: Start date in YYYY-MM-DD format (defaults to current week Monday)
          example: "2025-01-06"
        - name: to_date
          in: query
          required: false
          schema:
            type: string
            format: date
            pattern: '^\d{4}-\d{2}-\d{2}$'
          description: End date in YYYY-MM-DD format (defaults to current week Friday, must be >= from_date)
          example: "2025-01-10"
      responses:
        '200':
          description: Weekly planner retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WeeklyPlannerResponse'
        '404':
          description: No active meals found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "No active meals found"
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/catalogue/meal-plan-durations:
    get:
      summary: Get all meal plan durations
      description: |
        Returns all available meal plans with duration information, pricing, and promo code details.
        Direct access to plan_master table with computed fields for UI display.
        
        **Features:**
        - Only active plans visible to customers
        - Pricing calculation based on associated products
        - Promo code integration with discount calculation
        - Duration description and meal count computation
      operationId: getMealPlanDurations
      tags: [Meal Plan Duration]
      parameters:
        - name: plan_type
          in: query
          required: false
          schema:
            type: string
          description: Filter by plan type (e.g., periodbased,datebased)
          example: "periodbased"
        - name: plan_quantity
          in: query
          required: false
          schema:
            type: integer
          description: Filter by plan quantity (number of days)
          example: 5
      responses:
        '200':
          description: Meal plans retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MealPlanDurationListResponse'
        '500':
          description: Failed to retrieve meal plans
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /v2/catalogue/meal-plan-durations/{id}:
    get:
      summary: Get specific meal plan duration by ID
      description: |
        Returns detailed information about a specific meal plan including pricing,
        promo code details, and computed fields for UI display.
        
        **Features:**
        - Detailed plan information with pricing breakdown
        - Promo code discount calculation
        - Duration description and meal count
        - Product association and pricing calculation
      operationId: getMealPlanDurationById
      tags: [Meal Plan Duration]
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Meal plan ID (pk_plan_code)
          example: 1
      responses:
        '200':
          description: Meal plan retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MealPlanDurationResponse'
        '404':
          description: Meal plan not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Meal plan not found"
        '500':
          description: Failed to retrieve meal plan
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
