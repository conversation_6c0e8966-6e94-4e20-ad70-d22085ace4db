<?php

use App\Http\Controllers\Api\V2\CartController;
use App\Http\Controllers\Api\V2\CatalogueController;
use App\Http\Controllers\Api\V2\CouponController;
use App\Http\Controllers\Api\V2\HealthController;
use App\Http\Controllers\Api\V2\MealPlanDurationController;
use App\Http\Controllers\Api\V2\MealTypeController;
use App\Http\Controllers\Api\V2\MealItemController;
use App\Http\Controllers\Api\V2\MealDiscountController;
use App\Http\Controllers\Api\V2\MenuController;
use App\Http\Controllers\Api\V2\MetricsController;
use App\Http\Controllers\Api\V2\PlanMealController;
use App\Http\Controllers\Api\V2\ThemeController;
use App\Http\Controllers\Api\V2\WeeklyPlannerController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Health Check Routes
Route::get('v2/catalogue/health', [HealthController::class, 'check']);
Route::get('v2/catalogue/health/detailed', [HealthController::class, 'check'])
    ->middleware(['auth:sanctum', 'can:admin']);

// Metrics Route
Route::get('v2/catalogue/metrics', [MetricsController::class, 'export'])
    ->middleware(['auth:sanctum', 'can:admin']);

// API V2 Routes
Route::prefix('v2/catalogue')->group(function () {
    // Catalogue Routes
    Route::get('products', [CatalogueController::class, 'index']);
    Route::post('products', [CatalogueController::class, 'store']);
    Route::get('products/{id}', [CatalogueController::class, 'show']);
    Route::put('products/{id}', [CatalogueController::class, 'update']);
    Route::delete('products/{id}', [CatalogueController::class, 'destroy']);
    Route::get('products/search', [CatalogueController::class, 'search']);

    // Menu Routes
    Route::get('menus', [MenuController::class, 'index']);
    Route::post('menus', [MenuController::class, 'store']);
    Route::get('menus/{id}', [MenuController::class, 'show']);
    Route::put('menus/{id}', [MenuController::class, 'update']);
    Route::delete('menus/{id}', [MenuController::class, 'destroy']);
    Route::get('menus/kitchen/{kitchenId}', [MenuController::class, 'getByKitchen']);
    Route::get('menus/type/{type}', [MenuController::class, 'getByType']);

    // Cart Routes
    Route::get('cart', [CartController::class, 'getCart']);
    Route::post('cart/items', [CartController::class, 'addItem']);
    Route::put('cart/items/{id}', [CartController::class, 'updateItem']);
    Route::delete('cart/items/{id}', [CartController::class, 'removeItem']);
    Route::delete('cart', [CartController::class, 'clearCart']);
    Route::post('cart/apply-promo', [CartController::class, 'applyPromoCode']);
    Route::post('cart/checkout', [CartController::class, 'checkout'])->middleware('throttle:60,1');
    Route::post('cart/merge', [CartController::class, 'mergeCart']);

    // Plan Meal Routes
    Route::get('planmeals', [PlanMealController::class, 'index']);
    Route::post('planmeals', [PlanMealController::class, 'store']);
    Route::get('planmeals/{id}', [PlanMealController::class, 'show']);
    Route::put('planmeals/{id}', [PlanMealController::class, 'update']);
    Route::delete('planmeals/{id}', [PlanMealController::class, 'destroy']);
    Route::get('planmeals/customer/{customerId}', [PlanMealController::class, 'getByCustomer']);
    Route::post('planmeals/{id}/items', [PlanMealController::class, 'addItem']);
    Route::put('planmeals/{id}/items/{itemId}', [PlanMealController::class, 'updateItem']);
    Route::delete('planmeals/{id}/items/{itemId}', [PlanMealController::class, 'removeItem']);
    Route::post('planmeals/{id}/apply-promo', [PlanMealController::class, 'applyPromoCode']);
    Route::post('planmeals/{id}/checkout', [PlanMealController::class, 'checkout'])->middleware('throttle:60,1');

    Route::middleware('jwt.auth')->group(function () {
    // Weekly Planner Routes
    Route::get('weekly-planner', [WeeklyPlannerController::class, 'index']);

    // Meal Plan Duration Routes - Direct access to plan_master table
    Route::get('meal-plan-durations', [MealPlanDurationController::class, 'index']);
    Route::get('meal-plan-durations/{id}', [MealPlanDurationController::class, 'show']);
    });

    // Theme Routes
    Route::get('themes', [ThemeController::class, 'index']);
    Route::post('themes', [ThemeController::class, 'store']);
    Route::get('themes/{id}', [ThemeController::class, 'show']);
    Route::put('themes/{id}', [ThemeController::class, 'update']);
    Route::delete('themes/{id}', [ThemeController::class, 'destroy']);
    Route::get('themes/active', [ThemeController::class, 'getActiveTheme']);
    Route::post('themes/{id}/activate', [ThemeController::class, 'setActiveTheme']);
    Route::get('themes/{id}/config', [ThemeController::class, 'getThemeConfig']);
    Route::put('themes/{id}/config', [ThemeController::class, 'updateThemeConfig']);
});

// Meal Types
Route::prefix('v2/catalogue/meal-types')->group(function () {
    Route::get('/', [MealTypeController::class, 'index']);
    Route::post('/', [MealTypeController::class, 'store']);
    Route::get('/{id}', [MealTypeController::class, 'show']);
    Route::put('/{id}', [MealTypeController::class, 'update']);
    Route::delete('/{id}', [MealTypeController::class, 'destroy']);
    Route::get('/active', [MealTypeController::class, 'getActive']);
    Route::get('/slug/{slug}', [MealTypeController::class, 'getBySlug']);
});

// Meal Items
Route::prefix('v2/catalogue/meal-items')->group(function () {
    Route::get('/', [MealItemController::class, 'index']);
    Route::post('/', [MealItemController::class, 'store']);
    Route::get('/{id}', [MealItemController::class, 'show']);
    Route::put('/{id}', [MealItemController::class, 'update']);
    Route::delete('/{id}', [MealItemController::class, 'destroy']);
    Route::get('/featured', [MealItemController::class, 'getFeatured']);
    Route::get('/meal-type/{mealTypeId}', [MealItemController::class, 'getByMealTypeId']);
    Route::get('/meal-type-slug/{slug}', [MealItemController::class, 'getByMealTypeSlug']);
});

// Meal Discounts
Route::prefix('v2/catalogue/meal-discounts')->group(function () {
    Route::get('/', [MealDiscountController::class, 'index']);
    Route::post('/', [MealDiscountController::class, 'store']);
    Route::get('/{id}', [MealDiscountController::class, 'show']);
    Route::put('/{id}', [MealDiscountController::class, 'update']);
    Route::delete('/{id}', [MealDiscountController::class, 'destroy']);
    Route::get('/meal-item/{mealItemId}', [MealDiscountController::class, 'getActiveForMealItem']);
    Route::get('/meal-type/{mealTypeId}', [MealDiscountController::class, 'getActiveForMealType']);
});

// Coupons & Promo Codes - Complete CRUD Management
Route::prefix('v2/catalogue/coupons')->group(function () {
    // Basic CRUD operations
    Route::get('/', [CouponController::class, 'index']);
    Route::post('/', [CouponController::class, 'store']);
    Route::get('/{id}', [CouponController::class, 'show']);
    Route::put('/{id}', [CouponController::class, 'update']);
    Route::delete('/{id}', [CouponController::class, 'destroy']);
    
    // Coupon management operations
    Route::get('/active/customer', [CouponController::class, 'getActiveCoupons']);
    Route::post('/validate', [CouponController::class, 'validateCoupon']);
    Route::post('/apply', [CouponController::class, 'applyCoupon']);
    Route::get('/{id}/usage-stats', [CouponController::class, 'getUsageStats']);
    Route::patch('/{id}/toggle-status', [CouponController::class, 'toggleStatus']);
    Route::post('/bulk-action', [CouponController::class, 'bulkAction']);
});
