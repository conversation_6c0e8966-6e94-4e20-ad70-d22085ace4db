<?php

/**
 * Simple Queue Worker for Development
 * 
 * This script starts a queue worker that can be run in the background
 * for development purposes when supervisor is not available.
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

// Function to log messages
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[{$timestamp}] {$message}" . PHP_EOL;
}

// Function to check if queue worker is already running
function isWorkerRunning() {
    $output = shell_exec('ps aux | grep "queue:work" | grep -v grep');
    return !empty(trim($output));
}

// Function to start queue worker
function startQueueWorker() {
    logMessage("Starting queue worker...");
    
    // Start the worker process
    $command = 'php artisan queue:work --queue=default,notifications --sleep=3 --tries=3 --max-time=3600 --memory=512 --timeout=30';
    
    if (PHP_OS_FAMILY === 'Windows') {
        // Windows
        pclose(popen("start /B {$command}", "r"));
    } else {
        // Unix/Linux/macOS
        shell_exec("{$command} > storage/logs/queue-worker.log 2>&1 &");
    }
    
    logMessage("Queue worker started successfully!");
    logMessage("Check logs: tail -f storage/logs/queue-worker.log");
}

// Main execution
logMessage("Payment Service Queue Worker Manager");
logMessage("====================================");

// Check if worker is already running
if (isWorkerRunning()) {
    logMessage("Queue worker is already running.");
    logMessage("To stop: pkill -f 'php artisan queue:work'");
    exit(0);
}

// Create logs directory if it doesn't exist
if (!is_dir(__DIR__ . '/storage/logs')) {
    mkdir(__DIR__ . '/storage/logs', 0755, true);
}

// Start the worker
startQueueWorker();

logMessage("Queue worker management completed.");
logMessage("Use 'php artisan queue:status' to check queue status.");
