[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:php]
command=php artisan serve --host=0.0.0.0 --port=8000
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/php.log
stderr_logfile=/var/log/supervisor/php-error.log

[program:queue-worker]
command=php artisan queue:work --queue=default --sleep=3 --tries=3 --max-time=3600
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
numprocs=2
stdout_logfile=/var/log/supervisor/queue-worker.log
stderr_logfile=/var/log/supervisor/queue-worker-error.log
stopwaitsecs=3600

[program:queue-worker-notifications]
command=php artisan queue:work --queue=notifications --sleep=3 --tries=3 --max-time=3600
directory=/var/www/html
user=www-data
autostart=true
autorestart=true
numprocs=1
stdout_logfile=/var/log/supervisor/queue-worker-notifications.log
stderr_logfile=/var/log/supervisor/queue-worker-notifications-error.log
stopwaitsecs=3600
