<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class QueueStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'queue:status {--clear-failed : Clear all failed jobs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show queue status and manage failed jobs';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if ($this->option('clear-failed')) {
            return $this->clearFailedJobs();
        }

        $this->showQueueStatus();
        return 0;
    }

    /**
     * Show the current queue status.
     */
    private function showQueueStatus(): void
    {
        $this->info('Payment Service Queue Status');
        $this->line('================================');

        // Show pending jobs
        $pendingJobs = DB::table('jobs')->count();
        $this->line("Pending Jobs: {$pendingJobs}");

        // Show failed jobs
        $failedJobs = DB::table('failed_jobs')->count();
        $this->line("Failed Jobs: {$failedJobs}");

        if ($failedJobs > 0) {
            $this->warn("You have {$failedJobs} failed jobs. Run with --clear-failed to clear them.");
        }

        // Show recent failed jobs
        if ($failedJobs > 0) {
            $this->line('');
            $this->info('Recent Failed Jobs:');
            $recentFailed = DB::table('failed_jobs')
                ->orderBy('failed_at', 'desc')
                ->limit(5)
                ->get(['uuid', 'queue', 'failed_at', 'exception']);

            foreach ($recentFailed as $job) {
                $this->line("- {$job->uuid} ({$job->queue}) - {$job->failed_at}");
                $exception = substr($job->exception, 0, 100) . '...';
                $this->line("  Error: {$exception}");
            }
        }

        $this->line('');
        $this->info('Queue Commands:');
        $this->line('- Start worker: php artisan queue:work');
        $this->line('- Retry failed: php artisan queue:retry all');
        $this->line('- Clear failed: php artisan queue:status --clear-failed');
    }

    /**
     * Clear all failed jobs.
     */
    private function clearFailedJobs(): int
    {
        $count = DB::table('failed_jobs')->count();
        
        if ($count === 0) {
            $this->info('No failed jobs to clear.');
            return 0;
        }

        if ($this->confirm("Are you sure you want to clear {$count} failed jobs?")) {
            DB::table('failed_jobs')->delete();
            $this->info("Cleared {$count} failed jobs.");
        } else {
            $this->info('Operation cancelled.');
        }

        return 0;
    }
}
