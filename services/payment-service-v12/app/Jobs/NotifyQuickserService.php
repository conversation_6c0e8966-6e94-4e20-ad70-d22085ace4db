<?php

namespace App\Jobs;

use App\Models\PaymentTransaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class NotifyQuickserService implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The payment transaction instance.
     *
     * @var \App\Models\PaymentTransaction
     */
    public $transaction;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 30;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = [10, 30, 60];

    /**
     * Create a new job instance.
     *
     * @param  \App\Models\PaymentTransaction  $transaction
     * @return void
     */
    public function __construct(PaymentTransaction $transaction)
    {
        $this->transaction = $transaction;

        // Set the queue for this job
        $this->onQueue('notifications');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            if (!$this->transaction->success_url) {
                return;
            }

            $callbackData = [
                'payment_service_transaction_id' => $this->transaction->gateway_transaction_id,
                'gateway' => $this->transaction->gateway,
                'amount' => $this->transaction->payment_amount,
                'status' => 'completed',
                'transaction_id' => $this->transaction->transaction_id,
                'customer_id' => $this->transaction->customer_id,
                'order_id' => $this->transaction->pre_order_id
            ];

            Log::info('External success URL call data', [
                'transaction_id' => $this->transaction->transaction_id,
                'success_url' => $this->transaction->success_url,
                'callback_data' => $callbackData
            ]);

            $response = Http::timeout(15) // Increased timeout
                ->connectTimeout(5) // Connection timeout
                ->retry(2, 1000) // Retry with 1 second delay
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Service' => 'payment-service-v12',
                    'X-Request-ID' => uniqid('callback_'),
                    'X-Correlation-ID' => $this->transaction->transaction_id,
                ])
                ->post($this->transaction->success_url, $callbackData);

            if ($response->successful()) {
                Log::info('External success URL called successfully', [
                    'transaction_id' => $this->transaction->transaction_id,
                    'success_url' => $this->transaction->success_url,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            } else {
                Log::warning('External success URL call failed', [
                    'transaction_id' => $this->transaction->transaction_id,
                    'success_url' => $this->transaction->success_url,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Failed to call external success URL', [
                'transaction_id' => $this->transaction->transaction_id,
                'success_url' => $this->transaction->success_url,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts()
            ]);

            // If we haven't exceeded max attempts, release the job for retry
            if ($this->attempts() < $this->tries) {
                $delay = $this->backoff[$this->attempts() - 1] ?? 60;
                Log::info('Releasing job for retry', [
                    'transaction_id' => $this->transaction->transaction_id,
                    'attempt' => $this->attempts(),
                    'delay' => $delay
                ]);
                $this->release($delay);
            } else {
                // Max attempts reached, fail the job
                Log::error('Max attempts reached, failing job', [
                    'transaction_id' => $this->transaction->transaction_id,
                    'attempts' => $this->attempts()
                ]);
                $this->fail($e);
            }
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('NotifyQuickserService job failed permanently', [
            'transaction_id' => $this->transaction->transaction_id,
            'success_url' => $this->transaction->success_url,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // You could send an alert, create a manual task, etc.
        // For now, we'll just log it
    }
}
