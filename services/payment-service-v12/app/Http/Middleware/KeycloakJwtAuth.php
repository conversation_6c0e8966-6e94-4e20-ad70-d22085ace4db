<?php

namespace App\Http\Middleware;

use App\Utils\JwtDecoder;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

/**
 * Keycloak JWT Authentication Middleware for Customer Service v12
 * 
 * This middleware validates Keycloak JWT tokens and provides role-based access control
 * without requiring external JWT packages. It extracts user information including
 * Old SSO User ID for backward compatibility.
 */
class KeycloakJwtAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  ...$requiredRoles
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, ...$requiredRoles): Response
    {
        try {
            // Extract JWT token from request
            $token = $this->extractToken($request);
            
            if (!$token) {
                Log::warning('JWT authentication failed: No token provided', [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'url' => $request->fullUrl()
                ]);
                
                return $this->unauthorizedResponse('No authentication token provided');
            }

            // Decode and validate token
            $decodedToken = JwtDecoder::decode($token);
            
            if (!$decodedToken) {
                Log::warning('JWT authentication failed: Invalid token', [
                    'ip' => $request->ip(),
                    'token_preview' => substr($token, 0, 50) . '...',
                    'url' => $request->fullUrl()
                ]);
                
                return $this->unauthorizedResponse('Invalid authentication token');
            }

            // Check role requirements if specified
            if (!empty($requiredRoles)) {
                $hasRequiredRole = $this->checkRoleRequirements($decodedToken['payload'], $requiredRoles);
                
                if (!$hasRequiredRole) {
                    Log::warning('JWT authorization failed: Insufficient permissions', [
                        'user_id' => $decodedToken['user']['user_id'],
                        'username' => $decodedToken['user']['username'],
                        'required_roles' => $requiredRoles,
                        'user_roles' => JwtDecoder::extractRoles($decodedToken['payload']),
                        'url' => $request->fullUrl()
                    ]);
                    
                    return $this->forbiddenResponse('Insufficient permissions. Required roles: ' . implode(', ', $requiredRoles));
                }
            }

            // Add authentication data to request
            $this->enhanceRequest($request, $decodedToken);

            // Log successful authentication
            Log::info('JWT authentication successful', [
                'user_id' => $decodedToken['user']['user_id'],
                'username' => $decodedToken['user']['username'],
                'old_sso_user_id' => $decodedToken['user']['old_sso_user_id'],
                'roles' => JwtDecoder::extractRoles($decodedToken['payload']),
                'expires_in_minutes' => JwtDecoder::getExpirationInfo($decodedToken['payload'])['expires_in_minutes'],
                'url' => $request->fullUrl()
            ]);

            return $next($request);

        } catch (\Exception $e) {
            Log::error('JWT middleware error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'url' => $request->fullUrl()
            ]);
            
            return $this->errorResponse('Authentication service error');
        }
    }

    /**
     * Extract JWT token from request
     *
     * @param Request $request
     * @return string|null
     */
    private function extractToken(Request $request): ?string
    {
        // Check Authorization header (Bearer token)
        $authHeader = $request->header('Authorization');
        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            return substr($authHeader, 7);
        }

        // Check X-Auth-Token header
        $authToken = $request->header('X-Auth-Token');
        if ($authToken) {
            return $authToken;
        }

        // Check query parameter (not recommended for production)
        $queryToken = $request->query('token');
        if ($queryToken) {
            return $queryToken;
        }

        return null;
    }

    /**
     * Check if user has required roles
     *
     * @param array $payload
     * @param array $requiredRoles
     * @return bool
     */
    private function checkRoleRequirements(array $payload, array $requiredRoles): bool
    {
        foreach ($requiredRoles as $role) {
            if (JwtDecoder::hasRole($payload, $role)) {
                return true; // User has at least one required role
            }
        }
        
        return false;
    }

    /**
     * Enhance request with authentication data
     *
     * @param Request $request
     * @param array $decodedToken
     * @return void
     */
    private function enhanceRequest(Request $request, array $decodedToken): void
    {
        // Add authentication data to request attributes
        $request->attributes->set('jwt_token', $decodedToken);
        $request->attributes->set('auth_user', $decodedToken['user']);
        $request->attributes->set('jwt_payload', $decodedToken['payload']);

        // Add convenient methods to request using macros
        if (!Request::hasMacro('getAuthUser')) {
            Request::macro('getAuthUser', function () {
                return $this->attributes->get('auth_user');
            });
        }

        if (!Request::hasMacro('getUserRoles')) {
            Request::macro('getUserRoles', function () {
                $payload = $this->attributes->get('jwt_payload');
                return $payload ? JwtDecoder::extractRoles($payload) : [];
            });
        }

        if (!Request::hasMacro('hasRole')) {
            Request::macro('hasRole', function ($role, $resource = null) {
                $payload = $this->attributes->get('jwt_payload');
                return $payload ? JwtDecoder::hasRole($payload, $role, $resource) : false;
            });
        }

        if (!Request::hasMacro('isAdmin')) {
            Request::macro('isAdmin', function () {
                return $this->hasRole('admin');
            });
        }

        if (!Request::hasMacro('getOldSsoUserId')) {
            Request::macro('getOldSsoUserId', function () {
                $user = $this->attributes->get('auth_user');
                return $user['old_sso_user_id'] ?? null;
            });
        }

        if (!Request::hasMacro('getTokenExpiration')) {
            Request::macro('getTokenExpiration', function () {
                $payload = $this->attributes->get('jwt_payload');
                return $payload ? JwtDecoder::getExpirationInfo($payload) : null;
            });
        }
    }

    /**
     * Return unauthorized response
     *
     * @param string $message
     * @return Response
     */
    private function unauthorizedResponse(string $message): Response
    {
        return response()->json([
            'success' => false,
            'data' => [],
            'message' => $message,
            'error_code' => 'UNAUTHORIZED'
        ], 401);
    }

    /**
     * Return forbidden response
     *
     * @param string $message
     * @return Response
     */
    private function forbiddenResponse(string $message): Response
    {
        return response()->json([
            'success' => false,
            'data' => [],
            'message' => $message,
            'error_code' => 'FORBIDDEN'
        ], 403);
    }

    /**
     * Return server error response
     *
     * @param string $message
     * @return Response
     */
    private function errorResponse(string $message): Response
    {
        return response()->json([
            'success' => false,
            'data' => [],
            'message' => $message,
            'error_code' => 'SERVER_ERROR'
        ], 500);
    }
}
