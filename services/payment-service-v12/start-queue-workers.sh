#!/bin/bash

# Start Queue Workers for Payment Service
# This script starts multiple queue workers for better performance

echo "Starting Payment Service Queue Workers..."

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "Error: PHP is not installed or not in PATH"
    exit 1
fi

# Check if Laravel artisan is available
if [ ! -f "artisan" ]; then
    echo "Error: artisan file not found. Make sure you're in the Laravel project root."
    exit 1
fi

# Function to start a queue worker
start_worker() {
    local queue_name=$1
    local worker_name=$2
    
    echo "Starting $worker_name for queue: $queue_name"
    
    # Start the worker in background
    nohup php artisan queue:work \
        --queue=$queue_name \
        --sleep=3 \
        --tries=3 \
        --max-time=3600 \
        --memory=512 \
        --timeout=30 \
        > storage/logs/queue-$queue_name.log 2>&1 &
    
    echo "Started $worker_name (PID: $!)"
}

# Create logs directory if it doesn't exist
mkdir -p storage/logs

# Start workers for different queues
start_worker "default" "Default Worker"
start_worker "notifications" "Notifications Worker"

# Start additional default workers for load balancing
start_worker "default" "Default Worker 2"

echo ""
echo "Queue workers started successfully!"
echo ""
echo "To monitor the workers:"
echo "  - Check logs: tail -f storage/logs/queue-*.log"
echo "  - Check queue status: php artisan queue:work --help"
echo "  - Check failed jobs: php artisan queue:failed"
echo ""
echo "To stop all workers:"
echo "  - Kill processes: pkill -f 'php artisan queue:work'"
echo ""
