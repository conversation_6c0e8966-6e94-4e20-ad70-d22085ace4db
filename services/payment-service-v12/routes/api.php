<?php

use App\Http\Controllers\Api\PaymentMethodController;
use App\Http\Controllers\Api\V1\PaymentController as PaymentControllerV1;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\V2\HealthController;
use App\Http\Controllers\Api\V2\MetricsController as V2MetricsController;
use App\Http\Controllers\MetricsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health Check Routes
Route::get('v2/payments/health', [HealthController::class, 'check']);
Route::get('v2/payments/health/detailed', [HealthController::class, 'check'])
    ->middleware(['auth:sanctum', 'can:admin']);

// Metrics Route (Legacy)
Route::get('v2/payments/metrics', [V2MetricsController::class, 'export'])
    ->middleware(['auth:sanctum', 'can:admin']);

// OneFoodDialer 2025 Monitoring Routes
Route::prefix('monitoring')->group(function () {
    // Prometheus metrics endpoint
    Route::get('/metrics', [MetricsController::class, 'metrics']);

    // Health checks
    Route::get('/health', [MetricsController::class, 'health']);
    Route::get('/ready', [MetricsController::class, 'ready']);
    Route::get('/live', [MetricsController::class, 'live']);
});

// API v1 Routes
Route::prefix('v1')->group(function () {
    // Payment routes
    Route::prefix('payments')->group(function () {
        // List and show
        Route::get('/', [PaymentControllerV1::class, 'index']);
        Route::get('/{id}', [PaymentControllerV1::class, 'show'])->where('id', '[0-9]+');

        // Transaction routes
        Route::post('/process', [PaymentControllerV1::class, 'process']);
        Route::get('/transaction/{transactionId}/verify', [PaymentControllerV1::class, 'verify']);
        Route::post('/transaction/{transactionId}/refund', [PaymentControllerV1::class, 'refund']);
        Route::post('/transaction/{transactionId}/cancel', [PaymentControllerV1::class, 'cancel']);
        Route::get('/transaction/{transactionId}/status', [PaymentControllerV1::class, 'status']);
        Route::get('/transaction/{transactionId}/details', [PaymentControllerV1::class, 'details']);

        // Form generation
        Route::post('/form', [PaymentControllerV1::class, 'form']);

        // Gateways
        Route::get('/gateways', [PaymentControllerV1::class, 'gateways']);

        // Statistics
        Route::get('/statistics', [PaymentControllerV1::class, 'statistics']);

        // Webhooks
        Route::post('/webhooks/{gateway}', [PaymentControllerV1::class, 'webhook']);
    });
});

// API v2 Routes
Route::prefix('v2')->group(function () {
    // Test PhonePe payment routes (no auth for testing)
    Route::post('payments/test-phonepe', [PaymentController::class, 'testPhonePe']);
    Route::post('payments/test-phonepe-verify/{transactionId}', [PaymentController::class, 'testPhonePeVerify']);
    Route::get('payments/test-phonepe-status/{transactionId}', [PaymentController::class, 'testPhonePeStatus']);

    // Test Razorpay payment routes (no auth for testing)
    Route::post('payments/test-razorpay', [PaymentController::class, 'testRazorpay']);
    Route::post('payments/test-razorpay-verify/{paymentId}', [PaymentController::class, 'testRazorpayVerify']);
    Route::get('payments/test-razorpay-status/{paymentId}', [PaymentController::class, 'testRazorpayStatus']);

    // Complete Razorpay flow test (no auth for testing)
    Route::post('payments/test-razorpay-flow', [PaymentController::class, 'testRazorpayFlow']);

    // Debug gateway registration
    Route::get('payments/debug-gateways', [PaymentController::class, 'debugGateways']);

    // Razorpay webhook endpoint (no auth - Razorpay will call this)
    Route::post('payments/razorpay-webhook', [PaymentController::class, 'razorpayWebhook']);

    // Payment routes
    Route::prefix('payments')->group(function () {
        // Reports and analytics (moved to top to avoid route conflicts)
        Route::get('/statistics', [PaymentController::class, 'statistics'])->withoutMiddleware(['auth:sanctum']);

        // Core payment operations
        Route::get('/', [PaymentController::class, 'index']);
        Route::post('/', [PaymentController::class, 'initiate'])->withoutMiddleware(['auth:sanctum']);
        Route::get('/{id}', [PaymentController::class, 'status'])->where('id', '[A-Za-z0-9]+');
        Route::post('/{id}/process', [PaymentController::class, 'process'])->where('id', '[A-Za-z0-9]+');
        Route::post('/{id}/refund', [PaymentController::class, 'refund'])->where('id', '[A-Za-z0-9]+');
        Route::post('/{id}/cancel', [PaymentController::class, 'cancel'])->where('id', '[A-Za-z0-9]+');
        Route::post('/{id}/verify', [PaymentController::class, 'verify'])->where('id', '[A-Za-z0-9]+');

        // Payment management
        Route::get('/customer/{customerId}', [PaymentController::class, 'getCustomerPayments'])->where('customerId', '[0-9]+');
        Route::get('/order/{orderId}', [PaymentController::class, 'getOrderPayments'])->where('orderId', '[0-9]+');
        Route::post('/retry', [PaymentController::class, 'retryPayment']);
        Route::post('/capture', [PaymentController::class, 'capturePayment']);
        Route::post('/void', [PaymentController::class, 'voidPayment']);

        // Payment gateways
        Route::get('/gateways', [PaymentController::class, 'getGateways']);
        Route::get('/gateways/{gateway}/config', [PaymentController::class, 'getGatewayConfig']);
        Route::post('/gateways/{gateway}/test', [PaymentController::class, 'testGateway']);

        // Payment forms and tokens
        Route::post('/form', [PaymentController::class, 'generateForm']);
        Route::post('/token', [PaymentController::class, 'generateToken']);
        Route::post('/validate-token', [PaymentController::class, 'validateToken']);

        // Wallet operations
        Route::get('/wallet/{customerId}', [PaymentController::class, 'getWalletBalance'])->where('customerId', '[0-9]+');
        Route::post('/wallet/add', [PaymentController::class, 'addToWallet']);
        Route::post('/wallet/deduct', [PaymentController::class, 'deductFromWallet']);
        Route::get('/wallet/{customerId}/transactions', [PaymentController::class, 'getWalletTransactions'])->where('customerId', '[0-9]+');

        // Reports and analytics
        Route::get('/reports/daily', [PaymentController::class, 'getDailyReport']);
        Route::get('/reports/monthly', [PaymentController::class, 'getMonthlyReport']);
        Route::get('/reports/gateway', [PaymentController::class, 'getGatewayReport']);
        Route::get('/reports/failed', [PaymentController::class, 'getFailedPayments']);

        // Logs and audit
        Route::get('/logs', [PaymentController::class, 'logs']);
        Route::get('/{id}/logs', [PaymentController::class, 'transactionLogs'])->where('id', '[A-Za-z0-9]+');
        Route::get('/audit', [PaymentController::class, 'getAuditLog']);

        // Reconciliation
        Route::post('/reconcile', [PaymentController::class, 'reconcilePayments']);
        Route::get('/reconcile/status', [PaymentController::class, 'getReconciliationStatus']);

        // Bulk operations
        Route::post('/bulk/refund', [PaymentController::class, 'bulkRefund']);
        Route::post('/bulk/cancel', [PaymentController::class, 'bulkCancel']);
        Route::get('/bulk/status/{batchId}', [PaymentController::class, 'getBulkOperationStatus']);

        // Webhooks (no auth required)
        Route::post('/webhooks/{gateway}', [PaymentController::class, 'webhook'])->withoutMiddleware(['auth:sanctum']);
        Route::post('/callback', [PaymentController::class, 'callback'])->name('api.payments.callback')->withoutMiddleware(['auth:sanctum']);
    });

    // Payment method routes
    Route::prefix('payment-methods')->group(function () {
        Route::get('/customer/{customerId}', [PaymentMethodController::class, 'getCustomerPaymentMethods'])->where('customerId', '[0-9]+');
        Route::post('/', [PaymentMethodController::class, 'store']);
        Route::get('/{id}', [PaymentMethodController::class, 'show'])->where('id', '[0-9]+');
        Route::put('/{id}', [PaymentMethodController::class, 'update'])->where('id', '[0-9]+');
        Route::delete('/{id}', [PaymentMethodController::class, 'destroy'])->where('id', '[0-9]+');
        Route::put('/{id}/default', [PaymentMethodController::class, 'setDefault'])->where('id', '[0-9]+');
    });
});

// JWT Authentication Examples for Payment Service v12
use App\Http\Controllers\Api\V1\PaymentJwtController;

// Routes requiring JWT authentication (no specific roles)
Route::middleware(['jwt.auth'])->group(function () {
    Route::get('/v1/payment/profile', [PaymentJwtController::class, 'profile']);
    Route::get('/v1/payment/token-info', [PaymentJwtController::class, 'tokenInfo']);
    Route::get('/v1/payment/old-sso-mapping', [PaymentJwtController::class, 'oldSsoMapping']);
    Route::get('/v1/payment/health', [PaymentJwtController::class, 'healthCheck']);
});

// Routes requiring admin role
Route::middleware(['jwt.auth:admin'])->group(function () {
    Route::get('/v1/payment/dashboard', [PaymentJwtController::class, 'dashboard']);
});

// Routes requiring specific roles
Route::middleware(['jwt.auth:manage-payments'])->group(function () {
    Route::get('/v1/payment/manage-payments', [PaymentJwtController::class, 'managePayments']);
});
