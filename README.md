# Laravel Keycloak Integration Project

This project demonstrates the integration of <PERSON><PERSON> with Keycloak for authentication and authorization. It provides a secure and scalable solution for managing user access to your Laravel application.

## About the Project

This Laravel application showcases the implementation of Single Sign-On (SSO) using Keycloak. It includes features such as:

- User authentication via Keycloak
- Role-based access control
- Secure routing with middleware protection
- User dashboard displaying roles and associated companies

## Deployment Steps

Follow these steps to deploy the project:

1. **Clone the Repository**
   ```
   git clone <repository-url>
   cd <project-directory>
   ```

2. **Install Dependencies**
   ```
   composer install
   npm install
   ```

3. **Environment Setup**
   - Copy `.env.example` to `.env`
   - Update the `.env` file with your database and Keycloak configuration

4. **Generate Application Key**
   ```
   php artisan key:generate
   ```

5. **Run Migrations**
   ```
   php artisan migrate
   ```

6. **Configure Keycloak**
   - Set up a new realm in Keycloak
   - Create a new client for this application
   - Configure the client settings (redirect URIs, web origins, etc.)
   - Update the Keycloak configuration in `config/services.php`

7. **Compile Assets**
   ```
   npm run dev
   ```

8. **Start the Application**
   ```
   php artisan serve
   ```

9. **Access the Application**
   Open your browser and navigate to `http://localhost:8000`

## Additional Configuration

- Ensure that your Keycloak server is properly set up and accessible
- Configure the necessary roles and groups in Keycloak
- Adjust the `KeycloakMiddleware` as needed for your specific authorization requirements

## Troubleshooting

If you encounter any issues during deployment or usage:

- Check the Laravel log files in `storage/logs`
- Verify your Keycloak configuration in both the Keycloak admin panel and your Laravel `.env` file
- Ensure all required Keycloak client settings are correctly configured

For more detailed information on Keycloak integration and usage, refer to the official Laravel and Keycloak documentation.
