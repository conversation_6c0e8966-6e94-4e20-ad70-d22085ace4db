<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\{MigrationController, LagoWebhookController};
use Illuminate\Support\Facades\Artisan;

//migration routes
Route::get('/migrate', [MigrationController::class, 'migrate'])->name('migrate');

// call all artisan command to delete all users
Route::get('delete-users', function () {
    Artisan::call('app:delete-keycloak-users');
    return 'Users deleted';
});

Route::get('delete-groups', function () {
    Artisan::call('app:delete-keycloak-groups');
    return 'Users deleted';
});

// call all artisan command to create all clients
Route::get('create-clients', function () {
    Artisan::call('app:migrate-application-to-keycloak');
    return 'Clients created';
});

// call all artisan command to create all client roles
Route::get('create-users/{start_user_id}/{end_user_id}', function ($start_user_id, $end_user_id) {
    Artisan::call('app:migrate-users-to-keycloak', ['start_user_id' => $start_user_id, 'end_user_id' => $end_user_id]);
    return 'Users migration started';
});

// call all artisan command to create all client roles
Route::get('create-companies', function () {
    Artisan::call('app:migrate-company-to-keycloak');
    return 'Companies migration started';
});

// start queue worker
Route::get('start-queue-worker', function () {
    ini_set('max_execution_time', 600);
    Artisan::call('queue:work',['--stop-when-empty' => true, '--timeout' => 600]);
    return 'Queue worker started';
});

// get list of failed jobs
Route::get('failed-jobs', function () {
    $failedJobs = Artisan::call('queue:failed');
    return $failedJobs;
});

// retry failed jobs
Route::get('retry-failed-jobs', function () {
    Artisan::call('queue:retry');
    return 'Failed jobs retried';
});

Route::get('clear-jobs', function () {
    Artisan::call('queue:flush');
    Artisan::call('queue:clear');
    return 'All jobs cleared';
});

Route::get('/', function () {
    return 'SSO Migration';
});

Route::put('update-access', [MigrationController::class, 'updateAccess']);

Route::put('update-user-address', [MigrationController::class, 'createOrUpdateUserAddress']);

Route::post('lago-webhook', [LagoWebhookController::class, 'handleWebhook']);
