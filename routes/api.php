<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\{MigrationController, LagoWebhookController};
use App\Http\Controllers\Api\TrackTiffinsController;
use App\Http\Controllers\Api\V2\ConfigController;
use App\Http\Controllers\Api\V2\RoleController;
use App\Http\Controllers\Api\V2\SetupWizardController;
use App\Http\Controllers\Api\V2\SettingsController;
use Illuminate\Support\Facades\Artisan;

//migration routes
Route::get('/migrate', [MigrationController::class, 'migrate'])->name('migrate');

// call all artisan command to delete all users
Route::get('delete-users', function () {
    Artisan::call('app:delete-keycloak-users');
    return 'Users deleted';
});

Route::get('delete-groups', function () {
    Artisan::call('app:delete-keycloak-groups');
    return 'Users deleted';
});

// call all artisan command to create all clients
Route::get('create-clients', function () {
    Artisan::call('app:migrate-application-to-keycloak');
    return 'Clients created';
});

// call all artisan command to create all client roles
Route::get('create-users/{start_user_id}/{end_user_id}', function ($start_user_id, $end_user_id) {
    Artisan::call('app:migrate-users-to-keycloak', ['start_user_id' => $start_user_id, 'end_user_id' => $end_user_id]);
    return 'Users migration started';
});

// call all artisan command to create all client roles
Route::get('create-companies', function () {
    Artisan::call('app:migrate-company-to-keycloak');
    return 'Companies migration started';
});

// start queue worker
Route::get('start-queue-worker', function () {
    ini_set('max_execution_time', 600);
    Artisan::call('queue:work',['--stop-when-empty' => true, '--timeout' => 600]);
    return 'Queue worker started';
});

// get list of failed jobs
Route::get('failed-jobs', function () {
    $failedJobs = Artisan::call('queue:failed');
    return $failedJobs;
});

// retry failed jobs
Route::get('retry-failed-jobs', function () {
    Artisan::call('queue:retry');
    return 'Failed jobs retried';
});

Route::get('clear-jobs', function () {
    Artisan::call('queue:flush');
    Artisan::call('queue:clear');
    return 'All jobs cleared';
});

Route::get('/', function () {
    return 'SSO Migration';
});

Route::put('update-access', [MigrationController::class, 'updateAccess']);

Route::put('update-user-address', [MigrationController::class, 'createOrUpdateUserAddress']);

Route::post('lago-webhook', [LagoWebhookController::class, 'handleWebhook']);

// Test JWT middleware
Route::get('/test-jwt', function () {
    return response()->json([
        'success' => true,
        'message' => 'JWT middleware test endpoint',
        'timestamp' => now()->toIso8601String(),
    ]);
})->middleware(\App\Http\Middleware\KeycloakJwtAuth::class);

// API v2 routes with JWT authentication
Route::prefix('v2/admin')->middleware(\App\Http\Middleware\KeycloakJwtAuth::class)->group(function () {
    // User information
    Route::get('/user', function (Request $request) {
        $user = $request->user();
        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'roles' => $user->roles->pluck('name'),
                'permissions' => $user->getAllPermissions()->pluck('name'),
            ],
        ]);
    });

    // Health check endpoint
    Route::get('/health', function () {
        return response()->json([
            'status' => 'success',
            'message' => 'Service is healthy',
            'timestamp' => now()->toIso8601String(),
        ]);
    });

    // Health check endpoint with JWT authentication
    Route::get('/health-jwt', function () {
        return response()->json([
            'status' => 'success',
            'message' => 'Service is healthy with JWT authentication',
            'timestamp' => now()->toIso8601String(),
        ]);
    });

    // Settings Routes
    Route::prefix('settings')->group(function () {
        Route::post('/dropdowns', [SettingsController::class, 'getDropdownSettings']);
        Route::get('/dropdowns', [SettingsController::class, 'getDropdownSettings']);
        Route::post('/working-days', [SettingsController::class, 'getWorkingDays']);
        Route::get('/working-days', [SettingsController::class, 'getWorkingDays']);
        Route::post('/calculate-end-date', [SettingsController::class, 'calculateEndDate']);
        Route::get('/payment-gateways', [SettingsController::class, 'getPaymentGateways']);
        Route::get('/images', [SettingsController::class, 'getAllImages']);
    });

    // Configuration Routes
    Route::prefix('config')->group(function () {
        Route::get('/', [ConfigController::class, 'index']);
        Route::get('/{key}', [ConfigController::class, 'show']);
        Route::put('/{key}', [ConfigController::class, 'update']);
        Route::delete('/{key}', [ConfigController::class, 'destroy']);
        Route::get('/group/{group}', [ConfigController::class, 'getSettingsByGroup']);
    });
});
