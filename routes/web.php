<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Middleware\{CheckSubscription, CheckUnlimitedSubscription, KeycloakMiddleware};

// Public routes
Route::view('/', 'welcome')->name('welcome');

// Keycloak Authentication routes
Route::get('/login/keycloak', [AuthController::class, 'redirectToKeycloak'])->name('login.keycloak');
Route::get('/callback', [AuthController::class, 'handleKeycloakCallback'])->name('callback');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Protected routes
Route::middleware([KeycloakMiddleware::class, CheckSubscription::class])->group(function () {
    Route::get('/dashboard', [AuthController::class, 'dashboard'])
        ->name('dashboard')
        ->defaults('required_plan', 'Get Started');

    Route::get('/profile', function () {
        return view('profile');
    })->name('profile')
        ->defaults('required_plan', 'Get Started');

    Route::get('/report', function () {
        return view('report');
    })->name('report')
        ->defaults('required_plan', 'Unlimited');
});

Route::get('/subscription-required', function () {
    return view('subscription-required');
})->name('subscription.required');

Route::get('test', [AuthController::class,'test']);