variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""

stages:
  - deploy

deploy:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk update
    - apk add --no-cache openssh-client
    - chmod 0400 $AWS_PEM_FILE
    - ls -la
  script:
    - chmod 0400 $AWS_PEM_FILE
    - ssh -oStrictHostKeyChecking=no -o "IdentitiesOnly yes" -T -i $AWS_PEM_FILE ubuntu@************** "cd /home/<USER>/sso-migration && git stash && git pull ${CI_REPOSITORY_URL} && chmod +x run_sso.sh && ./run_sso.sh && git stash clear"
  only:
    - stg
