#!/bin/bash

echo "Checking for running containers..."
if sudo docker ps --filter "name=sso-migration" --format "{{.Names}}" | grep -q "sso-migration"; then
    echo "Removing old container..."
    sudo docker stop sso-migration
    sudo docker rm sso-migration
else
    echo "Container 'sso-migration' is not running."
fi

echo "Removing old Image if available..."
sudo docker rmi sso-migration &> /dev/null

echo "Clearing cache..."
sudo docker builder prune -af &> /dev/null

echo "Building Fresh docker image..."
sudo docker build -t sso-migration . &> /dev/null

echo "Starting the container..."
sudo docker run -d --name sso-migration -p 7000:7000 sso-migration &> /dev/null

echo "The application is started on port :7000"
