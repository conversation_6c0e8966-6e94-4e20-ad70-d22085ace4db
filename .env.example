APP_NAME="SSO Migration to Keycloak"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://app2.test:8000   # To check cookies in subdomains, use .test

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=root
DB_PASSWORD=""

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_CONNECTION=mysql
SESSION_COOKIE='onesso_session'
SESSION_PATH=/
SESSION_DOMAIN=null     # .test if created different hosts e.g app1.test, app2.test in /etc/hosts
SESSION_SECURE_COOKIE=false
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE="lax"

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

KEYCLOAK_CLIENT_ID="com_1_app_4"
KEYCLOAK_CLIENT_SECRET=""
KEYCLOAK_BASE_URL="http://localhost:8080"
KEYCLOAK_REALM="FSTech"

KEYCLOAK_REDIRECT_URI="${APP_URL}/callback"
# KEYCLOAK_ADMIN_USERNAME=
# KEYCLOAK_ADMIN_PASSWORD=

DB2_CONNECTION=old_sso
DB2_HOST=127.0.0.1
DB2_PORT=3306
DB2_DATABASE=live_chsone_auth
DB2_USERNAME=root
DB2_PASSWORD=""

DB3_CONNECTION=keycloak_postgres
DB3_HOST=
DB3_PORT=5432
DB3_NAME=
DB3_USERNAME="postgres"
DB3_PASSWORD=

KEYCLOAK_COMPANY_GROUP_PREFIX=C_
KEYCLOAK_CLIENT_PREFIX=APP_
MAX_CONCURRENT_REQUESTS=10

DB_GATE_MASTER_CONNECTION=gate_master
DB_GATE_MASTER_HOST=
DB_GATE_MASTER_PORT=3306
DB_GATE_MASTER_DATABASE=live_vizlog_master
DB_GATE_MASTER_USERNAME=
DB_GATE_MASTER_PASSWORD=

DB_MASTER_HOST=127.0.0.1
DB_MASTER_PORT=3306
DB_MASTER_DATABASE=chsone_master_db
DB_MASTER_USERNAME=
DB_MASTER_PASSWORD=

SUBSCRIPTION_URL=http://127.0.0.1:8003/api
SAAS_BASE_URL=http://127.0.0.1:8000