# JWT Token Analysis

## 🔍 JWT Token Decoded

### Basic Token Information
- **Issuer**: `https://stgsso.cubeone.in/realms/fstech`
- **Subject (User ID)**: `e30f1634-1685-46bc-a2c9-183d89459351`
- **Client ID**: `sso_migration`
- **Username**: `service-account-sso_migration`
- **Email Verified**: `false`
- **Token Type**: `Bearer`
- **Issued At**: `2025-01-05 11:08:41` (timestamp: **********)
- **Expires At**: `2025-01-05 12:08:41` (timestamp: **********)
- **Is Expired**: ❌ NO (if current time < expiry)

### 🏛️ Realm Access Roles
- `default-roles-fstech`
- `offline_access`
- `uma_authorization`

### 🔐 Resource Access Analysis

#### 📦 Resource: `realm-management`
**Roles (18 total):**
- 🔴 `realm-admin` (ADMIN)
- 🔴 `manage-identity-providers` (ADMIN)
- 🔴 `manage-users` (ADMIN)
- 🔴 `manage-events` (ADMIN)
- 🔴 `manage-realm` (ADMIN)
- 🔴 `manage-authorization` (ADMIN)
- 🔴 `manage-clients` (ADMIN)
- 🔴 `create-client` (ADMIN)
- 🟢 `view-realm` (VIEW)
- 🟢 `view-identity-providers` (VIEW)
- 🟢 `view-authorization` (VIEW)
- 🟢 `view-events` (VIEW)
- 🟢 `view-users` (VIEW)
- 🟢 `view-clients` (VIEW)
- 🟢 `query-realms` (QUERY)
- 🟢 `query-clients` (QUERY)
- 🟢 `query-users` (QUERY)
- 🟢 `query-groups` (QUERY)
- 🔴 `impersonation` (ADMIN)

#### 📦 Resource: `broker`
**Roles (1 total):**
- 🟢 `read-token` (VIEW)

#### 📦 Resource: `account`
**Roles (8 total):**
- 🔴 `manage-account` (ADMIN)
- 🔴 `manage-account-links` (ADMIN)
- 🔴 `manage-consent` (ADMIN)
- 🔴 `delete-account` (ADMIN)
- 🟢 `view-applications` (VIEW)
- 🟢 `view-consent` (VIEW)
- 🟢 `view-groups` (VIEW)
- 🟢 `view-profile` (VIEW)

## 🚨 Permission Analysis

### 🔴 Admin Permissions (12 total):
- `realm-management:realm-admin`
- `realm-management:manage-identity-providers`
- `realm-management:manage-users`
- `realm-management:manage-events`
- `realm-management:manage-realm`
- `realm-management:manage-authorization`
- `realm-management:manage-clients`
- `realm-management:create-client`
- `realm-management:impersonation`
- `account:manage-account`
- `account:manage-account-links`
- `account:manage-consent`
- `account:delete-account`

### 🟢 View/Query Permissions (11 total):
- `realm-management:view-realm`
- `realm-management:view-identity-providers`
- `realm-management:view-authorization`
- `realm-management:view-events`
- `realm-management:view-users`
- `realm-management:view-clients`
- `realm-management:query-realms`
- `realm-management:query-clients`
- `realm-management:query-users`
- `realm-management:query-groups`
- `broker:read-token`
- `account:view-applications`
- `account:view-consent`
- `account:view-groups`
- `account:view-profile`

## 🎯 Key Capabilities

- **Realm Admin**: ✅ YES
- **User Management**: ✅ YES
- **Client Management**: ✅ YES
- **Service Account**: ✅ YES

## 📊 Summary

- **Total Resources**: 3 (realm-management, broker, account)
- **Total Admin Permissions**: 12
- **Total View Permissions**: 11
- **Service Account**: ✅ YES (service-account-sso_migration)

## 🔍 What This Token Can Do

### ✅ **ALLOWED OPERATIONS:**

1. **Full Realm Administration**:
   - Create, update, delete realms
   - Manage realm settings and configurations
   - View all realm information

2. **Complete User Management**:
   - Create, update, delete users
   - Manage user attributes, roles, and groups
   - Query and search users
   - Impersonate users

3. **Full Client Management**:
   - Create, update, delete clients
   - Manage client configurations
   - View client details and settings

4. **Identity Provider Management**:
   - Configure and manage identity providers
   - View identity provider settings

5. **Authorization Management**:
   - Manage authorization policies
   - Configure permissions and scopes

6. **Event Management**:
   - View and manage Keycloak events
   - Access audit logs

7. **Account Management**:
   - Manage user accounts
   - Handle account linking
   - Manage user consent

### 🚫 **LIMITATIONS:**

- This is a **service account** token, not a regular user token
- Email is not verified (typical for service accounts)
- Limited to the `fstech` realm scope

## 🔧 Usage in Laravel Application

This token has **FULL ADMINISTRATIVE ACCESS** to Keycloak, which means your Laravel application can:

1. ✅ Create and manage users (perfect for `createOrUpdateUserAddress`)
2. ✅ Update user attributes (addresses, GSTIN, etc.)
3. ✅ Manage user roles and permissions
4. ✅ Create and configure clients
5. ✅ Perform all migration operations

## ⚠️ Security Considerations

- This token has **REALM ADMIN** privileges - handle with extreme care
- Store securely and never expose in client-side code
- Consider token rotation and expiry management
- Use HTTPS only for all API calls
- Implement proper logging and monitoring

## 🛠️ API Endpoints to Test

You can test the JWT decoder using these endpoints:

```bash
# Test the provided JWT
GET /api/test-jwt-decode

# Decode any JWT
POST /api/decode-jwt
{
  "jwt_token": "your_jwt_token_here"
}
```
