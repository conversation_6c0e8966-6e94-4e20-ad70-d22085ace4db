# User Address and GSTIN Management API

## Overview
This API endpoint allows you to create or update user addresses and GSTIN information in Keycloak user attributes. The API performs **type-wise updates**, meaning it only updates the address types provided in the request while preserving all other existing addresses.

## Endpoint
```
PUT /api/update-user-address
```

## Request Format

### Headers
```
Content-Type: application/json
```

### Request Body
```json
{
    "user_id": "keycloak-user-id",
    "addresses": [
        {
            "type": "home",
            "method": "update",
            "line1": "123 Home Street",
            "line2": "Apartment 4B",
            "landmark": "Near Central Park",
            "city": "Mumbai",
            "state": "MH",
            "country": "IND",
            "pincode": "400001",
            "default": true
        },
        {
            "type": "work",
            "method": "delete",
            "line1": "456 Work Avenue",
            "city": "Delhi",
            "state": "DL",
            "pincode": "110001",
            "default": false
        }
    ],
    "gstin": "27AAPFU0939F1ZV"
}
```

## Field Descriptions

### Required Fields
- `user_id`: Keycloak user ID (string)
- `addresses`: Array of address objects (minimum 1 address required)
- `addresses[].type`: Address type (string, max 50 chars)

### Required Fields for Update Operations
- `addresses[].line1`: Primary address line (string, max 255 chars)
- `addresses[].city`: City name (string, max 100 chars)
- `addresses[].state`: State name (string, max 100 chars)
- `addresses[].pincode`: 6-digit Indian pincode (string, format: first digit 1-9, remaining 5 digits 0-9)
- `addresses[].default`: Whether this is the default address (boolean)

### Optional Fields
- `addresses[].method`: Operation method - "update" or "delete" (string, defaults to "update")
- `addresses[].line2`: Secondary address line (string, max 255 chars)
- `addresses[].landmark`: Landmark reference (string, max 255 chars)
- `addresses[].country`: Country code (string, max 100 chars)
- `gstin`: Indian GST number (string, 15 characters following GST format)

## How Type-wise Updates Work

### Update Behavior
- **Partial Updates**: Only the address types provided in the request are updated
- **Preservation**: Existing addresses with types not in the request remain unchanged
- **Case Insensitive**: Address types are converted to lowercase for matching (e.g., 'HOME', 'Home', 'home' all match)
- **Default Management**: When a new default address is set, all other addresses automatically become non-default

### Examples
- If user has addresses: `home`, `work`, `billing`
- Request with only `home` type → Updates `home`, keeps `work` and `billing` unchanged
- Request with `work` and `shipping` → Updates `work`, adds `shipping`, keeps `home` and `billing` unchanged

## Validation Rules

### Address Validation
1. **Unique Types**: Each address in a single request must have a unique type. For multiple addresses of the same type, use suffixes like `home1`, `home2`, `work1`, `work2`, etc.
2. **Single Default**: Exactly one address in the request must be marked as `default: true`
3. **Pincode Format**: Must be 6 digits with first digit from 1-9
4. **Required Fields**: `type`, `line1`, `city`, `state`, `pincode`, and `default` are mandatory
5. **Case Insensitive Types**: Address types are automatically converted to lowercase

### GSTIN Validation
- Format: `NNLLLLLNNNNLNLNL` where:
  - N = Number (0-9)
  - L = Letter (A-Z)
- Example: `27AAPFU0939F1ZV`
- Pattern: `^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$`

## Response Format

### Success Response (200)
```json
{
    "message": "User address and GSTIN updated successfully"
}
```

### Validation Error Response (400)
```json
{
    "message": "Validation failed",
    "errors": {
        "addresses.0.pincode": ["The addresses.0.pincode format is invalid."],
        "gstin": ["The gstin format is invalid."]
    }
}
```

### Custom Validation Error Response (400)
```json
{
    "message": "Address types must be unique. Use type1, type2 for multiple addresses of same type."
}
```

### User Not Found Response (404)
```json
{
    "message": "User not found"
}
```

## Type-wise Update Examples

### Scenario 1: Initial Setup
```json
{
    "user_id": "abc123-def456-ghi789",
    "addresses": [
        {
            "type": "home",
            "line1": "123 Home Street",
            "city": "Mumbai",
            "state": "MH",
            "pincode": "400001",
            "default": true
        },
        {
            "type": "work",
            "line1": "456 Work Avenue",
            "city": "Delhi",
            "state": "DL",
            "pincode": "110001",
            "default": false
        }
    ]
}
```
**Result**: User has 2 addresses (home, work)

### Scenario 2: Update Only Home Address
```json
{
    "user_id": "abc123-def456-ghi789",
    "addresses": [
        {
            "type": "HOME", // Case insensitive
            "line1": "789 New Home Street",
            "city": "Pune",
            "state": "MH",
            "pincode": "411001",
            "default": true
        }
    ]
}
```
**Result**: User has 2 addresses (updated home, original work unchanged)

### Scenario 3: Add New Address Type
```json
{
    "user_id": "abc123-def456-ghi789",
    "addresses": [
        {
            "type": "billing",
            "line1": "321 Billing Address",
            "city": "Bangalore",
            "state": "KA",
            "pincode": "560001",
            "default": false
        }
    ]
}
```
**Result**: User has 3 addresses (original home, original work, new billing)

## Example Usage

### cURL Example
```bash
curl -X PUT http://your-domain.com/api/update-user-address \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "abc123-def456-ghi789",
    "addresses": [
      {
        "type": "home",
        "line1": "123 Main Street",
        "line2": "Apt 2B",
        "landmark": "Near Shopping Mall",
        "city": "Mumbai",
        "state": "Maharashtra",
        "pincode": "400001",
        "default": true
      },
      {
        "type": "office",
        "line1": "456 Business Park",
        "city": "Pune",
        "state": "Maharashtra", 
        "pincode": "411001",
        "default": false
      }
    ],
    "gstin": "27AAPFU0939F1ZV"
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/api/update-user-address', {
    method: 'PUT',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        user_id: 'abc123-def456-ghi789',
        addresses: [
            {
                type: 'home',
                line1: '123 Main Street',
                city: 'Mumbai',
                state: 'MH',
                pincode: '400001',
                default: true
            }
        ],
        gstin: '27AAPFU0939F1ZV'
    })
});

const result = await response.json();
console.log(result);
```

## Data Storage
- Addresses are stored as JSON string in Keycloak user attribute `addresses`
- GSTIN is stored as string in Keycloak user attribute `gstin`
- Existing user attributes (groupAccess, mobile, etc.) are preserved

## Notes
- **Type-wise Updates**: Only updates the address types provided in the request, preserves all others
- **Case Insensitive**: Address types are automatically converted to lowercase for consistency
- **Attribute Preservation**: All existing user attributes (groupAccess, mobile, etc.) are preserved
- **Address Types**: Should be descriptive (e.g., 'home', 'work', 'billing', 'shipping')
- **Multiple Same Types**: For multiple addresses of the same type, use numbered suffixes (e.g., 'home1', 'home2')
- **GSTIN Validation**: Follows the official Indian GST number format
- **Execution Timeout**: 600-second timeout for handling large requests
- **Default Management**: When setting a new default address, all other addresses automatically become non-default
