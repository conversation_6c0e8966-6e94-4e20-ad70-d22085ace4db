<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class UserAddressTest extends TestCase
{
    /**
     * Test successful user address and GSTIN update
     */
    public function test_successful_user_address_update()
    {
        // Mock Keycloak API responses
        Http::fake([
            '*/admin/realms/*/users/*' => Http::response([
                'id' => 'test-user-id',
                'username' => 'testuser',
                'email' => '<EMAIL>',
                'firstName' => 'Test',
                'lastName' => 'User',
                'attributes' => [
                    'mobile' => ['9876543210'],
                    'groupAccess' => ['{"c_1":{"app1":"admin"}}']
                ]
            ], 200),
            '*/admin/realms/*/protocol/openid-connect/token' => Http::response([
                'access_token' => 'mock-token',
                'expires_in' => 3600
            ], 200)
        ]);

        // Mock the PUT request for updating user
        Http::fake([
            '*/admin/realms/*/users/*' => Http::response('', 204)
        ]);

        $requestData = [
            'user_id' => 'test-user-id',
            'addresses' => [
                [
                    'type' => 'home',
                    'line1' => '123 Home St',
                    'line2' => 'Apt 4B',
                    'landmark' => 'Near Park',
                    'city' => 'Mumbai',
                    'state' => 'MH',
                    'pincode' => '400001',
                    'default' => true
                ],
                [
                    'type' => 'work',
                    'line1' => '456 Work Ave',
                    'city' => 'Delhi',
                    'state' => 'DL',
                    'pincode' => '110001',
                    'default' => false
                ]
            ],
            'gstin' => '27AAPFU0939F1ZV'
        ];

        $response = $this->putJson('/api/update-user-address', $requestData);

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'User address and GSTIN updated successfully'
                ]);
    }

    /**
     * Test validation errors for invalid data
     */
    public function test_validation_errors()
    {
        $requestData = [
            'user_id' => 'test-user-id',
            'addresses' => [
                [
                    'type' => 'home',
                    'line1' => '', // Empty required field
                    'city' => 'Mumbai',
                    'state' => 'MH',
                    'pincode' => '40000', // Invalid pincode
                    'default' => true
                ]
            ],
            'gstin' => 'INVALID-GSTIN' // Invalid GSTIN format
        ];

        $response = $this->putJson('/api/update-user-address', $requestData);

        $response->assertStatus(400)
                ->assertJsonStructure([
                    'message',
                    'errors'
                ]);
    }

    /**
     * Test duplicate address types validation
     */
    public function test_duplicate_address_types_validation()
    {
        $requestData = [
            'user_id' => 'test-user-id',
            'addresses' => [
                [
                    'type' => 'home',
                    'line1' => '123 Home St',
                    'city' => 'Mumbai',
                    'state' => 'MH',
                    'pincode' => '400001',
                    'default' => true
                ],
                [
                    'type' => 'home', // Duplicate type
                    'line1' => '456 Another Home',
                    'city' => 'Delhi',
                    'state' => 'DL',
                    'pincode' => '110001',
                    'default' => false
                ]
            ]
        ];

        $response = $this->putJson('/api/update-user-address', $requestData);

        $response->assertStatus(400)
                ->assertJson([
                    'message' => 'Address types must be unique. Use type1, type2 for multiple addresses of same type.'
                ]);
    }

    /**
     * Test multiple default addresses validation
     */
    public function test_multiple_default_addresses_validation()
    {
        $requestData = [
            'user_id' => 'test-user-id',
            'addresses' => [
                [
                    'type' => 'home',
                    'line1' => '123 Home St',
                    'city' => 'Mumbai',
                    'state' => 'MH',
                    'pincode' => '400001',
                    'default' => true
                ],
                [
                    'type' => 'work',
                    'line1' => '456 Work Ave',
                    'city' => 'Delhi',
                    'state' => 'DL',
                    'pincode' => '110001',
                    'default' => true // Both marked as default
                ]
            ]
        ];

        $response = $this->putJson('/api/update-user-address', $requestData);

        $response->assertStatus(400)
                ->assertJson([
                    'message' => 'Exactly one address must be marked as default.'
                ]);
    }

    /**
     * Test valid GSTIN formats
     */
    public function test_valid_gstin_formats()
    {
        $validGstins = [
            '27AAPFU0939F1ZV',
            '09AABCU9603R1ZM',
            '33GSPTN4321G1Z5'
        ];

        foreach ($validGstins as $gstin) {
            $requestData = [
                'user_id' => 'test-user-id',
                'addresses' => [
                    [
                        'type' => 'home',
                        'line1' => '123 Test St',
                        'city' => 'Mumbai',
                        'state' => 'MH',
                        'pincode' => '400001',
                        'default' => true
                    ]
                ],
                'gstin' => $gstin
            ];

            // Mock successful responses for each test
            Http::fake([
                '*/admin/realms/*/users/*' => Http::sequence()
                    ->push(['id' => 'test-user-id', 'username' => 'testuser'], 200)
                    ->push('', 204),
                '*/admin/realms/*/protocol/openid-connect/token' => Http::response([
                    'access_token' => 'mock-token',
                    'expires_in' => 3600
                ], 200)
            ]);

            $response = $this->putJson('/api/update-user-address', $requestData);

            // Should not fail validation for valid GSTIN
            $this->assertNotEquals(400, $response->getStatusCode(), "GSTIN {$gstin} should be valid");
        }
    }

    /**
     * Test type-wise address updates (case insensitive)
     */
    public function test_type_wise_address_updates()
    {
        // Mock user with existing addresses
        Http::fake([
            '*/admin/realms/*/users/*' => Http::sequence()
                ->push([
                    'id' => 'test-user-id',
                    'username' => 'testuser',
                    'email' => '<EMAIL>',
                    'attributes' => [
                        'addresses' => ['[{"type":"home","line1":"Old Home","city":"Mumbai","state":"MH","pincode":"400001","default":true},{"type":"work","line1":"Old Work","city":"Delhi","state":"DL","pincode":"110001","default":false}]']
                    ]
                ], 200)
                ->push('', 204),
            '*/admin/realms/*/protocol/openid-connect/token' => Http::response([
                'access_token' => 'mock-token',
                'expires_in' => 3600
            ], 200)
        ]);

        // Update only HOME address (case insensitive)
        $requestData = [
            'user_id' => 'test-user-id',
            'addresses' => [
                [
                    'type' => 'HOME', // Uppercase should work
                    'line1' => 'New Home Address',
                    'city' => 'Pune',
                    'state' => 'MH',
                    'pincode' => '411001',
                    'default' => true
                ]
            ]
        ];

        $response = $this->putJson('/api/update-user-address', $requestData);

        $response->assertStatus(200)
                ->assertJson([
                    'message' => 'User address and GSTIN updated successfully'
                ]);
    }

    /**
     * Test case insensitive type validation
     */
    public function test_case_insensitive_type_validation()
    {
        $requestData = [
            'user_id' => 'test-user-id',
            'addresses' => [
                [
                    'type' => 'HOME',
                    'line1' => '123 Home St',
                    'city' => 'Mumbai',
                    'state' => 'MH',
                    'pincode' => '400001',
                    'default' => true
                ],
                [
                    'type' => 'home', // Same type in different case
                    'line1' => '456 Another Home',
                    'city' => 'Delhi',
                    'state' => 'DL',
                    'pincode' => '110001',
                    'default' => false
                ]
            ]
        ];

        $response = $this->putJson('/api/update-user-address', $requestData);

        $response->assertStatus(400)
                ->assertJson([
                    'message' => 'Address types must be unique. Use type1, type2 for multiple addresses of same type.'
                ]);
    }
}
