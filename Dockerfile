# Use the official PHP 8.3 FPM image
FROM php:8.3-fpm-alpine3.18

# Install system dependencies
RUN apk add --no-cache \
    curl \
    curl-dev \
    linux-headers \
    libxml2-dev \
    libzip-dev \
    oniguruma-dev \
    libpng-dev \
    libjpeg-turbo-dev \
    libwebp-dev \
    icu-dev \
    libsodium-dev \
    autoconf \
    make \
    gcc \
    g++ \
    git \
    zip \
    unzip

# Install PHP extensions
RUN docker-php-ext-install mbstring soap sockets zip curl pdo_mysql pdo sodium xml 

# Install Composer globally
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Copy existing application directory contents
COPY . .

# Install Laravel dependencies
RUN composer install --optimize-autoloader --no-dev

# Set the correct permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage

RUN php artisan key:generate
RUN php artisan migrate

# Expose port 7000 (optional, only if needed)
EXPOSE 7000

# Start PHP-FPM service
CMD php artisan serve --host 0.0.0.0 --port 7000